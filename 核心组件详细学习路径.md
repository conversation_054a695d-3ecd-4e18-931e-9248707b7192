# OGL核心组件详细学习路径

## 学习前准备

### 必备知识
- JavaScript ES6+ 语法
- 基础的3D数学概念（向量、矩阵）
- HTML5 Canvas基础
- WebGL基础概念（可选，但有助于理解）

### 开发环境
- 现代浏览器（支持WebGL）
- 代码编辑器
- 本地服务器（用于运行示例）

## 详细学习路径

### 阶段1：数学基础 (深入理解)

#### 1.1 向量运算 (Vec2, Vec3, Vec4)
**学习顺序：**
```
src/math/Vec2.js → src/math/Vec3.js → src/math/Vec4.js
```

**重点概念：**
- 向量的创建和初始化
- 基础运算：加法、减法、乘法、除法
- 向量长度和归一化
- 点积和叉积的应用
- 向量插值（lerp）

**实践练习：**
```javascript
// 创建简单的向量运算示例
import { Vec3 } from './src/math/Vec3.js';

const a = new Vec3(1, 2, 3);
const b = new Vec3(4, 5, 6);
const result = new Vec3();

// 练习各种运算
result.add(a, b);
result.cross(a, b);
console.log('向量长度:', a.len());
```

#### 1.2 矩阵变换 (Mat3, Mat4)
**学习顺序：**
```
src/math/Mat3.js → src/math/Mat4.js
```

**重点概念：**
- 矩阵的创建和初始化
- 单位矩阵、零矩阵
- 矩阵乘法和逆矩阵
- 变换矩阵：平移、旋转、缩放
- 投影矩阵：透视投影、正交投影

**实践练习：**
```javascript
import { Mat4, Vec3 } from './src/math/';

const transform = new Mat4();
const position = new Vec3(1, 2, 3);
const rotation = new Vec3(0, Math.PI/4, 0);
const scale = new Vec3(2, 2, 2);

// 构建变换矩阵
transform.compose(position, rotation, scale);
```

#### 1.3 旋转表示 (Euler, Quat)
**学习顺序：**
```
src/math/Euler.js → src/math/Quat.js
```

**重点概念：**
- 欧拉角的优缺点
- 四元数的优势
- 旋转插值（slerp）
- 万向锁问题

### 阶段2：渲染基础

#### 2.1 渲染器 - 系统入口
**文件：** `src/core/Renderer.js`

**学习重点：**
- WebGL上下文创建
- 画布大小设置
- 渲染状态管理
- 清除缓冲区

**关键方法理解：**
```javascript
// 渲染器创建和配置
const renderer = new Renderer({
    width: 800,
    height: 600,
    dpr: window.devicePixelRatio
});

// 渲染循环
function render() {
    renderer.render({ scene, camera });
    requestAnimationFrame(render);
}
```

#### 2.2 几何体 - 数据容器
**文件：** `src/core/Geometry.js`

**学习重点：**
- 顶点属性定义
- 缓冲区创建和绑定
- 索引缓冲区
- VAO（顶点数组对象）

**实践示例：**
```javascript
// 创建三角形几何体
const geometry = new Geometry(gl, {
    position: { 
        size: 3, 
        data: new Float32Array([
            -1, -1, 0,
             1, -1, 0,
             0,  1, 0
        ])
    },
    uv: { 
        size: 2, 
        data: new Float32Array([
            0, 0,
            1, 0,
            0.5, 1
        ])
    }
});
```

#### 2.3 着色器程序 - GPU程序
**文件：** `src/core/Program.js` (您已经熟悉)

**学习重点：**
- 顶点着色器和片元着色器
- 统一变量传递
- 属性绑定
- 渲染状态设置

#### 2.4 纹理 - 图像数据
**文件：** `src/core/Texture.js`

**学习重点：**
- 纹理创建和配置
- 纹理参数设置
- 多重纹理
- 纹理格式

**实践示例：**
```javascript
// 创建纹理
const texture = new Texture(gl, {
    width: 256,
    height: 256,
    format: gl.RGBA,
    type: gl.UNSIGNED_BYTE
});

// 从图像创建纹理
const img = new Image();
img.onload = () => {
    texture.image = img;
};
img.src = 'path/to/image.jpg';
```

### 阶段3：场景管理

#### 3.1 变换 - 3D变换和场景图
**文件：** `src/core/Transform.js`

**学习重点：**
- 本地坐标系和世界坐标系
- 父子关系管理
- 变换矩阵计算
- 场景图遍历

**实践示例：**
```javascript
// 创建场景图
const scene = new Transform();
const parent = new Transform();
const child = new Transform();

parent.setParent(scene);
child.setParent(parent);

// 设置变换
parent.position.set(1, 0, 0);
child.rotation.y = Math.PI / 4;
```

#### 3.2 网格 - 渲染对象
**文件：** `src/core/Mesh.js`

**学习重点：**
- 几何体和程序的组合
- 渲染参数设置
- 绘制模式
- 实例化渲染

#### 3.3 相机 - 观察视角
**文件：** `src/core/Camera.js`

**学习重点：**
- 透视投影和正交投影
- 视图矩阵计算
- 视锥体参数
- 相机控制

**实践示例：**
```javascript
const camera = new Camera(gl);
camera.position.set(0, 0, 5);

// 设置透视投影
camera.perspective({
    fov: 45,
    aspect: width / height,
    near: 0.1,
    far: 100
});
```

### 阶段4：高级渲染

#### 4.1 渲染目标 - 离屏渲染
**文件：** `src/core/RenderTarget.js`

**学习重点：**
- 帧缓冲对象(FBO)
- 多重渲染目标(MRT)
- 深度缓冲和模板缓冲
- 后期处理基础

## 实践项目建议

### 项目1：基础渲染器 (完成阶段1-2后)
创建一个简单的3D场景：
- 渲染一个旋转的立方体
- 应用纹理
- 添加基础光照

### 项目2：场景管理器 (完成阶段3后)
创建复杂场景：
- 多个对象的父子关系
- 相机控制
- 基础交互

### 项目3：特效渲染器 (完成阶段4后)
实现高级效果：
- 阴影映射
- 后期处理
- 粒子系统

## 调试技巧

### 1. WebGL调试
```javascript
// 启用WebGL错误检查
function checkGLError(gl, operation) {
    const error = gl.getError();
    if (error !== gl.NO_ERROR) {
        console.error(`WebGL错误 ${operation}: ${error}`);
    }
}
```

### 2. 矩阵调试
```javascript
// 打印矩阵内容
function printMatrix(mat) {
    console.table([
        [mat[0], mat[4], mat[8], mat[12]],
        [mat[1], mat[5], mat[9], mat[13]],
        [mat[2], mat[6], mat[10], mat[14]],
        [mat[3], mat[7], mat[11], mat[15]]
    ]);
}
```

### 3. 着色器调试
```javascript
// 简单的颜色输出着色器
const debugFragment = `
    precision highp float;
    uniform vec3 uDebugColor;
    void main() {
        gl_FragColor = vec4(uDebugColor, 1.0);
    }
`;
```

## 常见问题和解决方案

### 1. 黑屏问题
- 检查着色器编译错误
- 验证几何体数据
- 确认相机位置和方向

### 2. 性能问题
- 减少绘制调用
- 优化着色器
- 使用适当的纹理大小

### 3. 数学计算错误
- 注意矩阵乘法顺序
- 检查坐标系转换
- 验证向量归一化

## 学习检查点

完成每个阶段后，您应该能够：

**阶段1完成：** 理解3D数学基础，能够进行向量和矩阵运算
**阶段2完成：** 创建基础的WebGL渲染管线
**阶段3完成：** 管理复杂的3D场景
**阶段4完成：** 实现高级渲染效果

每个检查点都建议创建一个小项目来验证理解程度。
