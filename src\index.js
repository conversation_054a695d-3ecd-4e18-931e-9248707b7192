/**
 * @file OGL库入口文件
 *
 * 该文件导出了OGL库的所有组件，包括核心组件、数学工具和额外功能。
 * OGL是一个轻量级的WebGL框架，专注于性能和易用性。
 */

// 核心组件 - 基础渲染功能
export { Geometry } from './core/Geometry.js'; // 几何体类 - 管理顶点数据
export { Program } from './core/Program.js'; // 着色器程序类 - 管理WebGL着色器
export { Renderer } from './core/Renderer.js'; // 渲染器类 - 管理WebGL上下文和渲染
export { Camera } from './core/Camera.js'; // 相机类 - 定义观察视角
export { Transform } from './core/Transform.js'; // 变换类 - 处理3D变换和场景图
export { Mesh } from './core/Mesh.js'; // 网格类 - 组合几何体和材质
export { Texture } from './core/Texture.js'; // 纹理类 - 管理图像纹理
export { RenderTarget } from './core/RenderTarget.js'; // 渲染目标类 - 离屏渲染

// 数学工具 - 3D数学运算
export { Color } from './math/Color.js'; // 颜色类 - 颜色表示和操作
export { Euler } from './math/Euler.js'; // 欧拉角类 - 旋转表示
export { Mat3 } from './math/Mat3.js'; // 3x3矩阵类
export { Mat4 } from './math/Mat4.js'; // 4x4矩阵类
export { Quat } from './math/Quat.js'; // 四元数类 - 旋转表示
export { Vec2 } from './math/Vec2.js'; // 二维向量类
export { Vec3 } from './math/Vec3.js'; // 三维向量类
export { Vec4 } from './math/Vec4.js'; // 四维向量类

// 额外功能 - 扩展组件和工具
// 基础几何体
export { Plane } from './extras/Plane.js'; // 平面几何体
export { Box } from './extras/Box.js'; // 立方体几何体
export { Sphere } from './extras/Sphere.js'; // 球体几何体
export { Cylinder } from './extras/Cylinder.js'; // 圆柱体几何体
export { Triangle } from './extras/Triangle.js'; // 三角形几何体
export { Torus } from './extras/Torus.js'; // 圆环几何体

// 交互和工具
export { Orbit } from './extras/Orbit.js'; // 轨道控制器
export { Raycast } from './extras/Raycast.js'; // 射线投射
export { Curve } from './extras/Curve.js'; // 曲线
export { Path } from './extras/path/Path.js'; // 路径
export { Tube } from './extras/Tube.js'; // 管道几何体

// 后期处理和特效
export { Post } from './extras/Post.js'; // 后期处理
export { Skin } from './extras/Skin.js'; // 蒙皮
export { Animation } from './extras/Animation.js'; // 动画系统
export { Text } from './extras/Text.js'; // 文本渲染
export { NormalProgram } from './extras/NormalProgram.js'; // 法线可视化程序
export { Flowmap } from './extras/Flowmap.js'; // 流动图
export { GPGPU } from './extras/GPGPU.js'; // GPU通用计算
export { Polyline } from './extras/Polyline.js'; // 多段线
export { Shadow } from './extras/Shadow.js'; // 阴影

// 纹理和加载器
export { KTXTexture } from './extras/KTXTexture.js'; // KTX纹理格式支持
export { TextureLoader } from './extras/TextureLoader.js'; // 纹理加载器
export { GLTFLoader } from './extras/GLTFLoader.js'; // GLTF模型加载器
export { GLTFSkin } from './extras/GLTFSkin.js'; // GLTF蒙皮
export { GLTFAnimation } from './extras/GLTFAnimation.js'; // GLTF动画
export { DracoManager } from './extras/DracoManager.js'; // Draco压缩管理器
export { BasisManager } from './extras/BasisManager.js'; // Basis纹理管理器

// 辅助工具和可视化
export { WireMesh } from './extras/WireMesh.js'; // 线框网格
export { AxesHelper } from './extras/helpers/AxesHelper.js'; // 坐标轴辅助
export { GridHelper } from './extras/helpers/GridHelper.js'; // 网格辅助
export { VertexNormalsHelper } from './extras/helpers/VertexNormalsHelper.js'; // 顶点法线辅助
export { FaceNormalsHelper } from './extras/helpers/FaceNormalsHelper.js'; // 面法线辅助

// 高级渲染功能
export { InstancedMesh } from './extras/InstancedMesh.js'; // 实例化网格
export { Texture3D } from './extras/Texture3D.js'; // 3D纹理
