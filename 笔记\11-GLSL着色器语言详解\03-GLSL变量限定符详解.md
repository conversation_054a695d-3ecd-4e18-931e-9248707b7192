# GLSL变量限定符详解

## 概述
GLSL中的变量限定符（Qualifiers）定义了变量的存储类型、访问权限和数据流向。理解这些限定符对于编写正确的着色器程序至关重要。

## 1. 存储限定符 (Storage Qualifiers)

### 1.1 attribute（WebGL 1.0）/ in（WebGL 2.0）
用于顶点着色器中接收顶点属性数据。

```glsl
// WebGL 1.0 语法
attribute vec3 a_position;    // 顶点位置
attribute vec3 a_normal;      // 顶点法向量
attribute vec2 a_texCoord;    // 纹理坐标
attribute vec4 a_color;       // 顶点颜色

// WebGL 2.0 语法
in vec3 a_position;
in vec3 a_normal;
in vec2 a_texCoord;
in vec4 a_color;
```

**特点：**
- 只能在顶点着色器中使用
- 每个顶点都有独立的属性值
- 通过 `gl.vertexAttribPointer()` 从缓冲区读取数据
- 只读，不能在着色器中修改

### 1.2 uniform
用于传递在整个绘制调用中保持不变的全局数据。

```glsl
// 矩阵类型
uniform mat4 u_modelMatrix;      // 模型矩阵
uniform mat4 u_viewMatrix;       // 视图矩阵
uniform mat4 u_projectionMatrix; // 投影矩阵
uniform mat3 u_normalMatrix;     // 法向量矩阵

// 标量类型
uniform float u_time;            // 时间
uniform float u_opacity;         // 透明度
uniform int u_lightCount;        // 光源数量

// 向量类型
uniform vec3 u_lightPosition;    // 光源位置
uniform vec3 u_cameraPosition;   // 相机位置
uniform vec4 u_ambientColor;     // 环境光颜色

// 纹理采样器
uniform sampler2D u_diffuseTexture;  // 漫反射纹理
uniform sampler2D u_normalTexture;   // 法线纹理
uniform samplerCube u_envTexture;    // 环境贴图

// 数组
uniform vec3 u_lightPositions[8];    // 多个光源位置
uniform float u_lightIntensities[8]; // 光源强度数组
```

**特点：**
- 在顶点着色器和片段着色器中都可以使用
- 在整个绘制调用中值保持不变
- 通过 `gl.uniform*()` 系列函数设置值
- 只读，不能在着色器中修改

### 1.3 varying（WebGL 1.0）/ in/out（WebGL 2.0）
用于在顶点着色器和片段着色器之间传递数据。

```glsl
// 顶点着色器 (WebGL 1.0)
attribute vec3 a_position;
attribute vec2 a_texCoord;
attribute vec3 a_normal;

varying vec2 v_texCoord;      // 传递纹理坐标
varying vec3 v_normal;        // 传递法向量
varying vec3 v_worldPosition; // 传递世界坐标
varying float v_depth;        // 传递深度值

void main() {
    v_texCoord = a_texCoord;
    v_normal = normalize(u_normalMatrix * a_normal);
    v_worldPosition = (u_modelMatrix * vec4(a_position, 1.0)).xyz;
    v_depth = gl_Position.z;
    
    gl_Position = u_projectionMatrix * u_viewMatrix * u_modelMatrix * vec4(a_position, 1.0);
}

// 片段着色器 (WebGL 1.0)
varying vec2 v_texCoord;
varying vec3 v_normal;
varying vec3 v_worldPosition;
varying float v_depth;

void main() {
    vec3 color = texture2D(u_diffuseTexture, v_texCoord).rgb;
    // 使用插值后的变量进行光照计算
    gl_FragColor = vec4(color, 1.0);
}
```

```glsl
// WebGL 2.0 语法
// 顶点着色器
in vec3 a_position;
in vec2 a_texCoord;

out vec2 v_texCoord;      // 输出到片段着色器
out vec3 v_normal;

// 片段着色器
in vec2 v_texCoord;       // 从顶点着色器接收
in vec3 v_normal;

out vec4 fragColor;       // 输出颜色
```

**特点：**
- 在顶点着色器中写入，在片段着色器中读取
- 值在光栅化过程中自动插值
- 插值方式可以通过插值限定符控制

## 2. 参数限定符 (Parameter Qualifiers)

### 2.1 in（输入参数）
```glsl
// 默认的参数类型，可以省略
void processColor(in vec3 inputColor) {
    // inputColor 只能读取，不能修改
    vec3 result = inputColor * 2.0;
}

// 等价于
void processColor(vec3 inputColor) {
    vec3 result = inputColor * 2.0;
}
```

### 2.2 out（输出参数）
```glsl
void calculateLighting(in vec3 position, in vec3 normal, out vec3 diffuse, out vec3 specular) {
    // 计算漫反射和镜面反射
    diffuse = max(dot(normal, lightDirection), 0.0) * lightColor;
    specular = pow(max(dot(reflect(-lightDirection, normal), viewDirection), 0.0), shininess) * lightColor;
}

void main() {
    vec3 diffuseColor, specularColor;
    calculateLighting(v_worldPosition, v_normal, diffuseColor, specularColor);
    
    gl_FragColor = vec4(diffuseColor + specularColor, 1.0);
}
```

### 2.3 inout（输入输出参数）
```glsl
void modifyColor(inout vec3 color) {
    // 可以读取和修改传入的参数
    color = clamp(color * 1.5, 0.0, 1.0);
}

void main() {
    vec3 finalColor = vec3(0.8, 0.6, 0.4);
    modifyColor(finalColor);  // finalColor 会被修改
    
    gl_FragColor = vec4(finalColor, 1.0);
}
```

## 3. 精度限定符 (Precision Qualifiers)

### 3.1 精度类型
```glsl
// 高精度（24位）
precision highp float;
highp vec3 highPrecisionVector;

// 中等精度（16位）
precision mediump float;
mediump vec2 mediumPrecisionVector;

// 低精度（8位）
precision lowp float;
lowp vec4 lowPrecisionColor;
```

### 3.2 默认精度设置
```glsl
// 片段着色器通常需要显式设置浮点精度
precision mediump float;

// 顶点着色器有默认的高精度
// precision highp float; // 通常不需要显式声明
```

### 3.3 精度选择指南
```glsl
// 位置计算 - 使用高精度
highp vec4 worldPosition = u_modelMatrix * vec4(a_position, 1.0);

// 颜色计算 - 使用中等精度
mediump vec3 diffuseColor = texture2D(u_diffuseTexture, v_texCoord).rgb;

// 简单标志或索引 - 使用低精度
lowp float materialIndex = a_materialId;
```

## 4. 插值限定符 (Interpolation Qualifiers) - WebGL 2.0

### 4.1 smooth（默认）
```glsl
// 平滑插值（默认行为）
smooth out vec3 v_color;
// 等价于
out vec3 v_color;
```

### 4.2 flat
```glsl
// 平坦插值（不插值，使用第一个顶点的值）
flat out int v_materialId;
flat out vec3 v_faceNormal;
```

### 4.3 noperspective
```glsl
// 线性插值（不进行透视校正）
noperspective out vec2 v_screenCoord;
```

## 5. 不变性限定符 (Invariant Qualifier)

### 5.1 invariant
```glsl
// 确保在不同着色器中计算结果一致
invariant gl_Position;

// 或者在变量声明时使用
invariant varying vec2 v_texCoord;
```

## 6. 常量限定符

### 6.1 const
```glsl
// 编译时常量
const float PI = 3.14159265359;
const int MAX_LIGHTS = 8;
const vec3 UP_VECTOR = vec3(0.0, 1.0, 0.0);

// 在函数中使用
vec3 rotateAroundY(vec3 position, float angle) {
    const mat3 rotationMatrix = mat3(
        cos(angle), 0.0, sin(angle),
        0.0, 1.0, 0.0,
        -sin(angle), 0.0, cos(angle)
    );
    return rotationMatrix * position;
}
```

## 7. 布局限定符 (Layout Qualifiers) - WebGL 2.0

### 7.1 location
```glsl
// 指定属性位置
layout(location = 0) in vec3 a_position;
layout(location = 1) in vec3 a_normal;
layout(location = 2) in vec2 a_texCoord;

// 指定输出位置
layout(location = 0) out vec4 fragColor;
layout(location = 1) out vec4 brightColor;
```

### 7.2 binding
```glsl
// 指定纹理绑定点
layout(binding = 0) uniform sampler2D u_diffuseTexture;
layout(binding = 1) uniform sampler2D u_normalTexture;
```

## 8. 实际应用示例

### 8.1 完整的着色器示例
```glsl
// 顶点着色器
#version 300 es
precision highp float;

// 输入属性
layout(location = 0) in vec3 a_position;
layout(location = 1) in vec3 a_normal;
layout(location = 2) in vec2 a_texCoord;

// 统一变量
uniform mat4 u_modelMatrix;
uniform mat4 u_viewMatrix;
uniform mat4 u_projectionMatrix;
uniform mat3 u_normalMatrix;

// 输出到片段着色器
out vec3 v_worldPosition;
out vec3 v_normal;
out vec2 v_texCoord;

void main() {
    vec4 worldPosition = u_modelMatrix * vec4(a_position, 1.0);
    
    v_worldPosition = worldPosition.xyz;
    v_normal = normalize(u_normalMatrix * a_normal);
    v_texCoord = a_texCoord;
    
    gl_Position = u_projectionMatrix * u_viewMatrix * worldPosition;
}
```

```glsl
// 片段着色器
#version 300 es
precision mediump float;

// 从顶点着色器接收
in vec3 v_worldPosition;
in vec3 v_normal;
in vec2 v_texCoord;

// 统一变量
uniform vec3 u_lightPosition;
uniform vec3 u_cameraPosition;
uniform sampler2D u_diffuseTexture;

// 输出
layout(location = 0) out vec4 fragColor;

void main() {
    vec3 normal = normalize(v_normal);
    vec3 lightDir = normalize(u_lightPosition - v_worldPosition);
    vec3 viewDir = normalize(u_cameraPosition - v_worldPosition);
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    
    // 镜面反射
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
    
    // 纹理颜色
    vec3 texColor = texture(u_diffuseTexture, v_texCoord).rgb;
    
    // 最终颜色
    vec3 finalColor = texColor * (diff + spec);
    fragColor = vec4(finalColor, 1.0);
}
```

## 总结
变量限定符是GLSL中控制数据流和存储的关键机制。正确使用这些限定符可以：
- 确保数据在着色器管线中正确传递
- 优化性能（选择合适的精度）
- 避免编译错误和运行时问题
- 实现复杂的渲染效果

理解每种限定符的作用和使用场景是编写高质量着色器代码的基础。
