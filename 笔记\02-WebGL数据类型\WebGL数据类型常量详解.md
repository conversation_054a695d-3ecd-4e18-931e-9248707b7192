# WebGL 数据类型常量详解

## 概述

WebGL 中的数据类型常量是 GPU 与 JavaScript 之间数据交换的桥梁。这些常量定义了如何解释缓冲区中的二进制数据，确保 CPU 和 GPU 对数据格式有统一的理解。理解这些常量对于正确处理顶点属性、索引数据和纹理数据至关重要。

## 核心概念

### 数据类型常量的作用

```javascript
// WebGL数据类型常量告诉GPU如何解释缓冲区中的字节数据
const data = new Uint16Array([0, 1, 2, 3, 4, 5]);

// 告诉GPU这些字节应该被解释为16位无符号整数
gl.vertexAttribPointer(location, size, gl.UNSIGNED_SHORT, false, 0, 0);
//                                     ↑
//                            数据类型常量
```

### 常量值与对应关系

WebGL 常量实际上是整数值，用于在 WebGL API 调用中标识数据类型：

```javascript
// 查看常量的实际值
console.log(gl.BYTE); // 5120
console.log(gl.UNSIGNED_BYTE); // 5121
console.log(gl.SHORT); // 5122
console.log(gl.UNSIGNED_SHORT); // 5123
console.log(gl.INT); // 5124
console.log(gl.UNSIGNED_INT); // 5125
console.log(gl.FLOAT); // 5126
```

## 整数类型常量详解

### gl.BYTE (5120) - 8 位有符号整数

-   **对应类型化数组**: `Int8Array`
-   **数值范围**: -128 到 127
-   **字节大小**: 1 字节/元素
-   **使用场景**: 压缩法线、切线数据，节省内存

```javascript
// 压缩法线数据示例
const compressedNormals = new Int8Array([
    127,
    0,
    0, // 法线 (1, 0, 0) 压缩后
    0,
    127,
    0, // 法线 (0, 1, 0) 压缩后
    0,
    0,
    127, // 法线 (0, 0, 1) 压缩后
]);

// 在着色器中需要归一化
gl.vertexAttribPointer(normalLocation, 3, gl.BYTE, true, 0, 0);
//                                                   ↑
//                                            归一化为[-1, 1]
```

### gl.UNSIGNED_BYTE (5121) - 8 位无符号整数

-   **对应类型化数组**: `Uint8Array`
-   **数值范围**: 0 到 255
-   **字节大小**: 1 字节/元素
-   **使用场景**: 颜色数据、纹理坐标、小型索引

```javascript
// 颜色数据示例（RGBA格式）
const colors = new Uint8Array([
    255,
    0,
    0,
    255, // 红色 (1.0, 0.0, 0.0, 1.0)
    0,
    255,
    0,
    255, // 绿色 (0.0, 1.0, 0.0, 1.0)
    0,
    0,
    255,
    255, // 蓝色 (0.0, 0.0, 1.0, 1.0)
]);

// 归一化到[0, 1]范围
gl.vertexAttribPointer(colorLocation, 4, gl.UNSIGNED_BYTE, true, 0, 0);

// 小型模型的索引数据（支持256个顶点）
const smallIndices = new Uint8Array([0, 1, 2, 0, 2, 3]);
```

### gl.SHORT (5122) - 16 位有符号整数

-   **对应类型化数组**: `Int16Array`
-   **数值范围**: -32,768 到 32,767
-   **字节大小**: 2 字节/元素
-   **使用场景**: 压缩位置数据、高精度法线

```javascript
// 压缩位置数据示例
const compressedPositions = new Int16Array([
    -32768,
    -32768,
    0, // 最小位置
    32767,
    -32768,
    0, // 右下角
    0,
    32767,
    0, // 顶部中心
]);

// 在着色器中需要缩放到实际范围
gl.vertexAttribPointer(positionLocation, 3, gl.SHORT, false, 0, 0);
```

### gl.UNSIGNED_SHORT (5123) - 16 位无符号整数

-   **对应类型化数组**: `Uint16Array`
-   **数值范围**: 0 到 65,535
-   **字节大小**: 2 字节/元素
-   **使用场景**: 中等规模模型的索引数据（最常用）

```javascript
// 中等规模模型的索引数据
const indices = new Uint16Array([
    0,
    1,
    2, // 第一个三角形
    0,
    2,
    3, // 第二个三角形
    4,
    5,
    6, // 第三个三角形
    // ... 可以索引65536个顶点
]);

// 在drawElements中使用
gl.drawElements(gl.TRIANGLES, indices.length, gl.UNSIGNED_SHORT, 0);
```

### gl.INT (5124) - 32 位有符号整数

-   **对应类型化数组**: `Int32Array`
-   **数值范围**: -2,147,483,648 到 2,147,483,647
-   **字节大小**: 4 字节/元素
-   **使用场景**: 大整数计算、特殊标识符

```javascript
// 特殊用途的整数数据
const specialData = new Int32Array([
    -2147483648, // 最小值标记
    0, // 默认值
    2147483647, // 最大值标记
]);

gl.vertexAttribPointer(specialLocation, 1, gl.INT, false, 0, 0);
```

### gl.UNSIGNED_INT (5125) - 32 位无符号整数

-   **对应类型化数组**: `Uint32Array`
-   **数值范围**: 0 到 4,294,967,295
-   **字节大小**: 4 字节/元素
-   **使用场景**: 大型模型索引数据、实例 ID

```javascript
// 大型模型的索引数据
const largeIndices = new Uint32Array([
    0,
    1,
    2,
    65536,
    65537,
    65538, // 超出16位范围的索引
    // ... 可以索引超过400万个顶点
]);

// 需要检查扩展支持
const ext = gl.getExtension('OES_element_index_uint');
if (ext) {
    gl.drawElements(gl.TRIANGLES, largeIndices.length, gl.UNSIGNED_INT, 0);
}

// 实例ID数据
const instanceIds = new Uint32Array(1000); // 1000个实例
for (let i = 0; i < 1000; i++) {
    instanceIds[i] = i;
}
```

## 浮点类型常量详解

### gl.FLOAT (5126) - 32 位浮点数

-   **对应类型化数组**: `Float32Array`
-   **精度**: 单精度（约 7 位有效数字）
-   **字节大小**: 4 字节/元素
-   **使用场景**: 位置、法线、纹理坐标、变换矩阵（最常用）

```javascript
// WebGL中最常用的数据类型
const positions = new Float32Array([
    -1.0,
    -1.0,
    0.0, // 左下角
    1.0,
    -1.0,
    0.0, // 右下角
    0.0,
    1.0,
    0.0, // 顶部
]);

const normals = new Float32Array([
    0.0,
    0.0,
    1.0, // 向前的法线
    0.0,
    0.0,
    1.0,
    0.0,
    0.0,
    1.0,
]);

const uvs = new Float32Array([
    0.0,
    0.0, // 左下角UV
    1.0,
    0.0, // 右下角UV
    0.5,
    1.0, // 顶部UV
]);

// 4x4变换矩阵
const matrix = new Float32Array([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);
```

## 实际应用中的类型选择

### 内存优化策略

```javascript
// 根据数据范围选择合适的类型
class GeometryOptimizer {
    static chooseIndexType(vertexCount) {
        if (vertexCount <= 256) {
            return { type: gl.UNSIGNED_BYTE, ArrayType: Uint8Array };
        } else if (vertexCount <= 65536) {
            return { type: gl.UNSIGNED_SHORT, ArrayType: Uint16Array };
        } else {
            return { type: gl.UNSIGNED_INT, ArrayType: Uint32Array };
        }
    }

    static chooseColorType(needsAlpha, highPrecision) {
        if (highPrecision) {
            return { type: gl.FLOAT, ArrayType: Float32Array };
        } else {
            return { type: gl.UNSIGNED_BYTE, ArrayType: Uint8Array, normalized: true };
        }
    }
}

// 使用示例
const vertexCount = 1000;
const indexInfo = GeometryOptimizer.chooseIndexType(vertexCount);
const indices = new indexInfo.ArrayType([0, 1, 2, 3, 4, 5]);
```

### 性能考虑

```javascript
// 不同数据类型的性能特点
const performanceComparison = {
    UNSIGNED_BYTE: {
        memoryUsage: '最小',
        transferSpeed: '最快',
        precision: '最低',
        useCase: '颜色、小索引',
    },
    UNSIGNED_SHORT: {
        memoryUsage: '中等',
        transferSpeed: '快',
        precision: '中等',
        useCase: '中等模型索引',
    },
    UNSIGNED_INT: {
        memoryUsage: '大',
        transferSpeed: '慢',
        precision: '高',
        useCase: '大型模型索引',
    },
    FLOAT: {
        memoryUsage: '大',
        transferSpeed: '慢',
        precision: '最高',
        useCase: '位置、法线、UV',
    },
};
```

## 类型转换和兼容性

### 自动类型推断

```javascript
// Geometry类中的自动类型推断机制
function inferWebGLType(data) {
    if (data instanceof Float32Array) return gl.FLOAT;
    if (data instanceof Uint16Array) return gl.UNSIGNED_SHORT;
    if (data instanceof Uint32Array) return gl.UNSIGNED_INT;
    if (data instanceof Int8Array) return gl.BYTE;
    if (data instanceof Uint8Array) return gl.UNSIGNED_BYTE;
    if (data instanceof Int16Array) return gl.SHORT;
    if (data instanceof Int32Array) return gl.INT;

    // 默认返回FLOAT
    return gl.FLOAT;
}

// 在addAttribute中的应用
geometry.addAttribute('position', {
    size: 3,
    data: new Float32Array([...])  // 自动推断为gl.FLOAT
});

geometry.addAttribute('index', {
    data: new Uint16Array([...])   // 自动推断为gl.UNSIGNED_SHORT
});
```

### 字节大小计算

```javascript
// 根据WebGL类型计算字节大小
function getBytesPerElement(glType) {
    switch (glType) {
        case gl.BYTE:
        case gl.UNSIGNED_BYTE:
            return 1;
        case gl.SHORT:
        case gl.UNSIGNED_SHORT:
            return 2;
        case gl.INT:
        case gl.UNSIGNED_INT:
        case gl.FLOAT:
            return 4;
        default:
            throw new Error(`未知的WebGL类型: ${glType}`);
    }
}

// 在索引偏移量计算中的应用
const indexBytesPerElement = getBytesPerElement(this.attributes.index.type);
const byteOffset = this.drawRange.start * indexBytesPerElement;
```

## 常见问题和解决方案

### 1. 索引类型选择错误

```javascript
// ❌ 错误：顶点数超过类型限制
const vertices = new Float32Array(100000 * 3); // 10万个顶点
const indices = new Uint16Array([0, 1, 2, 99999, 99998, 99997]); // 错误！超出65535限制

// ✅ 正确：根据顶点数选择合适的索引类型
const vertexCount = 100000;
let indices;
if (vertexCount <= 65536) {
    indices = new Uint16Array([...]);
} else {
    // 检查扩展支持
    const ext = gl.getExtension('OES_element_index_uint');
    if (ext) {
        indices = new Uint32Array([...]);
    } else {
        throw new Error('大型索引不支持，需要分割模型');
    }
}
```

### 2. 归一化参数使用错误

```javascript
// ❌ 错误：颜色数据不归一化
const colors = new Uint8Array([255, 128, 0, 255]); // 0-255范围
gl.vertexAttribPointer(colorLocation, 4, gl.UNSIGNED_BYTE, false, 0, 0);
// 着色器中收到的值是255.0, 128.0, 0.0, 255.0 - 超出[0,1]范围！

// ✅ 正确：颜色数据需要归一化
gl.vertexAttribPointer(colorLocation, 4, gl.UNSIGNED_BYTE, true, 0, 0);
// 着色器中收到的值是1.0, 0.5, 0.0, 1.0 - 正确的[0,1]范围
```

### 3. 精度丢失问题

```javascript
// ⚠️ 注意：Float32Array的精度限制
const highPrecisionValue = 1.23456789012345; // 15位小数
const float32Value = new Float32Array([highPrecisionValue])[0];
console.log(float32Value); // 1.2345679 - 精度丢失！

// 解决方案：根据精度需求选择类型
function chooseFloatType(requiredPrecision) {
    if (requiredPrecision <= 7) {
        return { type: gl.FLOAT, ArrayType: Float32Array };
    } else {
        // WebGL不直接支持Float64，需要其他方案
        console.warn('WebGL不支持双精度浮点，考虑数据预处理');
        return { type: gl.FLOAT, ArrayType: Float32Array };
    }
}
```

## 调试和验证工具

### 数据类型验证器

```javascript
class WebGLTypeValidator {
    static validateAttributeData(name, data, glType) {
        const expectedArrayType = this.getExpectedArrayType(glType);

        if (!(data instanceof expectedArrayType)) {
            console.warn(`属性 ${name}: 数据类型不匹配`);
            console.warn(`期望: ${expectedArrayType.name}, 实际: ${data.constructor.name}`);
            return false;
        }

        return true;
    }

    static getExpectedArrayType(glType) {
        const typeMap = {
            [gl.BYTE]: Int8Array,
            [gl.UNSIGNED_BYTE]: Uint8Array,
            [gl.SHORT]: Int16Array,
            [gl.UNSIGNED_SHORT]: Uint16Array,
            [gl.INT]: Int32Array,
            [gl.UNSIGNED_INT]: Uint32Array,
            [gl.FLOAT]: Float32Array,
        };

        return typeMap[glType] || Float32Array;
    }

    static validateIndexRange(indices, vertexCount) {
        const maxIndex = Math.max(...indices);
        if (maxIndex >= vertexCount) {
            console.error(`索引越界: 最大索引${maxIndex}, 顶点数${vertexCount}`);
            return false;
        }
        return true;
    }
}

// 使用示例
const data = new Float32Array([1, 2, 3]);
WebGLTypeValidator.validateAttributeData('position', data, gl.FLOAT); // true
```

### 内存使用分析器

```javascript
class MemoryAnalyzer {
    static analyzeGeometry(geometry) {
        let totalBytes = 0;
        const analysis = {};

        Object.entries(geometry.attributes).forEach(([name, attr]) => {
            const bytesPerElement = getBytesPerElement(attr.type);
            const attributeBytes = attr.data.length * bytesPerElement;

            analysis[name] = {
                type: this.getTypeName(attr.type),
                elements: attr.data.length,
                bytesPerElement,
                totalBytes: attributeBytes,
                percentage: 0, // 稍后计算
            };

            totalBytes += attributeBytes;
        });

        // 计算百分比
        Object.values(analysis).forEach((info) => {
            info.percentage = ((info.totalBytes / totalBytes) * 100).toFixed(1);
        });

        return { analysis, totalBytes };
    }

    static getTypeName(glType) {
        const names = {
            [gl.BYTE]: 'BYTE',
            [gl.UNSIGNED_BYTE]: 'UNSIGNED_BYTE',
            [gl.SHORT]: 'SHORT',
            [gl.UNSIGNED_SHORT]: 'UNSIGNED_SHORT',
            [gl.INT]: 'INT',
            [gl.UNSIGNED_INT]: 'UNSIGNED_INT',
            [gl.FLOAT]: 'FLOAT',
        };
        return names[glType] || 'UNKNOWN';
    }
}

// 使用示例
const { analysis, totalBytes } = MemoryAnalyzer.analyzeGeometry(geometry);
console.log(`总内存使用: ${totalBytes} 字节`);
console.table(analysis);
```

## 最佳实践总结

### 1. 类型选择原则

```javascript
const bestPractices = {
    // 位置数据：通常使用FLOAT，除非内存极度受限
    position: gl.FLOAT,

    // 法线数据：可以使用压缩格式节省内存
    normal: gl.BYTE, // 归一化到[-1,1]

    // 纹理坐标：通常使用FLOAT，精度要求高
    uv: gl.FLOAT,

    // 颜色数据：使用UNSIGNED_BYTE节省75%内存
    color: gl.UNSIGNED_BYTE, // 归一化到[0,1]

    // 索引数据：根据顶点数量选择
    index: 'auto', // 自动选择UNSIGNED_BYTE/SHORT/INT

    // 实例化矩阵：必须使用FLOAT
    instanceMatrix: gl.FLOAT,
};
```

### 2. 性能优化建议

-   **内存优先**: 优先使用较小的数据类型，特别是移动设备
-   **精度平衡**: 在精度和性能之间找到平衡点
-   **批量处理**: 相同类型的数据放在一起处理
-   **扩展检查**: 使用 UNSIGNED_INT 前检查浏览器支持

这些 WebGL 数据类型常量是 GPU 编程的基础，正确选择和使用它们能够显著影响应用的性能和内存使用效率。
