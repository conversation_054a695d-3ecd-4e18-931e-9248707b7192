import { Vec3 } from '../math/Vec3.js';
import { Quat } from '../math/Quat.js';

// 预先创建临时变量以避免重复创建对象
// 前一帧的位置、旋转和缩放
const prevPos = /* @__PURE__ */ new Vec3();
const prevRot = /* @__PURE__ */ new Quat();
const prevScl = /* @__PURE__ */ new Vec3();

// 下一帧的位置、旋转和缩放
const nextPos = /* @__PURE__ */ new Vec3();
const nextRot = /* @__PURE__ */ new Quat();
const nextScl = /* @__PURE__ */ new Vec3();

/**
 * 动画类
 * 用于处理对象的关键帧动画
 */
export class Animation {
    /**
     * 创建一个动画实例
     * @param {Object} options - 动画选项
     * @param {Array} options.objects - 要应用动画的对象数组
     * @param {Object} options.data - 包含动画帧数据的对象
     */
    constructor({ objects, data }) {
        this.objects = objects; // 要动画的对象数组
        this.data = data; // 动画数据
        this.elapsed = 0; // 已经过去的时间
        this.weight = 1; // 动画权重
        this.duration = data.frames.length - 1; // 动画持续时间
    }

    /**
     * 更新动画状态
     * @param {Number} totalWeight - 所有动画的总权重，用于混合多个动画
     * @param {Boolean} isSet - 是否直接设置属性而不是混合
     */
    update(totalWeight = 1, isSet) {
        // 计算当前动画的实际权重
        const weight = isSet ? 1 : this.weight / totalWeight;
        // 计算当前动画的时间点（循环播放）
        const elapsed = this.elapsed % this.duration;

        // 计算当前帧和下一帧的索引以及它们之间的混合因子
        const floorFrame = Math.floor(elapsed);
        const blend = elapsed - floorFrame; // 两帧之间的插值因子
        const prevKey = this.data.frames[floorFrame]; // 当前帧数据
        const nextKey = this.data.frames[(floorFrame + 1) % this.duration]; // 下一帧数据

        // 为每个对象应用动画
        this.objects.forEach((object, i) => {
            // 从关键帧数据中提取当前帧的变换数据
            prevPos.fromArray(prevKey.position, i * 3);
            prevRot.fromArray(prevKey.quaternion, i * 4);
            prevScl.fromArray(prevKey.scale, i * 3);

            // 从关键帧数据中提取下一帧的变换数据
            nextPos.fromArray(nextKey.position, i * 3);
            nextRot.fromArray(nextKey.quaternion, i * 4);
            nextScl.fromArray(nextKey.scale, i * 3);

            // 在当前帧和下一帧之间进行插值
            prevPos.lerp(nextPos, blend); // 位置线性插值
            prevRot.slerp(nextRot, blend); // 旋转球面插值
            prevScl.lerp(nextScl, blend); // 缩放线性插值

            // 将插值结果应用到对象上，考虑动画权重
            object.position.lerp(prevPos, weight);
            object.quaternion.slerp(prevRot, weight);
            object.scale.lerp(prevScl, weight);
        });
    }
}
