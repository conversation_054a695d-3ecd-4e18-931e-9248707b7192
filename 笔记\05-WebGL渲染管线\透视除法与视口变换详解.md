# 透视除法与视口变换详解

## 概述
透视除法和视口变换是GPU渲染管线中的关键步骤，负责将3D坐标最终转换为屏幕像素坐标。

## 1. 透视除法 (Perspective Division)

### 核心操作
```javascript
// 透视除法的数学本质
// 输入：齐次坐标 (x, y, z, w)
// 输出：标准化设备坐标 NDC (x/w, y/w, z/w)

vec4 clipSpacePosition = projectionMatrix * viewSpacePosition;
// clipSpacePosition = (x, y, z, w)

vec3 ndcPosition = clipSpacePosition.xyz / clipSpacePosition.w;
// ndcPosition = (x/w, y/w, z/w)
```

### 工作原理

#### 1. 齐次坐标的意义
```
齐次坐标 (x, y, z, w) 表示3D点：
• 当 w = 1 时：表示普通3D点 (x, y, z)
• 当 w ≠ 1 时：实际3D点为 (x/w, y/w, z/w)
• w 分量携带了深度信息，用于透视投影
```

#### 2. 透视效果的实现
```
透视投影矩阵会根据深度调整 w 分量：
• 近处物体：w 值较小 → 除法后坐标较大 → 屏幕上显示较大
• 远处物体：w 值较大 → 除法后坐标较小 → 屏幕上显示较小

这就是"近大远小"透视效果的数学原理！
```

#### 3. 坐标范围标准化
```
透视除法将所有坐标标准化到 [-1, 1] 范围：
• X轴：-1 (视锥体左边) 到 +1 (视锥体右边)
• Y轴：-1 (视锥体下边) 到 +1 (视锥体上边)  
• Z轴：-1 (近裁剪平面) 到 +1 (远裁剪平面)
```

### 实际示例
```javascript
// 透视投影后的两个点
pointA_clip = (100, 200, 300, 100);  // 近处点
pointB_clip = (200, 400, 600, 200);  // 远处点

// 透视除法后
pointA_ndc = (100/100, 200/100, 300/100) = (1.0, 2.0, 3.0);
pointB_ndc = (200/200, 400/200, 600/200) = (1.0, 2.0, 3.0);

// 结果：相同的屏幕位置，但不同的深度值
```

## 2. 视口变换 (Viewport Transform)

### 核心操作
```javascript
// 视口变换公式
// 输入：NDC坐标 [-1, 1]
// 输出：屏幕像素坐标 [0, screenSize]

screenX = (ndcX + 1) * viewportWidth / 2 + viewportX;
screenY = (ndcY + 1) * viewportHeight / 2 + viewportY;
screenZ = (ndcZ + 1) * (far - near) / 2 + near;
```

### WebGL中的设置
```javascript
// 设置视口
gl.viewport(x, y, width, height);
// x, y: 视口在canvas中的起始位置（左下角）
// width, height: 视口的像素尺寸
```

### 坐标系转换详解

#### NDC坐标系 → 屏幕坐标系
```
NDC坐标系:              屏幕坐标系:
(-1,1)    (1,1)         (0,0)       (width,0)
  +--------+               +------------+
  |        |               |            |
  | (0,0)  |      →       |  (w/2,h/2) |
  |        |               |            |
  +--------+               +------------+
(-1,-1)   (1,-1)         (0,height)   (width,height)

注意：WebGL使用左下角为原点的坐标系
```

#### 变换公式推导
```
X轴变换：
NDC: [-1, 1] → Screen: [0, width]
公式：screenX = (ndcX + 1) * width / 2

Y轴变换：
NDC: [-1, 1] → Screen: [0, height]  
公式：screenY = (ndcY + 1) * height / 2

示例：
NDC点 (0.5, -0.5) 在 800x600 屏幕上：
screenX = (0.5 + 1) * 800 / 2 = 600
screenY = (-0.5 + 1) * 600 / 2 = 150
结果：(600, 150)
```

### 实际应用场景

#### 1. 适配不同屏幕尺寸
```javascript
// 手机屏幕 375x667
gl.viewport(0, 0, 375, 667);

// 桌面显示器 1920x1080
gl.viewport(0, 0, 1920, 1080);

// 同样的NDC坐标 (0, 0) 会映射到：
// 手机: (187, 333) - 屏幕中心
// 桌面: (960, 540) - 屏幕中心
```

#### 2. 多视口渲染
```javascript
// 分屏渲染示例
function renderSplitScreen() {
    // 左半屏 - 第一人称视角
    gl.viewport(0, 0, canvas.width/2, canvas.height);
    drawScene(firstPersonCamera);
    
    // 右半屏 - 第三人称视角
    gl.viewport(canvas.width/2, 0, canvas.width/2, canvas.height);
    drawScene(thirdPersonCamera);
}
```

#### 3. 小窗口渲染
```javascript
// 主视图
gl.viewport(0, 0, canvas.width, canvas.height);
drawMainScene();

// 小地图窗口（右上角）
const miniMapSize = 200;
gl.viewport(
    canvas.width - miniMapSize, 
    canvas.height - miniMapSize, 
    miniMapSize, 
    miniMapSize
);
drawMiniMap();
```

## 3. 完整的坐标变换管线

```
3D模型顶点 → 局部坐标 → 世界坐标 → 相机坐标 → 裁剪坐标 → NDC坐标 → 屏幕坐标
     ↓           ↓          ↓          ↓          ↓         ↓         ↓
   原始数据   模型矩阵   视图矩阵   投影矩阵   透视除法   视口变换   像素位置
     ↓           ↓          ↓          ↓          ↓         ↓         ↓
   顶点数据   移动旋转   相机变换   透视投影   标准化    屏幕映射   最终显示
```

### 在顶点着色器中的体现
```glsl
// 顶点着色器代码
attribute vec3 position;
uniform mat4 modelMatrix;
uniform mat4 viewMatrix;
uniform mat4 projectionMatrix;

void main() {
    // 1. 局部坐标 → 世界坐标
    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
    
    // 2. 世界坐标 → 相机坐标
    vec4 viewPosition = viewMatrix * worldPosition;
    
    // 3. 相机坐标 → 裁剪坐标
    gl_Position = projectionMatrix * viewPosition;
    
    // 4. GPU硬件自动执行：
    //    - 透视除法：gl_Position.xyz / gl_Position.w → NDC
    //    - 视口变换：NDC → 屏幕像素坐标
}
```

## 4. 深度缓冲与Z-Fighting

### 深度值的处理
```javascript
// 透视除法后的Z值用于深度测试
// Z值范围：[-1, 1] (NDC) → [0, 1] (深度缓冲)

depthBufferValue = (ndcZ + 1) / 2;

// 深度测试
if (newDepth < existingDepth) {
    // 更近的像素，更新颜色和深度
    colorBuffer[pixel] = newColor;
    depthBuffer[pixel] = newDepth;
}
```

### Z-Fighting问题
```
当两个面非常接近时：
• 深度值几乎相同
• 浮点精度误差导致深度测试结果不稳定
• 表现为闪烁的"打架"效果

解决方案：
• 调整近远裁剪平面比例
• 使用多边形偏移 (gl.polygonOffset)
• 避免重叠几何体
```

## 5. 关键要点总结

### 透视除法的作用
1. **实现透视效果**：通过w分量实现"近大远小"
2. **坐标标准化**：将坐标统一到[-1,1]范围
3. **深度准备**：为深度测试提供正确的Z值

### 视口变换的作用  
1. **设备适配**：适应不同屏幕尺寸和分辨率
2. **像素定位**：将抽象坐标转换为具体像素位置
3. **多视口支持**：实现分屏、小窗口等复杂布局

### 性能考虑
- 透视除法和视口变换都在GPU硬件中高效执行
- 这些操作对每个顶点都要执行，但硬件并行处理
- 正确的视口设置可以避免不必要的像素计算

## 6. 实际调试技巧

### 检查坐标变换
```javascript
// 在JavaScript中模拟坐标变换
function debugCoordinateTransform(localPos, modelMatrix, viewMatrix, projMatrix) {
    const worldPos = modelMatrix.multiply(localPos);
    const viewPos = viewMatrix.multiply(worldPos);
    const clipPos = projMatrix.multiply(viewPos);
    
    console.log('Local:', localPos);
    console.log('World:', worldPos);
    console.log('View:', viewPos);
    console.log('Clip:', clipPos);
    
    // 模拟透视除法
    const ndcPos = {
        x: clipPos.x / clipPos.w,
        y: clipPos.y / clipPos.w,
        z: clipPos.z / clipPos.w
    };
    console.log('NDC:', ndcPos);
    
    // 模拟视口变换
    const screenPos = {
        x: (ndcPos.x + 1) * canvas.width / 2,
        y: (ndcPos.y + 1) * canvas.height / 2
    };
    console.log('Screen:', screenPos);
}
```

这两个步骤是3D渲染管线的核心，理解它们对于掌握3D图形编程至关重要！
