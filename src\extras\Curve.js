import { Vec3 } from '../math/Vec3.js';

// 曲线类型常量
const CATMULLROM = 'catmullrom'; // Catmull-Rom样条曲线
const CUBICBEZIER = 'cubicbezier'; // 三次贝塞尔曲线
const QUADRATICBEZIER = 'quadraticbezier'; // 二次贝塞尔曲线

// 临时向量，用于计算过程中避免重复创建对象
const _a0 = /* @__PURE__ */ new Vec3(),
    _a1 = /* @__PURE__ */ new Vec3(),
    _a2 = /* @__PURE__ */ new Vec3(),
    _a3 = /* @__PURE__ */ new Vec3();

/**
 * 获取三次贝塞尔曲线的控制点
 * 用于Catmull-Rom样条曲线转换为贝塞尔曲线
 * @param {Array<Vec3>} points - 点数组
 * @param {Number} i - 当前点索引
 * @param {Number} [a=0.168] - 第一个控制点的张力参数
 * @param {Number} [b=0.168] - 第二个控制点的张力参数
 * @returns {Array<Vec3>} 两个控制点组成的数组
 */
function getCtrlPoint(points, i, a = 0.168, b = 0.168) {
    // 处理第一个点的控制点
    if (i < 1) {
        // 如果是第一个点，使用第一个和第二个点计算控制点
        _a0.sub(points[1], points[0]).scale(a).add(points[0]);
    } else {
        // 否则使用前后点计算控制点
        _a0.sub(points[i + 1], points[i - 1])
            .scale(a)
            .add(points[i]);
    }

    // 处理最后几个点的控制点
    if (i > points.length - 3) {
        const last = points.length - 1;
        // 如果是最后几个点，使用特殊方法计算控制点
        _a1.sub(points[last - 1], points[last])
            .scale(b)
            .add(points[last]);
    } else {
        // 否则使用常规方法计算控制点
        _a1.sub(points[i], points[i + 2])
            .scale(b)
            .add(points[i + 1]);
    }

    // 返回两个控制点的副本
    return [_a0.clone(), _a1.clone()];
}

/**
 * 计算二次贝塞尔曲线上的点
 * @param {Number} t - 参数，范围[0,1]
 * @param {Vec3} p0 - 起始点
 * @param {Vec3} c0 - 控制点
 * @param {Vec3} p1 - 结束点
 * @returns {Vec3} 曲线上的点
 */
function getQuadraticBezierPoint(t, p0, c0, p1) {
    const k = 1 - t; // 补数

    // 二次贝塞尔曲线公式: B(t) = (1-t)²P₀ + 2(1-t)tC₀ + t²P₁
    _a0.copy(p0).scale(k ** 2); // (1-t)²P₀
    _a1.copy(c0).scale(2 * k * t); // 2(1-t)tC₀
    _a2.copy(p1).scale(t ** 2); // t²P₁

    // 计算最终点
    const ret = new Vec3();
    ret.add(_a0, _a1).add(_a2);
    return ret;
}

/**
 * 计算三次贝塞尔曲线上的点
 * @param {Number} t - 参数，范围[0,1]
 * @param {Vec3} p0 - 起始点
 * @param {Vec3} c0 - 第一个控制点
 * @param {Vec3} c1 - 第二个控制点
 * @param {Vec3} p1 - 结束点
 * @returns {Vec3} 曲线上的点
 */
function getCubicBezierPoint(t, p0, c0, c1, p1) {
    const k = 1 - t; // 补数

    // 三次贝塞尔曲线公式: B(t) = (1-t)³P₀ + 3(1-t)²tC₀ + 3(1-t)t²C₁ + t³P₁
    _a0.copy(p0).scale(k ** 3); // (1-t)³P₀
    _a1.copy(c0).scale(3 * k ** 2 * t); // 3(1-t)²tC₀
    _a2.copy(c1).scale(3 * k * t ** 2); // 3(1-t)t²C₁
    _a3.copy(p1).scale(t ** 3); // t³P₁

    // 计算最终点
    const ret = new Vec3();
    ret.add(_a0, _a1).add(_a2).add(_a3);
    return ret;
}

/**
 * 曲线类
 * 支持三种类型的曲线：Catmull-Rom样条曲线、三次贝塞尔曲线和二次贝塞尔曲线
 */
export class Curve {
    /**
     * 创建一个曲线实例
     * @param {Object} [options] - 配置选项
     * @param {Array<Vec3>} [options.points] - 控制点数组，默认为四个点组成的矩形
     * @param {Number} [options.divisions=12] - 曲线细分数量
     * @param {String} [options.type=CATMULLROM] - 曲线类型
     */
    constructor({ points = [new Vec3(0, 0, 0), new Vec3(0, 1, 0), new Vec3(1, 1, 0), new Vec3(1, 0, 0)], divisions = 12, type = CATMULLROM } = {}) {
        this.points = points; // 控制点数组
        this.divisions = divisions; // 曲线细分数量
        this.type = type; // 曲线类型
    }

    /**
     * 获取二次贝塞尔曲线上的点
     * @private
     * @param {Number} [divisions=this.divisions] - 曲线细分数量
     * @returns {Array<Vec3>} 曲线上的点数组
     */
    _getQuadraticBezierPoints(divisions = this.divisions) {
        const points = [];
        const count = this.points.length;

        // 二次贝塞尔曲线至少需要3个点（起点、控制点、终点）
        if (count < 3) {
            console.warn('Not enough points provided.');
            return [];
        }

        // 第一段曲线的点
        const p0 = this.points[0]; // 起点
        let c0 = this.points[1], // 控制点
            p1 = this.points[2]; // 终点

        // 计算第一段曲线上的点
        for (let i = 0; i <= divisions; i++) {
            const p = getQuadraticBezierPoint(i / divisions, p0, c0, p1);
            points.push(p);
        }

        // 处理后续的曲线段
        let offset = 3;
        while (count - offset > 0) {
            // 使用反射法计算新的控制点，保持曲线的连续性
            p0.copy(p1); // 新的起点是上一段的终点
            c0 = p1.scale(2).sub(c0); // 新的控制点是上一段终点关于上一段控制点的反射
            p1 = this.points[offset]; // 新的终点

            // 计算新段曲线上的点（跳过第一个点，因为它与上一段的最后一个点重合）
            for (let i = 1; i <= divisions; i++) {
                const p = getQuadraticBezierPoint(i / divisions, p0, c0, p1);
                points.push(p);
            }
            offset++;
        }

        return points;
    }

    /**
     * 获取三次贝塞尔曲线上的点
     * @private
     * @param {Number} [divisions=this.divisions] - 曲线细分数量
     * @returns {Array<Vec3>} 曲线上的点数组
     */
    _getCubicBezierPoints(divisions = this.divisions) {
        const points = [];
        const count = this.points.length;

        // 三次贝塞尔曲线至少需要4个点（起点、两个控制点、终点）
        if (count < 4) {
            console.warn('Not enough points provided.');
            return [];
        }

        // 第一段曲线的点
        let p0 = this.points[0], // 起点
            c0 = this.points[1], // 第一个控制点
            c1 = this.points[2], // 第二个控制点
            p1 = this.points[3]; // 终点

        // 计算第一段曲线上的点
        for (let i = 0; i <= divisions; i++) {
            const p = getCubicBezierPoint(i / divisions, p0, c0, c1, p1);
            points.push(p);
        }

        // 处理后续的曲线段
        let offset = 4;
        while (count - offset > 1) {
            // 使用反射法计算新的控制点，保持曲线的连续性
            p0.copy(p1); // 新的起点是上一段的终点
            c0 = p1.scale(2).sub(c1); // 新的第一个控制点是上一段终点关于上一段第二个控制点的反射
            c1 = this.points[offset]; // 新的第二个控制点
            p1 = this.points[offset + 1]; // 新的终点

            // 计算新段曲线上的点（跳过第一个点，因为它与上一段的最后一个点重合）
            for (let i = 1; i <= divisions; i++) {
                const p = getCubicBezierPoint(i / divisions, p0, c0, c1, p1);
                points.push(p);
            }
            offset += 2;
        }

        return points;
    }

    /**
     * 获取Catmull-Rom样条曲线上的点
     * 通过将Catmull-Rom样条转换为三次贝塞尔曲线来实现
     * @private
     * @param {Number} [divisions=this.divisions] - 曲线细分数量
     * @param {Number} [a=0.168] - 第一个控制点的张力参数
     * @param {Number} [b=0.168] - 第二个控制点的张力参数
     * @returns {Array<Vec3>} 曲线上的点数组
     */
    _getCatmullRomPoints(divisions = this.divisions, a = 0.168, b = 0.168) {
        const points = [];
        const count = this.points.length;

        // Catmull-Rom样条曲线至少需要3个点
        if (count <= 2) {
            return this.points;
        }

        let p0;
        this.points.forEach((p, i) => {
            if (i === 0) {
                // 记录第一个点
                p0 = p;
            } else {
                // 计算控制点
                const [c0, c1] = getCtrlPoint(this.points, i - 1, a, b);
                // 创建一个三次贝塞尔曲线
                const c = new Curve({
                    points: [p0, c0, c1, p],
                    type: CUBICBEZIER,
                });
                // 移除上一个点（避免重复）
                points.pop();
                // 添加贝塞尔曲线上的点
                points.push(...c.getPoints(divisions));
                // 更新起点为当前点
                p0 = p;
            }
        });

        return points;
    }

    /**
     * 获取曲线上的点
     * @param {Number} [divisions=this.divisions] - 曲线细分数量
     * @param {Number} [a=0.168] - Catmull-Rom曲线的第一个控制点张力参数
     * @param {Number} [b=0.168] - Catmull-Rom曲线的第二个控制点张力参数
     * @returns {Array<Vec3>} 曲线上的点数组
     */
    getPoints(divisions = this.divisions, a = 0.168, b = 0.168) {
        const type = this.type;

        // 根据曲线类型调用相应的方法
        if (type === QUADRATICBEZIER) {
            return this._getQuadraticBezierPoints(divisions);
        }

        if (type === CUBICBEZIER) {
            return this._getCubicBezierPoints(divisions);
        }

        if (type === CATMULLROM) {
            return this._getCatmullRomPoints(divisions, a, b);
        }

        // 如果类型不匹配，直接返回控制点
        return this.points;
    }
}

// 导出曲线类型常量
Curve.CATMULLROM = CATMULLROM;
Curve.CUBICBEZIER = CUBICBEZIER;
Curve.QUADRATICBEZIER = QUADRATICBEZIER;
