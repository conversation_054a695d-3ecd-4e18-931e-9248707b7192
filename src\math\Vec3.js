import * as Vec3Func from './functions/Vec3Func.js';

/**
 * @file 三维向量类
 *
 * 该文件实现了三维向量类，提供了向量运算的各种方法。
 * Vec3类继承自JavaScript的Array类，使其可以像数组一样被访问。
 */

/**
 * 三维向量类
 *
 * 表示三维空间中的点或方向，提供了丰富的向量运算方法。
 * 继承自Array，可以通过索引访问分量(如vec[0]等同于vec.x)。
 */
export class Vec3 extends Array {
    /**
     * 创建一个新的三维向量
     *
     * @param {number} [x=0] - x分量
     * @param {number} [y=x] - y分量，默认等于x
     * @param {number} [z=x] - z分量，默认等于x
     * @returns {Vec3} 新创建的向量
     */
    constructor(x = 0, y = x, z = x) {
        super(x, y, z);
        return this;
    }

    /**
     * 获取x分量
     * @returns {number} x分量值
     */
    get x() {
        return this[0];
    }

    /**
     * 获取y分量
     * @returns {number} y分量值
     */
    get y() {
        return this[1];
    }

    /**
     * 获取z分量
     * @returns {number} z分量值
     */
    get z() {
        return this[2];
    }

    /**
     * 设置x分量
     * @param {number} v - 新的x分量值
     */
    set x(v) {
        this[0] = v;
    }

    /**
     * 设置y分量
     * @param {number} v - 新的y分量值
     */
    set y(v) {
        this[1] = v;
    }

    /**
     * 设置z分量
     * @param {number} v - 新的z分量值
     */
    set z(v) {
        this[2] = v;
    }

    /**
     * 设置向量的分量值
     *
     * @param {number|Vec3} x - x分量值或包含所有分量的向量
     * @param {number} [y=x] - y分量值，默认等于x
     * @param {number} [z=x] - z分量值，默认等于x
     * @returns {Vec3} 当前向量的引用
     */
    set(x, y = x, z = x) {
        if (x.length) return this.copy(x);
        Vec3Func.set(this, x, y, z);
        return this;
    }

    /**
     * 复制另一个向量的值
     *
     * @param {Vec3} v - 要复制的向量
     * @returns {Vec3} 当前向量的引用
     */
    copy(v) {
        Vec3Func.copy(this, v);
        return this;
    }

    /**
     * 向量加法
     *
     * @param {Vec3} va - 第一个向量
     * @param {Vec3} [vb] - 第二个向量，如果未提供则使用当前向量作为第一个向量
     * @returns {Vec3} 当前向量的引用
     */
    add(va, vb) {
        if (vb) Vec3Func.add(this, va, vb);
        else Vec3Func.add(this, this, va);
        return this;
    }

    /**
     * 向量减法
     *
     * @param {Vec3} va - 第一个向量
     * @param {Vec3} [vb] - 第二个向量，如果未提供则从当前向量减去va
     * @returns {Vec3} 当前向量的引用
     */
    sub(va, vb) {
        if (vb) Vec3Func.subtract(this, va, vb);
        else Vec3Func.subtract(this, this, va);
        return this;
    }

    /**
     * 向量乘法或标量乘法
     *
     * @param {Vec3|number} v - 要相乘的向量或标量
     * @returns {Vec3} 当前向量的引用
     */
    multiply(v) {
        if (v.length) Vec3Func.multiply(this, this, v);
        else Vec3Func.scale(this, this, v);
        return this;
    }

    /**
     * 向量除法或标量除法
     *
     * @param {Vec3|number} v - 要相除的向量或标量
     * @returns {Vec3} 当前向量的引用
     */
    divide(v) {
        if (v.length) Vec3Func.divide(this, this, v);
        else Vec3Func.scale(this, this, 1 / v);
        return this;
    }

    /**
     * 计算向量的逆（每个分量取倒数）
     *
     * @param {Vec3} [v=this] - 要计算逆的向量，默认为当前向量
     * @returns {Vec3} 当前向量的引用
     */
    inverse(v = this) {
        Vec3Func.inverse(this, v);
        return this;
    }

    /**
     * 计算向量的长度（模）
     * 注：不能使用'length'作为方法名，因为Array.prototype已使用该名称
     *
     * @returns {number} 向量的长度
     */
    len() {
        return Vec3Func.length(this);
    }

    /**
     * 计算与另一个向量的距离，或当前向量的长度
     *
     * @param {Vec3} [v] - 另一个向量，如果未提供则返回当前向量的长度
     * @returns {number} 两向量间的距离或当前向量的长度
     */
    distance(v) {
        if (v) return Vec3Func.distance(this, v);
        else return Vec3Func.length(this);
    }

    /**
     * 计算向量长度的平方
     *
     * @returns {number} 向量长度的平方
     */
    squaredLen() {
        return Vec3Func.squaredLength(this);
    }

    /**
     * 计算与另一个向量距离的平方，或当前向量长度的平方
     *
     * @param {Vec3} [v] - 另一个向量，如果未提供则返回当前向量长度的平方
     * @returns {number} 两向量间距离的平方或当前向量长度的平方
     */
    squaredDistance(v) {
        if (v) return Vec3Func.squaredDistance(this, v);
        else return Vec3Func.squaredLength(this);
    }

    /**
     * 向量取反（所有分量变为相反数）
     *
     * @param {Vec3} [v=this] - 要取反的向量，默认为当前向量
     * @returns {Vec3} 当前向量的引用
     */
    negate(v = this) {
        Vec3Func.negate(this, v);
        return this;
    }

    /**
     * 计算向量叉积
     *
     * @param {Vec3} va - 第一个向量
     * @param {Vec3} [vb] - 第二个向量，如果未提供则使用当前向量作为第一个向量
     * @returns {Vec3} 当前向量的引用
     */
    cross(va, vb) {
        if (vb) Vec3Func.cross(this, va, vb);
        else Vec3Func.cross(this, this, va);
        return this;
    }

    /**
     * 向量缩放（乘以标量）
     *
     * @param {number} v - 缩放因子
     * @returns {Vec3} 当前向量的引用
     */
    scale(v) {
        Vec3Func.scale(this, this, v);
        return this;
    }

    /**
     * 向量归一化（使长度为1）
     *
     * @returns {Vec3} 当前向量的引用
     */
    normalize() {
        Vec3Func.normalize(this, this);
        return this;
    }

    /**
     * 计算与另一个向量的点积
     *
     * @param {Vec3} v - 另一个向量
     * @returns {number} 点积结果
     */
    dot(v) {
        return Vec3Func.dot(this, v);
    }

    equals(v) {
        return Vec3Func.exactEquals(this, v);
    }

    applyMatrix3(mat3) {
        Vec3Func.transformMat3(this, this, mat3);
        return this;
    }

    applyMatrix4(mat4) {
        Vec3Func.transformMat4(this, this, mat4);
        return this;
    }

    scaleRotateMatrix4(mat4) {
        Vec3Func.scaleRotateMat4(this, this, mat4);
        return this;
    }

    applyQuaternion(q) {
        Vec3Func.transformQuat(this, this, q);
        return this;
    }

    angle(v) {
        return Vec3Func.angle(this, v);
    }

    lerp(v, t) {
        Vec3Func.lerp(this, this, v, t);
        return this;
    }

    smoothLerp(v, decay, dt) {
        Vec3Func.smoothLerp(this, this, v, decay, dt);
        return this;
    }

    clone() {
        return new Vec3(this[0], this[1], this[2]);
    }

    fromArray(a, o = 0) {
        this[0] = a[o];
        this[1] = a[o + 1];
        this[2] = a[o + 2];
        return this;
    }

    toArray(a = [], o = 0) {
        a[o] = this[0];
        a[o + 1] = this[1];
        a[o + 2] = this[2];
        return a;
    }

    transformDirection(mat4) {
        const x = this[0];
        const y = this[1];
        const z = this[2];

        this[0] = mat4[0] * x + mat4[4] * y + mat4[8] * z;
        this[1] = mat4[1] * x + mat4[5] * y + mat4[9] * z;
        this[2] = mat4[2] * x + mat4[6] * y + mat4[10] * z;

        return this.normalize();
    }
}
