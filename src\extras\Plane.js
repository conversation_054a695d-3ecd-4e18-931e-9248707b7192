import { Geometry } from '../core/Geometry.js';

/**
 * @file 平面几何体类
 *
 * 该文件实现了平面几何体，可用于创建矩形平面网格。
 * 平面可以细分为多个段，以增加细节或用于变形。
 */

/**
 * 平面几何体类
 *
 * 表示一个矩形平面，可以指定宽度、高度和细分段数。
 * 继承自基础几何体类。
 */
export class Plane extends Geometry {
    /**
     * 创建一个新的平面几何体
     *
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} [options] - 平面配置选项
     * @param {number} [options.width=1] - 平面宽度
     * @param {number} [options.height=1] - 平面高度
     * @param {number} [options.widthSegments=1] - 宽度方向的细分段数
     * @param {number} [options.heightSegments=1] - 高度方向的细分段数
     * @param {Object} [options.attributes={}] - 附加的几何体属性
     */
    constructor(gl, { width = 1, height = 1, widthSegments = 1, heightSegments = 1, attributes = {} } = {}) {
        // 存储段数以便于使用
        const wSegs = widthSegments;
        const hSegs = heightSegments;

        // 确定数组长度
        const num = (wSegs + 1) * (hSegs + 1); // 顶点数量
        const numIndices = wSegs * hSegs * 6; // 索引数量（每个四边形由2个三角形组成，每个三角形3个索引）

        // 一次性生成空数组
        const position = new Float32Array(num * 3); // 位置 (x, y, z)
        const normal = new Float32Array(num * 3); // 法线 (x, y, z)
        const uv = new Float32Array(num * 2); // 纹理坐标 (u, v)
        // 根据索引数量选择合适的索引类型（大于65536需要使用Uint32Array）
        const index = numIndices > 65536 ? new Uint32Array(numIndices) : new Uint16Array(numIndices);

        // 构建平面几何数据
        Plane.buildPlane(position, normal, uv, index, width, height, 0, wSegs, hSegs);

        // 设置几何体属性
        Object.assign(attributes, {
            position: { size: 3, data: position }, // 顶点位置
            normal: { size: 3, data: normal }, // 顶点法线
            uv: { size: 2, data: uv }, // 纹理坐标
            index: { data: index }, // 索引数据
        });

        // 调用父类构造函数
        super(gl, attributes);
    }

    /**
     * 构建平面几何体数据
     *
     * 该静态方法生成平面的顶点、法线、纹理坐标和索引数据。
     * 可以通过参数控制平面的方向和位置。
     *
     * @param {Float32Array} position - 顶点位置数组
     * @param {Float32Array} normal - 顶点法线数组
     * @param {Float32Array} uv - 纹理坐标数组
     * @param {Uint16Array|Uint32Array} index - 索引数组
     * @param {number} width - 平面宽度
     * @param {number} height - 平面高度
     * @param {number} depth - 平面深度偏移
     * @param {number} wSegs - 宽度方向的细分段数
     * @param {number} hSegs - 高度方向的细分段数
     * @param {number} [u=0] - 位置数组中表示宽度的索引 (0=x, 1=y, 2=z)
     * @param {number} [v=1] - 位置数组中表示高度的索引 (0=x, 1=y, 2=z)
     * @param {number} [w=2] - 位置数组中表示深度的索引 (0=x, 1=y, 2=z)
     * @param {number} [uDir=1] - 宽度方向的符号 (1 或 -1)
     * @param {number} [vDir=-1] - 高度方向的符号 (1 或 -1)
     * @param {number} [i=0] - 顶点数组的起始索引
     * @param {number} [ii=0] - 索引数组的起始索引
     */
    static buildPlane(position, normal, uv, index, width, height, depth, wSegs, hSegs, u = 0, v = 1, w = 2, uDir = 1, vDir = -1, i = 0, ii = 0) {
        const io = i; // 保存初始顶点索引
        const segW = width / wSegs; // 每段宽度
        const segH = height / hSegs; // 每段高度

        // 遍历所有网格点，生成顶点数据
        for (let iy = 0; iy <= hSegs; iy++) {
            // 计算当前行的y坐标（从中心点开始）
            let y = iy * segH - height / 2;

            for (let ix = 0; ix <= wSegs; ix++, i++) {
                // 计算当前列的x坐标（从中心点开始）
                let x = ix * segW - width / 2;

                // 设置顶点位置
                // 根据u、v、w参数确定哪个轴对应宽度、高度和深度
                position[i * 3 + u] = x * uDir; // 宽度方向
                position[i * 3 + v] = y * vDir; // 高度方向
                position[i * 3 + w] = depth / 2; // 深度方向

                // 设置法线方向（垂直于平面）
                normal[i * 3 + u] = 0;
                normal[i * 3 + v] = 0;
                normal[i * 3 + w] = depth >= 0 ? 1 : -1; // 根据深度确定法线方向

                // 设置纹理坐标 (0,0) 到 (1,1)
                uv[i * 2] = ix / wSegs; // u坐标
                uv[i * 2 + 1] = 1 - iy / hSegs; // v坐标（翻转y轴）

                // 跳过最后一行和最后一列的索引创建（因为它们没有完整的四边形）
                if (iy === hSegs || ix === wSegs) continue;

                // 计算当前四边形的四个顶点索引
                let a = io + ix + iy * (wSegs + 1); // 左上
                let b = io + ix + (iy + 1) * (wSegs + 1); // 左下
                let c = io + ix + (iy + 1) * (wSegs + 1) + 1; // 右下
                let d = io + ix + iy * (wSegs + 1) + 1; // 右上

                // 创建两个三角形（一个四边形）
                // 三角形1：a-b-d（左上-左下-右上）
                index[ii * 6] = a;
                index[ii * 6 + 1] = b;
                index[ii * 6 + 2] = d;
                // 三角形2：b-c-d（左下-右下-右上）
                index[ii * 6 + 3] = b;
                index[ii * 6 + 4] = c;
                index[ii * 6 + 5] = d;

                ii++; // 移动到下一个四边形
            }
        }
    }
}
