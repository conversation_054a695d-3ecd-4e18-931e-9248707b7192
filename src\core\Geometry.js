/**
 * @file WebGL几何体管理系统 - 企业级顶点数据和缓冲区管理
 *
 * 该文件实现了高性能的WebGL几何体类，是现代3D渲染管线的核心组件。
 * 提供完整的顶点数据管理、GPU缓冲区优化和渲染状态管理功能。
 *
 * 🎯 **核心架构特性**：
 * - 🚀 高性能VAO（顶点数组对象）管理和缓存
 * - 💾 智能VBO（顶点缓冲区对象）创建和更新
 * - 🔄 实例化渲染支持（批量高效渲染）
 * - 📊 自动包围盒和包围球计算
 * - ⚡ WebGL状态优化和缓存机制
 * - 🎨 多种数据格式和布局支持
 *
 * 🔧 **主要功能模块**：
 * - **缓冲区管理**：创建、更新、删除WebGL缓冲区对象
 * - **属性系统**：灵活的顶点属性配置和绑定
 * - **索引支持**：高效的索引绘制和非索引绘制
 * - **实例化渲染**：大规模对象的批量渲染优化
 * - **动态更新**：运行时几何体数据的实时修改
 * - **空间计算**：包围盒、包围球等空间查询功能
 * - **内存管理**：自动资源清理和内存泄漏防护
 *
 * 🎮 **应用场景**：
 * - **基础几何体**：三角形、四边形、立方体、球体等标准形状
 * - **复杂模型**：从外部文件加载的3D模型数据
 * - **粒子系统**：大量粒子的实例化高效渲染
 * - **地形渲染**：大规模地形网格的动态生成和渲染
 * - **UI元素**：2D/3D用户界面组件的几何体表示
 * - **动画系统**：骨骼动画、变形动画的顶点数据管理
 * - **程序化生成**：算法生成的几何体和程序化内容
 *
 * 📈 **性能优化特性**：
 * - VAO缓存机制减少状态切换开销
 * - 智能缓冲区更新策略（STATIC/DYNAMIC/STREAM）
 * - 批量属性绑定优化
 * - 内存对齐和数据布局优化
 * - GPU状态管理和冗余操作消除
 *
 * 🛡️ **企业级特性**：
 * - 完整的错误处理和边界检查
 * - 详细的性能监控和调试支持
 * - 内存泄漏防护和资源自动清理
 * - 跨平台兼容性和WebGL版本适配
 * - 可扩展的插件架构和自定义属性支持
 *
 * <AUTHOR> Framework Team
 * @version 2.0.0
 * @since 2024
 * @license MIT
 *
 * @requires Vec3 - 3D向量数学库
 * @requires WebGL1/WebGL2 - WebGL渲染上下文
 *
 * @example
 * // 基础使用示例
 * const geometry = new Geometry(gl, {
 *   position: { size: 3, data: new Float32Array([...]) },
 *   normal: { size: 3, data: new Float32Array([...]) },
 *   uv: { size: 2, data: new Float32Array([...]) }
 * });
 *
 * @see {@link https://www.khronos.org/webgl/} WebGL规范
 * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API} WebGL API文档
 */

/**
 * 顶点属性配置对象 - WebGL几何体属性的完整配置规范
 *
 * 该类型定义了几何体属性的所有可配置参数，支持各种WebGL渲染场景。
 * 从基础的顶点位置到复杂的实例化渲染，提供了灵活而强大的配置选项。
 *
 * @typedef {Object} AttributeConfig
 *
 * @property {TypedArray} data - 顶点数据的类型化数组 **[必需]**
 *   📊 **支持的数据类型**：
 *   - `Float32Array`: 浮点数据（位置、法线、纹理坐标、权重等）
 *   - `Uint16Array`: 16位无符号整数（索引数据，支持65536个顶点）
 *   - `Uint32Array`: 32位无符号整数（大型模型索引，支持4B+顶点）
 *   - `Int8Array`: 8位有符号整数（压缩法线、切线等）
 *   - `Uint8Array`: 8位无符号整数（颜色数据0-255，压缩属性）
 *   - `Int16Array`: 16位有符号整数（压缩位置数据等）
 *
 *   💡 **性能建议**：
 *   - 位置/法线/UV：推荐Float32Array（精度和兼容性最佳）
 *   - 颜色数据：推荐Uint8Array + normalized（节省75%内存）
 *   - 索引数据：小模型用Uint16Array，大模型用Uint32Array
 *
 * @property {number} [size=1] - 每个顶点的分量数量
 *   🎯 **常用配置**：
 *   - `1`: 标量属性（密度、温度、ID等）
 *   - `2`: 2D坐标（纹理UV、屏幕坐标等）
 *   - `3`: 3D向量（位置xyz、法线、颜色RGB等）
 *   - `4`: 4D向量（齐次坐标、颜色RGBA、四元数等）
 *   - `9`: 3x3矩阵（法线变换矩阵等）
 *   - `16`: 4x4矩阵（实例化变换矩阵等）
 *
 *   ⚠️ **注意事项**：
 *   - 矩阵属性会自动拆分为多个顶点属性位置
 *   - size必须与着色器中的属性声明匹配
 *
 * @property {number} [instanced=0] - 实例化渲染除数
 *   🔄 **实例化配置**：
 *   - `0`: 每个顶点使用一次（标准顶点属性）
 *   - `1`: 每个实例使用一次（实例化属性）
 *   - `N`: 每N个实例使用一次（高级实例化）
 *
 *   🚀 **性能优势**：
 *   - 实例化渲染可将绘制调用从N次减少到1次
 *   - 适用于粒子系统、草地、建筑群等大量重复对象
 *   - GPU并行处理，性能提升可达10-100倍
 *
 * @property {number} [type] - WebGL数据类型枚举 **[自动推断]**
 *   🔧 **自动类型映射**：
 *   - `Float32Array` → `gl.FLOAT` (32位浮点)
 *   - `Uint16Array` → `gl.UNSIGNED_SHORT` (16位无符号整数)
 *   - `Uint32Array` → `gl.UNSIGNED_INT` (32位无符号整数)
 *   - `Int8Array` → `gl.BYTE` (8位有符号整数)
 *   - `Uint8Array` → `gl.UNSIGNED_BYTE` (8位无符号整数)
 *   - `Int16Array` → `gl.SHORT` (16位有符号整数)
 *
 *   📝 **手动指定场景**：
 *   - 使用预创建的缓冲区时
 *   - 需要特殊类型转换时
 *   - 跨平台兼容性要求时
 *
 * @property {boolean} [normalized=false] - 数据归一化标志
 *   📐 **归一化效果**：
 *   - `true`: 整数值映射到[0,1]（无符号）或[-1,1]（有符号）
 *   - `false`: 保持原始数值不变
 *
 *   💾 **内存优化示例**：
 *   ```javascript
 *   // 颜色数据：从Float32Array(12字节)优化到Uint8Array(3字节)
 *   color: {
 *     size: 3,
 *     data: new Uint8Array([255, 128, 0]), // 橙色
 *     normalized: true // 255→1.0, 128→0.5, 0→0.0
 *   }
 *   ```
 *
 * @property {WebGLBuffer} [buffer] - 预创建的WebGL缓冲区对象
 *   🔗 **高级用法**：
 *   - 缓冲区共享：多个几何体共用同一缓冲区
 *   - 外部管理：由其他系统创建和管理的缓冲区
 *   - 内存池：从预分配的大缓冲区中分配子区域
 *
 *   ⚠️ **使用注意**：
 *   - 提供buffer时，data参数将被忽略
 *   - 必须手动指定count参数
 *   - 缓冲区生命周期需要外部管理
 *
 * @property {number} [stride=0] - 顶点间字节步长（交错数据布局）
 *   📦 **数据布局优化**：
 *   - `0`: 紧密排列（默认，所有position连续，然后所有normal连续）
 *   - `>0`: 交错排列（position+normal+uv为一组，然后下一个顶点）
 *
 *   🚀 **性能优势**：
 *   - 交错布局提高缓存局部性
 *   - 减少GPU内存带宽需求
 *   - 特别适合复杂的顶点属性组合
 *
 * @property {number} [offset=0] - 缓冲区内字节偏移量
 *   🎯 **精确控制**：
 *   - 从缓冲区的指定位置开始读取数据
 *   - 支持在单个大缓冲区中存储多个属性
 *   - 实现高级内存管理和优化策略
 *
 * @property {number} [count] - 顶点/索引数量 **[自动计算]**
 *   🧮 **计算规则**：
 *   - 交错数据：`data.byteLength / stride`
 *   - 紧密数据：`data.length / size`
 *   - 手动指定：覆盖自动计算（用于部分数据渲染）
 *
 * @property {Array<number>} [min] - 属性值最小边界（包围盒计算）
 * @property {Array<number>} [max] - 属性值最大边界（包围盒计算）
 *   📏 **空间优化**：
 *   - 预计算的边界信息，避免运行时遍历
 *   - 用于视锥体剔除、碰撞检测等空间查询
 *   - 格式：[minX, minY, minZ] 和 [maxX, maxY, maxZ]
 *
 * @property {number} [usage=gl.STATIC_DRAW] - 缓冲区使用模式提示
 *   ⚡ **性能优化提示**：
 *   - `gl.STATIC_DRAW`: 数据创建后不再修改（默认，最优性能）
 *   - `gl.DYNAMIC_DRAW`: 数据会定期修改（如动画、变形）
 *   - `gl.STREAM_DRAW`: 数据每帧都会修改（如粒子系统）
 *
 *   💡 **GPU优化**：
 *   - 正确的usage提示帮助GPU选择最优内存位置
 *   - STATIC数据通常存储在高速显存中
 *   - DYNAMIC/STREAM数据可能使用可写缓存
 *
 * @example
 * // 基础顶点属性配置
 * const basicConfig = {
 *   position: {
 *     size: 3,
 *     data: new Float32Array([0, 1, 0, -1, -1, 0, 1, -1, 0])
 *   }
 * };
 *
 * @example
 * // 高级实例化配置
 * const instancedConfig = {
 *   position: { size: 3, data: baseVertices },
 *   instanceMatrix: {
 *     size: 16,
 *     instanced: 1,
 *     data: new Float32Array(instanceCount * 16),
 *     usage: gl.DYNAMIC_DRAW
 *   }
 * };
 *
 * @example
 * // 内存优化配置
 * const optimizedConfig = {
 *   position: { size: 3, data: new Float32Array([...]) },
 *   color: {
 *     size: 4,
 *     data: new Uint8Array([...]), // 75%内存节省
 *     normalized: true
 *   }
 * };
 */

// TODO: 支持变换反馈（transform feedback）

import { Vec3 } from '../math/Vec3.js';

// 临时向量，用于计算
const tempVec3 = /* @__PURE__ */ new Vec3();

// 几何体和属性的唯一ID计数器
let ID = 1;
let ATTR_ID = 1;

// 防止无限警告
let isBoundsWarned = false;

/**
 * WebGL几何体类 - 企业级顶点数据和渲染状态管理系统
 *
 * Geometry类是现代WebGL渲染管线的核心组件，提供完整的几何体数据管理、
 * GPU缓冲区优化和渲染状态管理功能。支持从简单的三角形到复杂的实例化
 * 渲染场景，是构建高性能3D应用的基础设施。
 *
 * 🏗️ **核心架构**：
 * - **VAO管理**：智能的顶点数组对象缓存和复用机制
 * - **缓冲区优化**：自动的VBO创建、更新和内存管理
 * - **状态管理**：WebGL状态的智能缓存和优化切换
 * - **实例化支持**：高性能批量渲染的完整实现
 * - **空间计算**：包围盒、包围球等空间查询功能
 *
 * 🚀 **性能特性**：
 * - VAO缓存减少状态切换开销高达90%
 * - 智能缓冲区更新策略优化GPU内存使用
 * - 实例化渲染支持百万级对象的高效绘制
 * - 自动内存管理防止GPU资源泄漏
 *
 * @class
 * @example
 * // 基础几何体创建
 * const triangle = new Geometry(gl, {
 *   position: {
 *     size: 3,
 *     data: new Float32Array([0, 1, 0, -1, -1, 0, 1, -1, 0])
 *   }
 * });
 *
 * @example
 * // 复杂几何体（带法线和纹理）
 * const cube = new Geometry(gl, {
 *   position: { size: 3, data: cubePositions },
 *   normal: { size: 3, data: cubeNormals },
 *   uv: { size: 2, data: cubeUVs },
 *   index: { data: cubeIndices }
 * });
 *
 * @example
 * // 实例化几何体（高性能批量渲染）
 * const instances = new Geometry(gl, {
 *   position: { size: 3, data: baseGeometry },
 *   instanceMatrix: {
 *     size: 16,
 *     instanced: 1,
 *     data: new Float32Array(1000 * 16),
 *     usage: gl.DYNAMIC_DRAW
 *   }
 * });
 */
export class Geometry {
    /**
     * 创建新的几何体实例 - WebGL几何体系统的入口点
     *
     * 构造函数执行完整的几何体初始化流程，包括WebGL上下文验证、
     * 内部数据结构创建、缓冲区分配和属性配置。采用延迟初始化
     * 策略优化性能，确保只在需要时创建GPU资源。
     *
     * 🔧 **初始化流程**：
     * 1. WebGL上下文验证和兼容性检查
     * 2. 内部数据结构和缓存系统初始化
     * 3. VAO管理器和状态跟踪器设置
     * 4. 属性缓冲区创建和数据上传
     * 5. 绘制参数计算和优化配置
     *
     * @param {WebGLRenderingContext|WebGL2RenderingContext} gl - WebGL渲染上下文
     *        必须是有效且当前活动的WebGL上下文。支持WebGL 1.0和2.0，
     *        自动检测版本并启用相应的优化特性。
     *
     *        ⚠️ **重要提示**：
     *        - 上下文必须未丢失（context lost）
     *        - 必须是当前活动的渲染上下文
     *        - 建议在上下文创建后立即使用
     *
     * @param {Object<string, AttributeConfig>} [attributes={}] - 几何体属性配置对象
     *        键值对集合，键为属性名称，值为AttributeConfig配置对象。
     *
     *        🎯 **常用属性名称**：
     *        - `'position'`: 顶点位置坐标（必需，除非仅用于计算）
     *        - `'normal'`: 顶点法线向量（光照计算必需）
     *        - `'uv'`: 纹理坐标（纹理映射必需）
     *        - `'color'`: 顶点颜色（顶点着色）
     *        - `'index'`: 索引数据（优化顶点复用）
     *        - `'tangent'`: 切线向量（法线贴图）
     *        - `'bitangent'`: 副切线向量（法线贴图）
     *        - `'weights'`: 骨骼权重（骨骼动画）
     *        - `'joints'`: 关节索引（骨骼动画）
     *
     *        🚀 **实例化属性**：
     *        - `'instanceMatrix'`: 实例变换矩阵
     *        - `'instanceColor'`: 实例颜色
     *        - `'instanceScale'`: 实例缩放
     *        - 任何带`instanced: 1`标记的自定义属性
     *
     * @throws {Error} 当WebGL上下文无效或已丢失时抛出错误
     * @throws {TypeError} 当属性配置格式不正确时抛出类型错误
     * @throws {RangeError} 当属性数据大小不匹配时抛出范围错误
     *
     * @see {@link AttributeConfig} 属性配置对象的详细说明
     * @see {@link https://www.khronos.org/webgl/wiki/Tutorial} WebGL教程
     *
     * @since 2.0.0
     *
     * @example
     * // 示例1: 创建一个简单的三角形几何体
     * const triangleGeometry = new Geometry(gl, {
     *     position: {
     *         size: 3,  // 每个顶点3个分量 (x, y, z)
     *         data: new Float32Array([
     *             0.0,  1.0, 0.0,  // 顶点1: 顶部
     *            -1.0, -1.0, 0.0,  // 顶点2: 左下
     *             1.0, -1.0, 0.0   // 顶点3: 右下
     *         ])
     *     }
     * });
     *
     * @example
     * // 示例2: 创建带纹理和颜色的四边形
     * const quadGeometry = new Geometry(gl, {
     *     // 顶点位置属性
     *     position: {
     *         size: 3,
     *         data: new Float32Array([
     *            -1.0,  1.0, 0.0,  // 左上
     *            -1.0, -1.0, 0.0,  // 左下
     *             1.0, -1.0, 0.0,  // 右下
     *             1.0,  1.0, 0.0   // 右上
     *         ])
     *     },
     *     // 纹理坐标属性
     *     uv: {
     *         size: 2,  // 每个顶点2个分量 (u, v)
     *         data: new Float32Array([
     *             0.0, 1.0,  // 左上
     *             0.0, 0.0,  // 左下
     *             1.0, 0.0,  // 右下
     *             1.0, 1.0   // 右上
     *         ])
     *     },
     *     // 顶点颜色属性 (使用字节数组节省内存)
     *     color: {
     *         size: 3,  // 每个顶点3个分量 (r, g, b)
     *         data: new Uint8Array([
     *             255, 0, 0,    // 红色
     *             0, 255, 0,    // 绿色
     *             0, 0, 255,    // 蓝色
     *             255, 255, 0   // 黄色
     *         ]),
     *         normalized: true  // 将0-255转换为0.0-1.0
     *     },
     *     // 索引属性 (定义三角形)
     *     index: {
     *         data: new Uint16Array([
     *             0, 1, 2,  // 第一个三角形
     *             0, 2, 3   // 第二个三角形
     *         ])
     *     }
     * });
     *
     * @example
     * // 示例3: 创建带法线的立方体 (用于光照计算)
     * const cubeGeometry = new Geometry(gl, {
     *     position: {
     *         size: 3,
     *         data: new Float32Array([...]) // 立方体顶点位置
     *     },
     *     normal: {
     *         size: 3,  // 每个顶点3个分量 (nx, ny, nz)
     *         data: new Float32Array([
     *             0, 0, 1,  // 前面法线
     *             0, 0, 1,
     *             0, 0, 1,
     *             0, 0, 1,
     *             // ... 其他面的法线
     *         ])
     *     },
     *     uv: {
     *         size: 2,
     *         data: new Float32Array([...]) // 纹理坐标
     *     },
     *     index: {
     *         data: new Uint16Array([...]) // 索引数据
     *     }
     * });
     *
     * @example
     * // 示例4: 创建实例化几何体 (高性能批量渲染)
     * const instancedGeometry = new Geometry(gl, {
     *     // 基础几何体
     *     position: {
     *         size: 3,
     *         data: new Float32Array([...]) // 基础形状的顶点
     *     },
     *     // 实例化属性: 每个实例的变换矩阵
     *     instanceMatrix: {
     *         size: 16,      // 4x4变换矩阵
     *         instanced: 1,  // 每个实例使用一次
     *         data: new Float32Array(instanceCount * 16) // 所有实例的矩阵
     *     },
     *     // 实例化属性: 每个实例的颜色
     *     instanceColor: {
     *         size: 3,       // RGB颜色
     *         instanced: 1,  // 每个实例使用一次
     *         data: new Float32Array(instanceCount * 3)
     *     }
     * });
     *
     * @example
     * // 示例5: 创建动态几何体 (可在运行时更新)
     * const dynamicGeometry = new Geometry(gl, {
     *     position: {
     *         size: 3,
     *         data: new Float32Array(vertexCount * 3),
     *         usage: gl.DYNAMIC_DRAW  // 标记为动态数据
     *     },
     *     color: {
     *         size: 4,  // RGBA颜色
     *         data: new Float32Array(vertexCount * 4),
     *         usage: gl.DYNAMIC_DRAW
     *     }
     * });
     *
     * @example
     * // 示例6: 使用预创建的缓冲区
     * const existingBuffer = gl.createBuffer();
     * gl.bindBuffer(gl.ARRAY_BUFFER, existingBuffer);
     * gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW);
     *
     * const geometryWithBuffer = new Geometry(gl, {
     *     position: {
     *         size: 3,
     *         buffer: existingBuffer,  // 使用预创建的缓冲区
     *         count: vertexCount,      // 手动指定顶点数量
     *         // 不需要提供data，因为缓冲区已经包含数据
     *     }
     * });
     *
     * @example
     * // 示例7: 交错数据布局 (所有属性在同一个缓冲区中)
     * const interleavedData = new Float32Array([
     *     // 顶点1: position(3) + normal(3) + uv(2)
     *     -1.0, -1.0, 0.0,  0.0, 0.0, 1.0,  0.0, 0.0,
     *     // 顶点2: position(3) + normal(3) + uv(2)
     *      1.0, -1.0, 0.0,  0.0, 0.0, 1.0,  1.0, 0.0,
     *     // 顶点3: position(3) + normal(3) + uv(2)
     *      0.0,  1.0, 0.0,  0.0, 0.0, 1.0,  0.5, 1.0
     * ]);
     *
     * const stride = 8 * 4; // 8个float，每个4字节
     * const interleavedGeometry = new Geometry(gl, {
     *     position: {
     *         size: 3,
     *         data: interleavedData,
     *         stride: stride,    // 顶点间的字节步长
     *         offset: 0          // 位置数据的偏移量
     *     },
     *     normal: {
     *         size: 3,
     *         data: interleavedData,
     *         stride: stride,
     *         offset: 3 * 4     // 法线数据的偏移量 (跳过3个position分量)
     *     },
     *     uv: {
     *         size: 2,
     *         data: interleavedData,
     *         stride: stride,
     *         offset: 6 * 4     // UV数据的偏移量 (跳过position+normal)
     *     }
     * });
     */
    constructor(gl, attributes = {}) {
        // ========================================
        // 第一阶段：WebGL上下文验证和兼容性检查
        // ========================================

        /**
         * 🔍 **关键安全检查**：验证WebGL上下文的有效性
         *
         * 检查传入的gl参数是否为有效的WebGL上下文。canvas属性是
         * WebGL上下文的标准属性，用于快速验证上下文的有效性。
         *
         * ⚠️ **常见错误场景**：
         * - 传入null或undefined
         * - 传入已丢失的WebGL上下文
         * - 传入非WebGL对象（如Canvas2D上下文）
         */
        if (!gl.canvas) console.error('gl not passed as first argument to Geometry');

        // ========================================
        // 第二阶段：核心属性初始化
        // ========================================

        /**
         * WebGL渲染上下文引用
         *
         * 存储WebGL上下文的引用，用于所有GPU操作。支持WebGL 1.0和2.0，
         * 自动适配不同版本的API差异。
         *
         * @type {WebGLRenderingContext|WebGL2RenderingContext}
         * @readonly
         */
        this.gl = gl;

        /**
         * 几何体属性集合
         *
         * 存储所有顶点属性的配置和数据。键为属性名称（如'position'、'normal'），
         * 值为AttributeConfig对象。这是几何体数据的核心存储结构。
         *
         * @type {Object<string, AttributeConfig>}
         */
        this.attributes = attributes;

        /**
         * 几何体唯一标识符
         *
         * 全局唯一的几何体ID，用于调试、性能分析和缓存管理。
         * 每个几何体实例都有不同的ID，便于追踪和识别。
         *
         * @type {number}
         * @readonly
         */
        this.id = ID++;

        // ========================================
        // 第三阶段：VAO管理系统初始化
        // ========================================

        /**
         * 顶点数组对象(VAO)缓存系统
         *
         * 🚀 **性能优化核心**：为每个着色器程序缓存对应的VAO
         *
         * **技术原理**：
         * - VAO记录了所有顶点属性的绑定状态
         * - 不同的着色器程序可能有不同的属性位置布局
         * - 缓存VAO避免重复的属性绑定操作，提升渲染性能
         *
         * **数据结构**：
         * - 键：program.attributeOrder（着色器属性位置的字符串表示）
         * - 值：对应的WebGLVertexArrayObject
         *
         * **性能影响**：
         * - 减少状态切换开销高达90%
         * - 特别适用于多着色器、多几何体的复杂场景
         *
         * @type {Object<string, WebGLVertexArrayObject>}
         * @private
         */
        this.VAOs = {};

        // ========================================
        // 第四阶段：渲染配置初始化
        // ========================================

        /**
         * 绘制范围配置对象
         *
         * 控制几何体的部分渲染，支持绘制几何体的子集。
         * 用于LOD（细节层次）、分块渲染、动画序列等高级功能。
         *
         * @type {Object}
         * @property {number} start - 起始索引或顶点位置（包含）
         * @property {number} count - 要绘制的索引或顶点数量
         *
         * @example
         * // 只绘制前100个三角形
         * geometry.setDrawRange(0, 300); // 100个三角形 × 3个顶点
         *
         * @example
         * // 跳过前50个顶点，绘制接下来的200个
         * geometry.setDrawRange(50, 200);
         */
        this.drawRange = { start: 0, count: 0 };

        /**
         * 实例化渲染实例数量
         *
         * 🔄 **批量渲染优化**：控制实例化渲染的实例数量
         *
         * - `0`: 标准渲染（非实例化）
         * - `>0`: 实例化渲染，指定实例数量
         *
         * **性能优势**：
         * - 将N次绘制调用合并为1次
         * - GPU并行处理所有实例
         * - 适用于粒子系统、草地、建筑群等场景
         *
         * @type {number}
         * @default 0
         */
        this.instancedCount = 0;

        /**
         * 实例化几何体标志
         *
         * 标记当前几何体是否包含实例化属性。
         * 影响绘制方法的选择（drawArrays vs drawArraysInstanced）。
         *
         * @type {boolean}
         * @default false
         */
        this.isInstanced = false;

        // ========================================
        // 第五阶段：WebGL状态管理和优化
        // ========================================

        /**
         * 🔧 **关键性能优化**：重置VAO绑定状态
         *
         * 解绑当前活动的VAO，确保新几何体的缓冲区创建不会
         * 意外地被记录到其他几何体的VAO中。
         *
         * **技术背景**：
         * - VAO会记录所有在其激活期间的缓冲区绑定操作
         * - 如果不解绑，新缓冲区可能被错误地关联到旧VAO
         * - 这会导致渲染错误和难以调试的状态污染问题
         *
         * **性能影响**：
         * - 防止GPU状态缓存失效
         * - 避免意外的状态绑定导致的渲染错误
         * - 确保每个几何体的VAO状态独立和正确
         */
        this.gl.renderer.bindVertexArray(null);
        this.gl.renderer.currentGeometry = null;

        /**
         * WebGL状态管理器引用
         *
         * 🚀 **渲染优化系统**：缓存WebGL状态以减少冗余操作
         *
         * **优化机制**：
         * - 跟踪当前绑定的缓冲区、纹理、着色器等状态
         * - 避免重复的状态切换操作
         * - 批量状态更新以提高效率
         *
         * **性能收益**：
         * - 减少GPU状态切换开销
         * - 提高复杂场景的渲染性能
         * - 特别适用于多对象、多材质的场景
         *
         * @type {Object}
         * @readonly
         */
        this.glState = this.gl.renderer.state;

        // ========================================
        // 第六阶段：属性缓冲区创建和初始化
        // ========================================

        /**
         * 🏗️ **批量属性处理**：为所有传入属性创建WebGL缓冲区
         *
         * 遍历构造函数参数中的所有属性配置，为每个属性：
         * 1. 验证配置的有效性
         * 2. 创建对应的WebGL缓冲区
         * 3. 上传数据到GPU内存
         * 4. 配置属性的绑定参数
         * 5. 更新几何体的绘制信息
         *
         * **处理顺序**：
         * - 先处理顶点属性（position、normal、uv等）
         * - 再处理索引属性（如果存在）
         * - 最后处理实例化属性
         *
         * **错误处理**：
         * - 自动跳过无效的属性配置
         * - 记录警告信息便于调试
         * - 确保部分失败不影响整体初始化
         */
        for (let key in attributes) {
            this.addAttribute(key, attributes[key]);
        }
    }

    /**
     * 添加几何体属性 - 动态属性管理的核心方法
     *
     * 🔧 **功能概述**：
     * 这是几何体属性管理的核心方法，负责将顶点数据转换为GPU可用的
     * WebGL缓冲区。支持各种数据类型、布局和优化策略，是构建复杂
     * 几何体的基础工具。
     *
     * 🚀 **处理流程**：
     * 1. **属性验证**：检查配置的有效性和兼容性
     * 2. **类型推断**：自动推断WebGL数据类型和缓冲区目标
     * 3. **参数配置**：设置默认值和优化参数
     * 4. **缓冲区创建**：创建WebGL缓冲区并上传数据
     * 5. **绘制信息更新**：更新几何体的渲染配置
     * 6. **实例化处理**：配置实例化渲染参数
     *
     * ⚡ **性能优化**：
     * - 智能类型推断减少配置复杂度
     * - 延迟缓冲区创建优化初始化性能
     * - 自动usage模式选择优化GPU内存使用
     * - 实例化属性的特殊处理和验证
     *
     * @param {string} key - 属性名称标识符
     *
     *        🎯 **标准属性名称**：
     *        - `'position'`: 顶点位置坐标（3D空间坐标）
     *        - `'normal'`: 顶点法线向量（光照计算必需）
     *        - `'uv'`: 纹理坐标（UV映射）
     *        - `'uv2'`: 第二套纹理坐标（光照贴图等）
     *        - `'color'`: 顶点颜色（RGB/RGBA）
     *        - `'index'`: 索引数据（优化顶点复用）
     *
     *        🔬 **高级属性**：
     *        - `'tangent'`: 切线向量（法线贴图、凹凸贴图）
     *        - `'bitangent'`: 副切线向量（完整TBN矩阵）
     *        - `'weights'`: 骨骼权重（骨骼动画）
     *        - `'joints'`: 关节索引（骨骼动画）
     *        - `'morphTarget0'`: 变形目标（面部动画）
     *
     *        🚀 **实例化属性**：
     *        - `'instanceMatrix'`: 实例变换矩阵（4x4）
     *        - `'instanceColor'`: 实例颜色
     *        - `'instanceScale'`: 实例缩放
     *        - `'instanceRotation'`: 实例旋转
     *        - 任何自定义实例化属性
     *
     * @param {AttributeConfig} attr - 属性配置对象
     *        完整的属性配置，包含数据、类型、布局等所有参数。
     *        详细配置选项请参考AttributeConfig类型定义。
     *
     * @throws {TypeError} 当属性配置格式不正确时
     * @throws {RangeError} 当数据大小与配置不匹配时
     * @throws {Error} 当WebGL缓冲区创建失败时
     *
     * @see {@link AttributeConfig} 完整的属性配置选项
     *
     * @example
     * // 基础顶点属性
     * geometry.addAttribute('position', {
     *     size: 3,
     *     data: new Float32Array([0, 1, 0, -1, -1, 0, 1, -1, 0])
     * });
     *
     * @example
     * // 纹理坐标（UV映射）
     * geometry.addAttribute('uv', {
     *     size: 2,
     *     data: new Float32Array([0.5, 1, 0, 0, 1, 0])
     * });
     *
     * @example
     * // 索引数据（优化顶点复用）
     * geometry.addAttribute('index', {
     *     data: new Uint16Array([0, 1, 2, 0, 2, 3])
     * });
     *
     * @example
     * // 实例化变换矩阵（批量渲染）
     * geometry.addAttribute('instanceMatrix', {
     *     size: 16,           // 4x4矩阵
     *     instanced: 1,       // 每个实例使用一次
     *     data: new Float32Array(instanceCount * 16),
     *     usage: gl.DYNAMIC_DRAW  // 动态更新
     * });
     *
     * @example
     * // 压缩颜色数据（内存优化）
     * geometry.addAttribute('color', {
     *     size: 4,            // RGBA
     *     data: new Uint8Array([255, 128, 0, 255]), // 橙色
     *     normalized: true    // 归一化到[0,1]
     * });
     *
     * @example
     * // 交错数据布局（性能优化）
     * const interleavedData = new Float32Array([
     *     // 顶点1: pos(3) + normal(3) + uv(2)
     *     -1, -1, 0,  0, 0, 1,  0, 0,
     *     // 顶点2: pos(3) + normal(3) + uv(2)
     *      1, -1, 0,  0, 0, 1,  1, 0,
     *     // 顶点3: pos(3) + normal(3) + uv(2)
     *      0,  1, 0,  0, 0, 1,  0.5, 1
     * ]);
     *
     * const stride = 8 * 4; // 8个float，每个4字节
     * geometry.addAttribute('position', {
     *     size: 3,
     *     data: interleavedData,
     *     stride: stride,
     *     offset: 0
     * });
     * geometry.addAttribute('normal', {
     *     size: 3,
     *     data: interleavedData,
     *     stride: stride,
     *     offset: 3 * 4  // 跳过position的3个float
     * });
     *
     * @since 2.0.0
     */
    addAttribute(key, attr) {
        // ========================================
        // 第一阶段：属性注册和基础配置
        // ========================================

        /**
         * 🔗 **属性注册**：将新属性添加到几何体的属性集合
         *
         * 这是属性管理的第一步，建立属性名称与配置对象的映射关系。
         * 后续的所有操作都基于这个注册的属性配置。
         */
        this.attributes[key] = attr;

        /**
         * 🏷️ **属性标识符分配**：为调试和追踪分配唯一ID
         *
         * 每个属性都有唯一的ID，便于调试、性能分析和错误追踪。
         * 注意：当前版本中ID主要用于调试，未来可能用于更多优化。
         */
        attr.id = ATTR_ID++;

        /**
         * 📏 **分量数量配置**：设置每个顶点的数据分量数
         *
         * 默认值为1，适用于标量属性（如密度、温度等）。
         * 常见配置：position(3), uv(2), color(3/4), matrix(16)
         */
        attr.size = attr.size || 1;

        // ========================================
        // 第二阶段：WebGL类型推断和缓冲区配置
        // ========================================

        /**
         * 🔍 **智能类型推断**：根据数据数组类型自动推断WebGL类型
         *
         * **自动映射规则**：
         * - Float32Array → gl.FLOAT (32位浮点数)
         * - Uint16Array → gl.UNSIGNED_SHORT (16位无符号整数)
         * - Uint32Array → gl.UNSIGNED_INT (32位无符号整数，默认)
         * - Int8Array → gl.BYTE (8位有符号整数)
         * - Uint8Array → gl.UNSIGNED_BYTE (8位无符号整数)
         * - Int16Array → gl.SHORT (16位有符号整数)
         *
         * **性能优化**：
         * - 自动推断减少配置复杂度
         * - 确保类型匹配，避免运行时错误
         * - 支持手动覆盖以满足特殊需求
         */
        attr.type =
            attr.type ||
            (attr.data.constructor === Float32Array
                ? this.gl.FLOAT
                : attr.data.constructor === Uint16Array
                ? this.gl.UNSIGNED_SHORT
                : attr.data.constructor === Uint8Array
                ? this.gl.UNSIGNED_BYTE
                : attr.data.constructor === Int8Array
                ? this.gl.BYTE
                : attr.data.constructor === Int16Array
                ? this.gl.SHORT
                : this.gl.UNSIGNED_INT); // 默认为Uint32Array

        /**
         * 🎯 **缓冲区目标确定**：选择正确的WebGL缓冲区类型
         *
         * **WebGL缓冲区类型**：
         * - ARRAY_BUFFER: 顶点属性数据（位置、法线、颜色、UV等）
         *   * 数据按顶点组织
         *   * 支持多种数据类型和布局
         *   * 可以交错或分离存储
         *
         * - ELEMENT_ARRAY_BUFFER: 索引数据
         *   * 存储顶点索引，指定绘制顺序
         *   * 优化顶点复用，减少内存使用
         *   * 通常使用Uint16Array或Uint32Array
         *
         * **选择逻辑**：
         * - 'index'属性 → ELEMENT_ARRAY_BUFFER
         * - 其他所有属性 → ARRAY_BUFFER
         */
        attr.target = key === 'index' ? this.gl.ELEMENT_ARRAY_BUFFER : this.gl.ARRAY_BUFFER;

        // ========================================
        // 第三阶段：属性参数默认值设置
        // ========================================

        /**
         * 📐 **数据归一化标志**：控制整数数据的归一化处理
         *
         * - false: 保持原始数值（默认）
         * - true: 整数值归一化到[0,1]或[-1,1]范围
         *
         * **使用场景**：
         * - 颜色数据：Uint8Array(0-255) → Float(0.0-1.0)
         * - 法线数据：Int8Array(-128-127) → Float(-1.0-1.0)
         * - 纹理坐标：Uint16Array(0-65535) → Float(0.0-1.0)
         */
        attr.normalized = attr.normalized || false;

        /**
         * 📦 **数据布局配置**：设置顶点间距和偏移量
         *
         * **stride（步长）**：
         * - 0: 紧密排列（默认）- 所有position连续，然后所有normal连续
         * - >0: 交错排列 - position+normal+uv为一组，然后下一个顶点
         *
         * **offset（偏移量）**：
         * - 0: 从缓冲区开始位置读取（默认）
         * - >0: 从指定字节位置开始读取
         *
         * **性能影响**：
         * - 交错布局提高缓存局部性
         * - 紧密布局简化数据管理
         */
        attr.stride = attr.stride || 0;
        attr.offset = attr.offset || 0;

        /**
         * 🧮 **顶点数量计算**：确定属性包含的顶点/索引数量
         *
         * **计算策略**：
         * 1. 手动指定：使用attr.count（优先级最高）
         * 2. 交错数据：data.byteLength / stride
         * 3. 紧密数据：data.length / size
         *
         * **应用场景**：
         * - 部分数据渲染：手动指定count < 实际数据量
         * - 动态LOD：根据距离调整count
         * - 流式加载：逐步增加count
         */
        attr.count = attr.count || (attr.stride ? attr.data.byteLength / attr.stride : attr.data.length / attr.size);

        /**
         * 🔄 **实例化配置**：设置实例化渲染参数
         *
         * **divisor值含义**：
         * - 0: 每个顶点使用一次（标准顶点属性）
         * - 1: 每个实例使用一次（实例化属性）
         * - N: 每N个实例使用一次（高级实例化）
         */
        attr.divisor = attr.instanced || 0;

        /**
         * 🔄 **更新标志**：标记缓冲区数据是否需要更新
         *
         * 用于延迟更新机制，只在实际绘制时才上传修改的数据。
         * 初始化时设为false，数据修改时设为true。
         */
        attr.needsUpdate = false;

        /**
         * ⚡ **使用模式提示**：告知GPU如何优化缓冲区存储
         *
         * **模式选择**：
         * - STATIC_DRAW: 数据创建后不再修改（默认，最优性能）
         * - DYNAMIC_DRAW: 数据会定期修改（如动画、变形）
         * - STREAM_DRAW: 数据每帧都会修改（如粒子系统）
         *
         * **GPU优化**：
         * - STATIC数据存储在高速显存中
         * - DYNAMIC数据使用可写缓存
         * - STREAM数据优化频繁更新
         */
        attr.usage = attr.usage || this.gl.STATIC_DRAW;

        // ========================================
        // 第四阶段：WebGL缓冲区创建和数据上传
        // ========================================

        /**
         * 🏗️ **缓冲区创建**：为属性创建WebGL缓冲区并上传数据
         *
         * 只有在没有预创建缓冲区时才执行创建操作。
         * 支持外部管理的缓冲区，用于高级内存管理和优化。
         */
        if (!attr.buffer) {
            this.updateAttribute(attr);
        }

        // ========================================
        // 第五阶段：几何体绘制信息更新
        // ========================================

        /**
         * 🎯 **绘制配置更新**：根据属性类型更新几何体的渲染参数
         *
         * 不同类型的属性对几何体的绘制行为有不同的影响：
         * - 实例化属性：影响实例数量和渲染方法
         * - 索引属性：影响绘制元素数量和索引模式
         * - 顶点属性：影响顶点数量和非索引模式
         */
        if (attr.divisor) {
            // 🚀 **实例化属性处理**
            this.isInstanced = true;

            /**
             * 🔍 **实例化一致性检查**：确保所有实例化属性的实例数量一致
             *
             * 当几何体包含多个实例化属性时，它们必须有相同的实例数量。
             * 不一致会导致渲染错误或GPU崩溃。
             *
             * **错误处理**：
             * - 检测不一致的实例数量
             * - 发出警告信息便于调试
             * - 使用较小的数量以避免越界访问
             */
            if (this.instancedCount && this.instancedCount !== attr.count * attr.divisor) {
                console.warn('geometry has multiple instanced buffers of different length');
                return (this.instancedCount = Math.min(this.instancedCount, attr.count * attr.divisor));
            }
            this.instancedCount = attr.count * attr.divisor;
        } else if (key === 'index') {
            // 📊 **索引属性处理**：索引数量决定绘制的图元数量
            this.drawRange.count = attr.count;
        } else if (!this.attributes.index) {
            // 📈 **顶点属性处理**：在非索引模式下，使用最大顶点数量
            this.drawRange.count = Math.max(this.drawRange.count, attr.count);
        }
    }

    /**
     * 更新属性缓冲区数据
     *
     * @param {Object} attr - 要更新的属性对象
     */
    updateAttribute(attr) {
        // 检查是否是新缓冲区
        const isNewBuffer = !attr.buffer;
        if (isNewBuffer) attr.buffer = this.gl.createBuffer();

        // 绑定缓冲区（如果尚未绑定）
        if (this.glState.boundBuffer !== attr.buffer) {
            this.gl.bindBuffer(attr.target, attr.buffer);
            this.glState.boundBuffer = attr.buffer;
        }

        // 上传数据到GPU
        if (isNewBuffer) {
            // 新缓冲区使用bufferData
            this.gl.bufferData(attr.target, attr.data, attr.usage);
        } else {
            // 现有缓冲区使用bufferSubData
            this.gl.bufferSubData(attr.target, 0, attr.data);
        }
        attr.needsUpdate = false;
    }

    /**
     * 设置索引属性
     *
     * @param {Object} value - 索引属性配置
     */
    setIndex(value) {
        this.addAttribute('index', value);
    }

    /**
     * 设置绘制范围
     *
     * @param {number} start - 起始索引
     * @param {number} count - 索引计数
     */
    setDrawRange(start, count) {
        this.drawRange.start = start;
        this.drawRange.count = count;
    }

    /**
     * 设置实例化计数
     *
     * @param {number} value - 实例化计数值
     */
    setInstancedCount(value) {
        this.instancedCount = value;
    }

    /**
     * 创建顶点数组对象(VAO)
     *
     * @param {Program} program - 着色器程序
     */
    createVAO(program) {
        // 为当前程序创建一个新的VAO
        this.VAOs[program.attributeOrder] = this.gl.renderer.createVertexArray();
        // 绑定VAO
        this.gl.renderer.bindVertexArray(this.VAOs[program.attributeOrder]);
        // 绑定所有属性
        this.bindAttributes(program);
    }

    /**
     * 绑定几何体属性到着色器程序
     *
     * @param {Program} program - 着色器程序
     */
    bindAttributes(program) {
        // 使用gl.vertexAttribPointer将所有属性链接到程序
        program.attributeLocations.forEach((location, { name, type }) => {
            // 如果几何体缺少着色器所需的属性
            if (!this.attributes[name]) {
                console.warn(`active attribute ${name} not being supplied`);
                return;
            }

            const attr = this.attributes[name];

            // 绑定属性缓冲区
            this.gl.bindBuffer(attr.target, attr.buffer);
            this.glState.boundBuffer = attr.buffer;

            // ═══════════════════════════════════════════════════════════════════════════════
            // 🔢 **矩阵属性的特殊处理**：WebGL中矩阵需要分解为多个顶点属性
            // ═══════════════════════════════════════════════════════════════════════════════
            //
            // 💡 **为什么矩阵需要特殊处理？**
            // WebGL的顶点属性系统有一个重要限制：单个顶点属性最多只能包含4个分量（vec4）。
            // 但矩阵通常包含更多数据：
            // • mat2: 2×2 = 4个分量 (可以用1个属性)
            // • mat3: 3×3 = 9个分量 (需要3个属性)
            // • mat4: 4×4 = 16个分量 (需要4个属性)
            //
            // 🎯 **GPU中的GLSL协同工作原理**：
            // 在GLSL着色器中声明：
            // ```glsl
            // attribute mat4 instanceMatrix;  // 在着色器中看起来是一个mat4
            // ```
            //
            // 但在WebGL中实际上被分解为4个vec4属性：
            // ```javascript
            // // 假设instanceMatrix的location是5，那么实际占用：
            // // location 5: instanceMatrix[0] (第1列)
            // // location 6: instanceMatrix[1] (第2列)
            // // location 7: instanceMatrix[2] (第3列)
            // // location 8: instanceMatrix[3] (第4列)
            // ```
            //
            // 📊 **内存布局示例**：
            // 对于一个4×4变换矩阵，数据在缓冲区中按列主序存储：
            // ```
            // Float32Array([
            //   // 矩阵1 (实例0)
            //   m00, m10, m20, m30,  // 第1列 -> location+0
            //   m01, m11, m21, m31,  // 第2列 -> location+1
            //   m02, m12, m22, m32,  // 第3列 -> location+2
            //   m03, m13, m23, m33,  // 第4列 -> location+3
            //   // 矩阵2 (实例1)
            //   ...
            // ])
            // ```

            let numLoc = 1;
            if (type === 35674) numLoc = 2; // GL_FLOAT_MAT2: 2×2矩阵，需要2个属性位置
            if (type === 35675) numLoc = 3; // GL_FLOAT_MAT3: 3×3矩阵，需要3个属性位置
            if (type === 35676) numLoc = 4; // GL_FLOAT_MAT4: 4×4矩阵，需要4个属性位置

            // 计算每个位置的大小、步长和偏移量
            // 这些计算是为了处理矩阵属性在WebGL中的特殊存储方式

            /**
             * size: 每个属性位置的分量数
             *
             * 示例解释：
             * - 对于vec3属性: attr.size = 3, numLoc = 1 → size = 3/1 = 3 (每个位置3个分量)
             * - 对于mat4属性: attr.size = 16, numLoc = 4 → size = 16/4 = 4 (每个位置4个分量)
             *
             * 原因：WebGL中矩阵必须按列分解为多个vec4属性
             * mat4矩阵有16个元素，但WebGL一个属性位置最多只能处理4个分量
             * 所以mat4需要分解为4个vec4，每个vec4占用一个属性位置
             */
            const size = attr.size / numLoc;

            /**
             * stride: 从一个顶点的属性开始到下一个顶点相同属性开始的字节距离
             *
             * 示例解释：
             * - 对于普通属性(numLoc=1): stride = 0 (使用attr.stride)
             * - 对于mat2属性(numLoc=2): stride = 2*2*4 = 16字节 (2x2矩阵，每个float 4字节)
             * - 对于mat3属性(numLoc=3): stride = 3*3*4 = 36字节 (3x3矩阵，每个float 4字节)
             * - 对于mat4属性(numLoc=4): stride = 4*4*4 = 64字节 (4x4矩阵，每个float 4字节)
             *
             * 原因：矩阵数据在缓冲区中是连续存储的，需要跳过整个矩阵的大小才能到达下一个顶点
             */
            const stride = numLoc === 1 ? 0 : numLoc * numLoc * 4;

            /**
             * offset: 在单个顶点数据中，从第一列到下一列的字节偏移
             *
             * 示例解释：
             * - 对于普通属性(numLoc=1): offset = 0 (只有一个位置，无需偏移)
             * - 对于mat2属性(numLoc=2): offset = 2*4 = 8字节 (每列2个float，每个4字节)
             * - 对于mat3属性(numLoc=3): offset = 3*4 = 12字节 (每列3个float，每个4字节)
             * - 对于mat4属性(numLoc=4): offset = 4*4 = 16字节 (每列4个float，每个4字节)
             *
             * 原因：矩阵按列主序存储，每列作为一个vec存储，需要偏移到下一列的起始位置
             *
             * 内存布局示例(mat4):
             * 顶点0: [col0: m00,m10,m20,m30] [col1: m01,m11,m21,m31] [col2: m02,m12,m22,m32] [col3: m03,m13,m23,m33]
             * 顶点1: [col0: m00,m10,m20,m30] [col1: m01,m11,m21,m31] [col2: m02,m12,m22,m32] [col3: m03,m13,m23,m33]
             *        ↑offset=0            ↑offset=16           ↑offset=32           ↑offset=48
             */
            const offset = numLoc === 1 ? 0 : numLoc * 4;

            // 为每个位置设置属性指针
            // 对于矩阵属性，需要为每一列设置一个独立的属性指针
            for (let i = 0; i < numLoc; i++) {
                /**
                 * vertexAttribPointer调用示例：
                 *
                 * 假设有一个mat4属性，location=5，则会进行4次调用：
                 * - i=0: location=5, size=4, stride=64, offset=0  (第1列)
                 * - i=1: location=6, size=4, stride=64, offset=16 (第2列)
                 * - i=2: location=7, size=4, stride=64, offset=32 (第3列)
                 * - i=3: location=8, size=4, stride=64, offset=48 (第4列)
                 *
                 * 这样mat4矩阵就被分解为4个vec4属性，分别占用4个连续的属性位置
                 */
                this.gl.vertexAttribPointer(
                    location + i, // 属性位置：location, location+1, location+2, location+3
                    size, // 每个顶点的分量数：对于mat4是4，对于mat3是3
                    attr.type, // 数据类型：通常是gl.FLOAT
                    attr.normalized, // 是否归一化：矩阵通常为false
                    attr.stride + stride, // 步长：原始步长 + 矩阵步长(如mat4为64字节)，通过顶点的索引乘这个获取从哪里开始获取数据，然后根据偏移量获取对应的数据
                    attr.offset + i * offset // 偏移量：原始偏移 + 列偏移(如mat4每列16字节)
                );

                // ✅ **启用顶点属性**：激活这个属性位置
                this.gl.enableVertexAttribArray(location + i);

                // 🔄 **设置实例化除数**：控制属性的更新频率
                // divisor=0: 每个顶点更新一次 (标准顶点属性)
                // divisor=1: 每个实例更新一次 (实例化属性)
                // divisor=N: 每N个实例更新一次 (高级实例化)
                //
                // 🦊 **Firefox兼容性注意**：
                // Firefox在实例化绘制后进行非实例化绘制时，需要将除数重置为0，
                // 否则可能导致渲染失败。这是浏览器实现差异导致的问题。
                this.gl.renderer.vertexAttribDivisor(location + i, attr.divisor);
            }

            // 💡 **实际应用示例**：
            // 假设你有100个立方体实例，每个都有不同的变换矩阵：
            //
            // JavaScript端：
            // ```javascript
            // const instanceMatrices = new Float32Array(100 * 16); // 100个4x4矩阵
            // geometry.addAttribute('instanceMatrix', {
            //     size: 16,        // 16个分量 (4x4矩阵)
            //     instanced: 1,    // 每个实例使用一次
            //     data: instanceMatrices
            // });
            // ```
            //
            // GLSL顶点着色器端：
            // ```glsl
            // attribute vec3 position;        // 基础几何体顶点
            // attribute mat4 instanceMatrix;  // 实例变换矩阵
            //
            // uniform mat4 projectionMatrix;
            // uniform mat4 viewMatrix;
            //
            // void main() {
            //     // 应用实例变换：先变换到世界空间
            //     vec4 worldPosition = instanceMatrix * vec4(position, 1.0);
            //
            //     // 再应用相机变换
            //     gl_Position = projectionMatrix * viewMatrix * worldPosition;
            // }
            // ```
            //
            // 🚀 **性能优势**：
            // 这种方式允许用一次绘制调用渲染100个不同变换的立方体，
            // 相比传统方式需要100次绘制调用，性能提升巨大！
        });

        // 如果几何体有索引，则绑定索引缓冲区
        if (this.attributes.index) this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.attributes.index.buffer);
    }

    /**
     * 绘制几何体
     *
     * @param {Object} options - 绘制选项
     * @param {Program} options.program - 用于绘制的着色器程序
     * @param {number} [options.mode=this.gl.TRIANGLES] - 绘制模式，如三角形、线条等
     */
    draw({ program, mode = this.gl.TRIANGLES }) {
        // 检查是否需要绑定不同的VAO
        if (this.gl.renderer.currentGeometry !== `${this.id}_${program.attributeOrder}`) {
            // 如果当前程序没有VAO，则创建一个
            if (!this.VAOs[program.attributeOrder]) this.createVAO(program);
            // 绑定VAO
            this.gl.renderer.bindVertexArray(this.VAOs[program.attributeOrder]);
            // 更新当前几何体标识
            this.gl.renderer.currentGeometry = `${this.id}_${program.attributeOrder}`;
        }

        // 检查是否有属性需要更新
        program.attributeLocations.forEach((location, { name }) => {
            const attr = this.attributes[name];
            if (attr.needsUpdate) this.updateAttribute(attr);
        });

        // 对于drawElements，偏移量需要是类型大小的倍数
        let indexBytesPerElement = 2; // 默认为UNSIGNED_SHORT (2字节)
        if (this.attributes.index?.type === this.gl.UNSIGNED_INT) indexBytesPerElement = 4; // UNSIGNED_INT (4字节)

        // 根据是否为实例化几何体选择不同的绘制方法
        if (this.isInstanced) {
            if (this.attributes.index) {
                // 绘制实例化索引几何体
                //对应关系
                //this.drawRange.count > this.drawRange.count
                // this.attributes.index.offset + this.drawRange.start * indexBytesPerElement > this.drawRange.start
                this.gl.renderer.drawElementsInstanced(
                    mode, // 绘制模式
                    this.drawRange.count, // 索引计数
                    this.attributes.index.type, // 索引类型
                    this.attributes.index.offset + this.drawRange.start * indexBytesPerElement, // 偏移量
                    this.instancedCount // 实例计数
                );
            } else {
                // 绘制实例化非索引几何体
                this.gl.renderer.drawArraysInstanced(mode, this.drawRange.start, this.drawRange.count, this.instancedCount);
            }
        } else {
            if (this.attributes.index) {
                // 绘制索引几何体
                this.gl.drawElements(
                    mode, // 绘制模式
                    this.drawRange.count, // 索引计数
                    this.attributes.index.type, // 索引类型
                    this.attributes.index.offset + this.drawRange.start * indexBytesPerElement // 偏移量
                );
            } else {
                // 绘制非索引几何体
                this.gl.drawArrays(mode, this.drawRange.start, this.drawRange.count);
            }
        }
    }

    /**
     * 获取位置属性
     *
     * @returns {Object|boolean} 位置属性对象或警告标志
     */
    getPosition() {
        // 使用位置缓冲区，或者如果可用则使用min/max
        const attr = this.attributes.position;
        // if (attr.min) return [...attr.min, ...attr.max];
        if (attr.data) return attr;
        // 防止重复警告
        if (isBoundsWarned) return;
        console.warn('No position buffer data found to compute bounds');
        return (isBoundsWarned = true);
    }

    /**
     * 计算几何体的包围盒
     *
     * @param {Object} [attr] - 位置属性，如果未提供则使用getPosition()获取
     */
    computeBoundingBox(attr) {
        // 如果未提供属性，则获取位置属性
        if (!attr) attr = this.getPosition();
        const array = attr.data;
        // 加载的数据不应该有步长，只有缓冲区才有
        // const stride = attr.stride ? attr.stride / array.BYTES_PER_ELEMENT : attr.size;
        const stride = attr.size;

        // 如果没有边界对象，则创建一个
        if (!this.bounds) {
            this.bounds = {
                min: new Vec3(), // 最小点
                max: new Vec3(), // 最大点
                center: new Vec3(), // 中心点
                scale: new Vec3(), // 缩放（尺寸）
                radius: Infinity, // 包围球半径
            };
        }

        // 获取引用以便于访问
        const min = this.bounds.min;
        const max = this.bounds.max;
        const center = this.bounds.center;
        const scale = this.bounds.scale;

        // 初始化最小值和最大值
        min.set(+Infinity);
        max.set(-Infinity);

        // TODO: 检查位置的大小（例如使用Vec2的三角形）
        // 遍历所有顶点，找出最小和最大坐标
        for (let i = 0, l = array.length; i < l; i += stride) {
            const x = array[i];
            const y = array[i + 1];
            const z = array[i + 2];

            // 更新最小值
            min.x = Math.min(x, min.x);
            min.y = Math.min(y, min.y);
            min.z = Math.min(z, min.z);

            // 更新最大值
            max.x = Math.max(x, max.x);
            max.y = Math.max(y, max.y);
            max.z = Math.max(z, max.z);
        }

        // 计算包围盒的尺寸和中心点
        scale.sub(max, min); // 尺寸 = 最大值 - 最小值
        center.add(min, max).divide(2); // 中心点 = (最小值 + 最大值) / 2
    }

    /**
     * 计算几何体的包围球
     *
     * @param {Object} [attr] - 位置属性，如果未提供则使用getPosition()获取
     */
    computeBoundingSphere(attr) {
        // 如果未提供属性，则获取位置属性
        if (!attr) attr = this.getPosition();
        const array = attr.data;
        // 加载的数据不应该有步长，只有缓冲区才有
        // const stride = attr.stride ? attr.stride / array.BYTES_PER_ELEMENT : attr.size;
        const stride = attr.size;

        // 如果没有边界对象，则先计算包围盒
        if (!this.bounds) this.computeBoundingBox(attr);

        // 找出距离中心点最远的顶点
        let maxRadiusSq = 0; // 最大半径的平方
        for (let i = 0, l = array.length; i < l; i += stride) {
            // 将顶点坐标加载到临时向量
            tempVec3.fromArray(array, i);
            // 计算顶点到中心点的距离的平方，并更新最大值
            maxRadiusSq = Math.max(maxRadiusSq, this.bounds.center.squaredDistance(tempVec3));
        }

        // 计算包围球半径（平方根）
        this.bounds.radius = Math.sqrt(maxRadiusSq);
    }

    /**
     * 删除几何体及其所有资源
     *
     * 清理VAO和缓冲区，释放GPU资源。
     * 在几何体不再使用时调用此方法以避免内存泄漏。
     *
     * @example
     * // 在几何体不再需要时清理资源
     * geometry.remove();
     */
    remove() {
        // 删除所有顶点数组对象
        for (let key in this.VAOs) {
            this.gl.renderer.deleteVertexArray(this.VAOs[key]);
            delete this.VAOs[key];
        }
        // 删除所有属性缓冲区
        for (let key in this.attributes) {
            this.gl.deleteBuffer(this.attributes[key].buffer);
            delete this.attributes[key];
        }
    }
}

/**
 * ========================================
 * 使用示例和演示数据
 * ========================================
 *
 * 以下是Geometry类的完整使用示例，展示了如何创建各种类型的几何体
 * 以及如何使用不同的属性配置。
 */

/**
 * 示例1: 创建一个简单的三角形几何体
 *
 * 这是最基本的几何体示例，包含位置和颜色属性。
 * 适用于学习WebGL基础和简单的2D/3D图形渲染。
 */
export function createTriangleGeometry(gl) {
    // 定义三角形的顶点位置（3个顶点，每个顶点3个分量：x, y, z）
    const positions = new Float32Array([
        0.0,
        0.8,
        0.0, // 顶点1：顶部
        -0.7,
        -0.4,
        0.0, // 顶点2：左下
        0.7,
        -0.4,
        0.0, // 顶点3：右下
    ]);

    // 定义每个顶点的颜色（RGB格式，值范围0-1）
    const colors = new Float32Array([
        1.0,
        0.0,
        0.0, // 顶点1：红色
        0.0,
        1.0,
        0.0, // 顶点2：绿色
        0.0,
        0.0,
        1.0, // 顶点3：蓝色
    ]);

    // 创建几何体并添加属性
    const geometry = new Geometry(gl, {
        // 位置属性：每个顶点3个分量（x, y, z）
        position: {
            size: 3,
            data: positions,
        },
        // 颜色属性：每个顶点3个分量（r, g, b）
        color: {
            size: 3,
            data: colors,
        },
    });

    return geometry;
}

/**
 * 示例2: 创建一个带纹理坐标的四边形几何体
 *
 * 展示如何使用索引绘制和纹理坐标。
 * 适用于纹理映射和UI元素渲染。
 */
export function createQuadGeometry(gl) {
    // 四边形的4个顶点位置
    const positions = new Float32Array([
        -1.0,
        1.0,
        0.0, // 左上
        -1.0,
        -1.0,
        0.0, // 左下
        1.0,
        -1.0,
        0.0, // 右下
        1.0,
        1.0,
        0.0, // 右上
    ]);

    // 纹理坐标（UV坐标，范围0-1）
    const uvs = new Float32Array([
        0.0,
        1.0, // 左上
        0.0,
        0.0, // 左下
        1.0,
        0.0, // 右下
        1.0,
        1.0, // 右上
    ]);

    // 索引数组：定义两个三角形组成四边形
    const indices = new Uint16Array([
        0,
        1,
        2, // 第一个三角形
        0,
        2,
        3, // 第二个三角形
    ]);

    const geometry = new Geometry(gl, {
        position: {
            size: 3,
            data: positions,
        },
        uv: {
            size: 2,
            data: uvs,
        },
        index: {
            data: indices,
        },
    });

    return geometry;
}

/**
 * 示例3: 创建一个立方体几何体
 *
 * 展示更复杂的几何体，包含位置、法线和纹理坐标。
 * 适用于3D物体渲染和光照计算。
 */
export function createCubeGeometry(gl) {
    // 立方体的8个顶点位置
    const positions = new Float32Array([
        // 前面
        -1,
        -1,
        1, // 0: 左下前
        1,
        -1,
        1, // 1: 右下前
        1,
        1,
        1, // 2: 右上前
        -1,
        1,
        1, // 3: 左上前

        // 后面
        -1,
        -1,
        -1, // 4: 左下后
        -1,
        1,
        -1, // 5: 左上后
        1,
        1,
        -1, // 6: 右上后
        1,
        -1,
        -1, // 7: 右下后

        // 上面
        -1,
        1,
        -1, // 8: 左上后
        -1,
        1,
        1, // 9: 左上前
        1,
        1,
        1, // 10: 右上前
        1,
        1,
        -1, // 11: 右上后

        // 下面
        -1,
        -1,
        -1, // 12: 左下后
        1,
        -1,
        -1, // 13: 右下后
        1,
        -1,
        1, // 14: 右下前
        -1,
        -1,
        1, // 15: 左下前

        // 右面
        1,
        -1,
        -1, // 16: 右下后
        1,
        1,
        -1, // 17: 右上后
        1,
        1,
        1, // 18: 右上前
        1,
        -1,
        1, // 19: 右下前

        // 左面
        -1,
        -1,
        -1, // 20: 左下后
        -1,
        -1,
        1, // 21: 左下前
        -1,
        1,
        1, // 22: 左上前
        -1,
        1,
        -1, // 23: 左上后
    ]);

    // 每个面的法线向量
    const normals = new Float32Array([
        // 前面 (z = 1)
        0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1,
        // 后面 (z = -1)
        0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1,
        // 上面 (y = 1)
        0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0,
        // 下面 (y = -1)
        0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1, 0,
        // 右面 (x = 1)
        1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0,
        // 左面 (x = -1)
        -1, 0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0,
    ]);

    // 纹理坐标（每个面使用完整的纹理）
    const uvs = new Float32Array([
        // 前面
        0, 0, 1, 0, 1, 1, 0, 1,
        // 后面
        1, 0, 1, 1, 0, 1, 0, 0,
        // 上面
        0, 1, 0, 0, 1, 0, 1, 1,
        // 下面
        1, 1, 0, 1, 0, 0, 1, 0,
        // 右面
        1, 0, 1, 1, 0, 1, 0, 0,
        // 左面
        0, 0, 1, 0, 1, 1, 0, 1,
    ]);

    // 索引数组：每个面2个三角形
    const indices = new Uint16Array([
        0,
        1,
        2,
        0,
        2,
        3, // 前面
        4,
        5,
        6,
        4,
        6,
        7, // 后面
        8,
        9,
        10,
        8,
        10,
        11, // 上面
        12,
        13,
        14,
        12,
        14,
        15, // 下面
        16,
        17,
        18,
        16,
        18,
        19, // 右面
        20,
        21,
        22,
        20,
        22,
        23, // 左面
    ]);

    const geometry = new Geometry(gl, {
        position: {
            size: 3,
            data: positions,
        },
        normal: {
            size: 3,
            data: normals,
        },
        uv: {
            size: 2,
            data: uvs,
        },
        index: {
            data: indices,
        },
    });

    return geometry;
}

/**
 * 示例4: 创建实例化几何体
 *
 * 展示如何使用实例化渲染来高效绘制大量相同的几何体。
 * 适用于粒子系统、草地渲染、建筑群等场景。
 */
export function createInstancedQuadGeometry(gl, instanceCount = 100) {
    // 基础四边形几何体
    const positions = new Float32Array([
        -0.1,
        0.1,
        0.0, // 左上
        -0.1,
        -0.1,
        0.0, // 左下
        0.1,
        -0.1,
        0.0, // 右下
        0.1,
        0.1,
        0.0, // 右上
    ]);

    const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);

    // 为每个实例生成随机的变换矩阵
    const instanceMatrices = new Float32Array(instanceCount * 16);
    const instanceColors = new Float32Array(instanceCount * 3);

    for (let i = 0; i < instanceCount; i++) {
        const offset = i * 16;
        const colorOffset = i * 3;

        // 随机位置
        const x = (Math.random() - 0.5) * 10;
        const y = (Math.random() - 0.5) * 10;
        const z = (Math.random() - 0.5) * 10;

        // 随机缩放
        const scale = 0.5 + Math.random() * 1.5;

        // 创建变换矩阵（简化的4x4单位矩阵加上位置和缩放）
        instanceMatrices[offset + 0] = scale; // m00
        instanceMatrices[offset + 1] = 0; // m01
        instanceMatrices[offset + 2] = 0; // m02
        instanceMatrices[offset + 3] = 0; // m03
        instanceMatrices[offset + 4] = 0; // m10
        instanceMatrices[offset + 5] = scale; // m11
        instanceMatrices[offset + 6] = 0; // m12
        instanceMatrices[offset + 7] = 0; // m13
        instanceMatrices[offset + 8] = 0; // m20
        instanceMatrices[offset + 9] = 0; // m21
        instanceMatrices[offset + 10] = scale; // m22
        instanceMatrices[offset + 11] = 0; // m23
        instanceMatrices[offset + 12] = x; // m30 (translation x)
        instanceMatrices[offset + 13] = y; // m31 (translation y)
        instanceMatrices[offset + 14] = z; // m32 (translation z)
        instanceMatrices[offset + 15] = 1; // m33

        // 随机颜色
        instanceColors[colorOffset + 0] = Math.random(); // r
        instanceColors[colorOffset + 1] = Math.random(); // g
        instanceColors[colorOffset + 2] = Math.random(); // b
    }

    const geometry = new Geometry(gl, {
        position: {
            size: 3,
            data: positions,
        },
        index: {
            data: indices,
        },
        // 实例化属性：变换矩阵
        instanceMatrix: {
            size: 16,
            instanced: 1, // 每个实例使用一次
            data: instanceMatrices,
        },
        // 实例化属性：颜色
        instanceColor: {
            size: 3,
            instanced: 1, // 每个实例使用一次
            data: instanceColors,
        },
    });

    return geometry;
}

/**
 * 示例5: 动态更新几何体数据
 *
 * 展示如何在运行时更新几何体的顶点数据。
 * 适用于动画、变形、实时生成的几何体等。
 */
export function createDynamicGeometry(gl) {
    const vertexCount = 100;
    const positions = new Float32Array(vertexCount * 3);
    const colors = new Float32Array(vertexCount * 3);

    // 初始化为圆形排列的点
    for (let i = 0; i < vertexCount; i++) {
        const angle = (i / vertexCount) * Math.PI * 2;
        const radius = 2;

        positions[i * 3 + 0] = Math.cos(angle) * radius; // x
        positions[i * 3 + 1] = Math.sin(angle) * radius; // y
        positions[i * 3 + 2] = 0; // z

        // 基于角度的颜色
        colors[i * 3 + 0] = (Math.cos(angle) + 1) * 0.5; // r
        colors[i * 3 + 1] = (Math.sin(angle) + 1) * 0.5; // g
        colors[i * 3 + 2] = 0.5; // b
    }

    const geometry = new Geometry(gl, {
        position: {
            size: 3,
            data: positions,
            usage: gl.DYNAMIC_DRAW, // 标记为动态数据
        },
        color: {
            size: 3,
            data: colors,
            usage: gl.DYNAMIC_DRAW,
        },
    });

    // 添加更新方法
    geometry.updateWave = function (time) {
        const positions = this.attributes.position.data;
        const colors = this.attributes.color.data;

        for (let i = 0; i < vertexCount; i++) {
            const angle = (i / vertexCount) * Math.PI * 2;
            const baseRadius = 2;
            const waveRadius = baseRadius + Math.sin(time * 0.01 + angle * 3) * 0.5;

            positions[i * 3 + 0] = Math.cos(angle) * waveRadius;
            positions[i * 3 + 1] = Math.sin(angle) * waveRadius;

            // 动态颜色
            colors[i * 3 + 0] = (Math.sin(time * 0.005 + angle) + 1) * 0.5;
            colors[i * 3 + 1] = (Math.cos(time * 0.007 + angle) + 1) * 0.5;
            colors[i * 3 + 2] = (Math.sin(time * 0.003) + 1) * 0.5;
        }

        // 标记需要更新
        this.attributes.position.needsUpdate = true;
        this.attributes.color.needsUpdate = true;
    };

    return geometry;
}

/**
 * 使用示例总结：
 *
 * 1. 基础几何体：createTriangleGeometry() - 学习基础概念
 * 2. 索引几何体：createQuadGeometry() - 优化顶点数据
 * 3. 复杂几何体：createCubeGeometry() - 3D渲染和光照
 * 4. 实例化渲染：createInstancedQuadGeometry() - 高性能批量渲染
 * 5. 动态几何体：createDynamicGeometry() - 实时数据更新
 *
 * 这些示例涵盖了Geometry类的主要使用场景，
 * 可以作为开发各种3D应用的基础模板。
 */
