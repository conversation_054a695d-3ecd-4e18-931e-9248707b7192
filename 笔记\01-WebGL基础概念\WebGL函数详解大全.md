# WebGL 函数详解大全

═══════════════════════════════════════════════════════════════════════════════

## 目录索引

### 快速导航

-   [学习路径指南](#学习路径指南)
-   [WebGL 1.0 核心函数](#webgl-10-核心函数)
    -   [上下文创建与管理](#上下文创建与管理)
    -   [着色器管理](#着色器管理)
    -   [着色器程序管理](#着色器程序管理)
    -   [缓冲区操作](#缓冲区操作)
    -   [顶点数组对象 (VAO)](#顶点数组对象-vao---webgl-20)
    -   [顶点属性管理](#顶点属性管理)
    -   [纹理处理](#纹理处理)
-   [WebGL 2.0 新增函数](#webgl-20-新增函数)
-   [WebGL 扩展函数](#webgl-扩展函数)
-   [性能优化指南](#性能优化指南)
-   [常见错误和注意事项](#常见错误和注意事项)
-   [实用代码示例](#实用代码示例)

───────────────────────────────────────────────────────────────────────────────

### 功能分类索引

#### 1. 上下文创建与管理

-   [getContext()](#getcontext) - 获取 WebGL 渲染上下文
-   [getContextAttributes()](#getcontextattributes) - 获取上下文属性
-   [isContextLost()](#iscontextlost) - 检查上下文是否丢失

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 2. 着色器管理

-   [createShader()](#createshader) - 创建着色器对象
-   [shaderSource()](#shadersource) - 设置着色器源代码
-   [compileShader()](#compileshader) - 编译着色器
-   [getShaderParameter()](#getshaderparameter) - 获取着色器参数
-   [getShaderInfoLog()](#getshaderinfolog) - 获取着色器编译日志
-   [getShaderSource()](#getshadersource) - 获取着色器源代码
-   [isShader()](#isshader) - 检查对象是否为着色器
-   [deleteShader()](#deleteshader) - 删除着色器对象
-   [createProgram()](#createprogram) - 创建着色器程序
-   [attachShader()](#attachshader) - 附加着色器到程序
-   [detachShader()](#detachshader) - 从程序分离着色器
-   [linkProgram()](#linkprogram) - 链接着色器程序
-   [getProgramParameter()](#getprogramparameter) - 获取程序参数
-   [getProgramInfoLog()](#getprograminfolog) - 获取程序链接日志
-   [validateProgram()](#validateprogram) - 验证着色器程序
-   [useProgram()](#useprogram) - 使用着色器程序
-   [isProgram()](#isprogram) - 检查对象是否为程序
-   [deleteProgram()](#deleteprogram) - 删除着色器程序

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 3. 缓冲区操作

-   [createBuffer()](#createbuffer) - 创建缓冲区对象
-   [bindBuffer()](#bindbuffer) - 绑定缓冲区
-   [bufferData()](#bufferdata) - 向缓冲区传输数据
-   [bufferSubData()](#buffersubdata) - 更新缓冲区子数据
-   [getBufferParameter()](#getbufferparameter) - 获取缓冲区参数
-   [isBuffer()](#isbuffer) - 检查对象是否为缓冲区
-   [deleteBuffer()](#deletebuffer) - 删除缓冲区
-   [createVertexArray()](#createvertexarray) - 创建顶点数组对象 (WebGL 2.0)
-   [bindVertexArray()](#bindvertexarray) - 绑定顶点数组对象 (WebGL 2.0)
-   [isVertexArray()](#isvertexarray) - 检查对象是否为 VAO (WebGL 2.0)
-   [deleteVertexArray()](#deletevertexarray) - 删除顶点数组对象 (WebGL 2.0)

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 4. 顶点属性管理

-   [getAttribLocation()](#getattriblocation) - 获取顶点属性位置
-   [enableVertexAttribArray()](#enablevertexattribarray) - 启用顶点属性数组
-   [disableVertexAttribArray()](#disablevertexattribarray) - 禁用顶点属性数组
-   [vertexAttribPointer()](#vertexattribpointer) - 设置顶点属性指针
-   [vertexAttribIPointer()](#vertexattribipointer) - 设置整数顶点属性指针 (WebGL 2.0)
-   [vertexAttrib1f()](#vertexattrib1f) - 设置单个浮点顶点属性
-   [vertexAttrib2f()](#vertexattrib2f) - 设置 2 个浮点顶点属性
-   [vertexAttrib3f()](#vertexattrib3f) - 设置 3 个浮点顶点属性
-   [vertexAttrib4f()](#vertexattrib4f) - 设置 4 个浮点顶点属性
-   [vertexAttrib1fv()](#vertexattrib1fv) - 设置单个浮点顶点属性(数组)
-   [vertexAttrib2fv()](#vertexattrib2fv) - 设置 2 个浮点顶点属性(数组)
-   [vertexAttrib3fv()](#vertexattrib3fv) - 设置 3 个浮点顶点属性(数组)
-   [vertexAttrib4fv()](#vertexattrib4fv) - 设置 4 个浮点顶点属性(数组)
-   [vertexAttribI4i()](#vertexattribi4i) - 设置 4 个整数顶点属性 (WebGL 2.0)
-   [vertexAttribI4ui()](#vertexattribi4ui) - 设置 4 个无符号整数顶点属性 (WebGL 2.0)
-   [vertexAttribDivisor()](#vertexattribdivisor) - 设置顶点属性除数 (WebGL 2.0)
-   [getVertexAttrib()](#getvertexattrib) - 获取顶点属性信息
-   [getVertexAttribOffset()](#getvertexattriboffset) - 获取顶点属性偏移量

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 5. Uniform 变量管理

-   [getUniformLocation()](#getuniformlocation) - 获取 uniform 变量位置
-   [uniform1f()](#uniform1f) - 设置单个浮点 uniform
-   [uniform2f()](#uniform2f) - 设置 2 个浮点 uniform
-   [uniform3f()](#uniform3f) - 设置 3 个浮点 uniform
-   [uniform4f()](#uniform4f) - 设置 4 个浮点 uniform
-   [uniform1i()](#uniform1i) - 设置单个整数 uniform
-   [uniform2i()](#uniform2i) - 设置 2 个整数 uniform
-   [uniform3i()](#uniform3i) - 设置 3 个整数 uniform
-   [uniform4i()](#uniform4i) - 设置 4 个整数 uniform
-   [uniform1ui()](#uniform1ui) - 设置单个无符号整数 uniform (WebGL 2.0)
-   [uniform2ui()](#uniform2ui) - 设置 2 个无符号整数 uniform (WebGL 2.0)
-   [uniform3ui()](#uniform3ui) - 设置 3 个无符号整数 uniform (WebGL 2.0)
-   [uniform4ui()](#uniform4ui) - 设置 4 个无符号整数 uniform (WebGL 2.0)
-   [uniform1fv()](#uniform1fv) - 设置浮点 uniform 数组
-   [uniform2fv()](#uniform2fv) - 设置 2 个浮点 uniform 数组
-   [uniform3fv()](#uniform3fv) - 设置 3 个浮点 uniform 数组
-   [uniform4fv()](#uniform4fv) - 设置 4 个浮点 uniform 数组
-   [uniform1iv()](#uniform1iv) - 设置整数 uniform 数组
-   [uniform2iv()](#uniform2iv) - 设置 2 个整数 uniform 数组
-   [uniform3iv()](#uniform3iv) - 设置 3 个整数 uniform 数组
-   [uniform4iv()](#uniform4iv) - 设置 4 个整数 uniform 数组
-   [uniformMatrix2fv()](#uniformmatrix2fv) - 设置 2x2 矩阵 uniform
-   [uniformMatrix3fv()](#uniformmatrix3fv) - 设置 3x3 矩阵 uniform
-   [uniformMatrix4fv()](#uniformmatrix4fv) - 设置 4x4 矩阵 uniform
-   [uniformMatrix2x3fv()](#uniformmatrix2x3fv) - 设置 2x3 矩阵 uniform (WebGL 2.0)
-   [uniformMatrix3x2fv()](#uniformmatrix3x2fv) - 设置 3x2 矩阵 uniform (WebGL 2.0)
-   [uniformMatrix2x4fv()](#uniformmatrix2x4fv) - 设置 2x4 矩阵 uniform (WebGL 2.0)
-   [uniformMatrix4x2fv()](#uniformmatrix4x2fv) - 设置 4x2 矩阵 uniform (WebGL 2.0)
-   [uniformMatrix3x4fv()](#uniformmatrix3x4fv) - 设置 3x4 矩阵 uniform (WebGL 2.0)
-   [uniformMatrix4x3fv()](#uniformmatrix4x3fv) - 设置 4x3 矩阵 uniform (WebGL 2.0)
-   [getUniform()](#getuniform) - 获取 uniform 变量值
-   [getActiveUniform()](#getactiveuniform) - 获取活动 uniform 信息
-   [getUniformBlockIndex()](#getuniformblockindex) - 获取 uniform 块索引 (WebGL 2.0)
-   [uniformBlockBinding()](#uniformblockbinding) - 绑定 uniform 块 (WebGL 2.0)

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 6. 纹理处理

-   [createTexture()](#createtexture) - 创建纹理对象
-   [bindTexture()](#bindtexture) - 绑定纹理
-   [texImage2D()](#teximage2d) - 设置 2D 纹理图像
-   [texImage3D()](#teximage3d) - 设置 3D 纹理图像 (WebGL 2.0)
-   [texSubImage2D()](#texsubimage2d) - 更新 2D 纹理子图像
-   [texSubImage3D()](#texsubimage3d) - 更新 3D 纹理子图像 (WebGL 2.0)
-   [texStorage2D()](#texstorage2d) - 分配 2D 纹理存储 (WebGL 2.0)
-   [texStorage3D()](#texstorage3d) - 分配 3D 纹理存储 (WebGL 2.0)
-   [texParameteri()](#texparameteri) - 设置纹理整数参数
-   [texParameterf()](#texparameterf) - 设置纹理浮点参数
-   [generateMipmap()](#generatemipmap) - 生成多级渐远纹理
-   [activeTexture()](#activetexture) - 激活纹理单元
-   [copyTexImage2D()](#copyteximage2d) - 从帧缓冲复制到 2D 纹理
-   [copyTexSubImage2D()](#copytexsubimage2d) - 从帧缓冲复制到 2D 纹理子区域
-   [copyTexSubImage3D()](#copytexsubimage3d) - 从帧缓冲复制到 3D 纹理子区域 (WebGL 2.0)
-   [compressedTexImage2D()](#compressedteximage2d) - 设置压缩 2D 纹理
-   [compressedTexImage3D()](#compressedteximage3d) - 设置压缩 3D 纹理 (WebGL 2.0)
-   [compressedTexSubImage2D()](#compressedtexsubimage2d) - 更新压缩 2D 纹理子区域
-   [compressedTexSubImage3D()](#compressedtexsubimage3d) - 更新压缩 3D 纹理子区域 (WebGL 2.0)
-   [getTexParameter()](#gettexparameter) - 获取纹理参数
-   [isTexture()](#istexture) - 检查对象是否为纹理
-   [deleteTexture()](#deletetexture) - 删除纹理对象

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 7. 渲染状态管理

-   [viewport()](#viewport) - 设置视口
-   [scissor()](#scissor) - 设置裁剪区域
-   [enable()](#enable) - 启用 WebGL 功能
-   [disable()](#disable) - 禁用 WebGL 功能
-   [isEnabled()](#isenabled) - 检查功能是否启用
-   [blendFunc()](#blendfunc) - 设置混合函数
-   [blendFuncSeparate()](#blendfuncseparate) - 分别设置 RGB 和 Alpha 混合函数
-   [blendEquation()](#blendequation) - 设置混合方程
-   [blendEquationSeparate()](#blendequationseparate) - 分别设置 RGB 和 Alpha 混合方程
-   [blendColor()](#blendcolor) - 设置混合颜色
-   [depthFunc()](#depthfunc) - 设置深度测试函数
-   [depthMask()](#depthmask) - 设置深度缓冲写入掩码
-   [depthRange()](#depthrange) - 设置深度范围
-   [stencilFunc()](#stencilfunc) - 设置模板测试函数
-   [stencilFuncSeparate()](#stencilfuncseparate) - 分别设置正面和背面模板测试函数
-   [stencilMask()](#stencilmask) - 设置模板写入掩码
-   [stencilMaskSeparate()](#stencilmaskseparate) - 分别设置正面和背面模板写入掩码
-   [stencilOp()](#stencilop) - 设置模板操作
-   [stencilOpSeparate()](#stencilopseparate) - 分别设置正面和背面模板操作
-   [cullFace()](#cullface) - 设置面剔除模式
-   [frontFace()](#frontface) - 设置正面方向
-   [lineWidth()](#linewidth) - 设置线宽
-   [polygonOffset()](#polygonoffset) - 设置多边形偏移
-   [sampleCoverage()](#samplecoverage) - 设置采样覆盖
-   [hint()](#hint) - 设置实现提示

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 8. 清除和颜色操作

-   [clear()](#clear) - 清除缓冲区
-   [clearColor()](#clearcolor) - 设置清除颜色
-   [clearDepth()](#cleardepth) - 设置清除深度值
-   [clearStencil()](#clearstencil) - 设置清除模板值
-   [colorMask()](#colormask) - 设置颜色写入掩码

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 9. 绘制命令

-   [drawArrays()](#drawarrays) - 绘制顶点数组
-   [drawElements()](#drawelements) - 绘制索引数组
-   [drawArraysInstanced()](#drawarraysinstanced) - 实例化绘制顶点数组 (WebGL 2.0)
-   [drawElementsInstanced()](#drawelementsinstanced) - 实例化绘制索引数组 (WebGL 2.0)
-   [drawRangeElements()](#drawrangeelements) - 绘制范围索引数组 (WebGL 2.0)

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 10. 帧缓冲操作

-   [createFramebuffer()](#createframebuffer) - 创建帧缓冲对象
-   [bindFramebuffer()](#bindframebuffer) - 绑定帧缓冲
-   [framebufferTexture2D()](#framebuffertexture2d) - 附加纹理到帧缓冲
-   [framebufferRenderbuffer()](#framebufferrenderbuffer) - 附加渲染缓冲到帧缓冲
-   [checkFramebufferStatus()](#checkframebufferstatus) - 检查帧缓冲状态
-   [deleteFramebuffer()](#deleteframebuffer) - 删除帧缓冲对象
-   [createRenderbuffer()](#createrenderbuffer) - 创建渲染缓冲对象
-   [bindRenderbuffer()](#bindrenderbuffer) - 绑定渲染缓冲
-   [renderbufferStorage()](#renderbufferstorage) - 设置渲染缓冲存储
-   [deleteRenderbuffer()](#deleterenderbuffer) - 删除渲染缓冲对象

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 11. 查询和获取信息

-   [getParameter()](#getparameter) - 获取 WebGL 参数
-   [getError()](#geterror) - 获取错误信息
-   [getSupportedExtensions()](#getsupportedextensions) - 获取支持的扩展列表
-   [getExtension()](#getextension) - 获取扩展对象
-   [readPixels()](#readpixels) - 读取像素数据

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

#### 12. 像素操作

-   [pixelStorei()](#pixelstorei) - 设置像素存储参数
-   [copyTexImage2D()](#copyteximage2d) - 从帧缓冲复制到纹理
-   [copyTexSubImage2D()](#copytexsubimage2d) - 从帧缓冲复制到纹理子区域

═══════════════════════════════════════════════════════════════════════════════

## 学习路径指南

### 🚀 初学者路径 (基础概念) - 预计学习时间：2-3 周

┌─────────────────────────────────────────────────────────────────────────────┐
│ 初学者学习路径 │
└─────────────────────────────────────────────────────────────────────────────┘

#### 第 1 阶段：WebGL 基础概念 (3-5 天)

1. **理解 WebGL 渲染管线**

    - 学习顶点着色器和片段着色器的作用
    - 了解 WebGL 坐标系统（NDC 坐标）
    - 理解 GPU 并行计算的基本概念

2. **环境搭建和第一个程序**
    - 获取 WebGL 上下文：`getContext()`
    - 设置视口：`viewport()`
    - 清除画布：`clearColor()`, `clear()`

#### 第 2 阶段：着色器入门 (5-7 天)

1. **着色器基础**

    - 创建着色器：`createShader()`
    - 设置源代码：`shaderSource()`
    - 编译着色器：`compileShader()`
    - 错误检查：`getShaderParameter()`, `getShaderInfoLog()`

2. **着色器程序**
    - 创建程序：`createProgram()`
    - 附加着色器：`attachShader()`
    - 链接程序：`linkProgram()`
    - 使用程序：`useProgram()`

#### 第 3 阶段：绘制第一个三角形 (3-5 天)

1. **缓冲区操作**

    - 创建缓冲区：`createBuffer()`
    - 绑定缓冲区：`bindBuffer()`
    - 传输数据：`bufferData()`

2. **顶点属性配置**

    - 获取属性位置：`getAttribLocation()`
    - 启用属性：`enableVertexAttribArray()`
    - 配置属性：`vertexAttribPointer()`

3. **基础绘制**
    - 绘制数组：`drawArrays()`

**学习目标：** 能够绘制简单的 2D 图形（三角形、矩形）

═══════════════════════════════════════════════════════════════════════════════

### 🎯 中级路径 (实用功能) - 预计学习时间：3-4 周

┌─────────────────────────────────────────────────────────────────────────────┐
│ 中级学习路径 │
└─────────────────────────────────────────────────────────────────────────────┘

#### 第 1 阶段：Uniform 变量和交互 (1 周)

1. **Uniform 变量管理**

    - 获取 uniform 位置：`getUniformLocation()`
    - 设置各种类型的 uniform：`uniform1f()`, `uniform2f()`, `uniformMatrix4fv()`
    - 实现简单的动画和交互

2. **矩阵变换**
    - 学习模型、视图、投影矩阵
    - 实现 2D/3D 变换

#### 第 2 阶段：纹理和材质 (1-2 周)

1. **纹理基础**

    - 创建纹理：`createTexture()`
    - 绑定纹理：`bindTexture()`
    - 加载图像：`texImage2D()`

2. **纹理配置**
    - 设置过滤：`texParameteri()`
    - 生成 mipmap：`generateMipmap()`
    - 多纹理单元：`activeTexture()`

#### 第 3 阶段：3D 渲染基础 (1 周)

1. **深度测试**

    - 启用深度测试：`enable(gl.DEPTH_TEST)`
    - 配置深度函数：`depthFunc()`
    - 清除深度缓冲：`clear(gl.DEPTH_BUFFER_BIT)`

2. **面剔除**

    - 启用面剔除：`enable(gl.CULL_FACE)`
    - 设置剔除模式：`cullFace()`
    - 设置正面方向：`frontFace()`

3. **索引绘制**
    - 使用索引缓冲：`drawElements()`
    - 优化顶点数据

**学习目标：** 能够创建带纹理的 3D 模型，实现基本的 3D 场景

═══════════════════════════════════════════════════════════════════════════════

### 🏆 高级路径 (性能优化) - 预计学习时间：4-6 周

┌─────────────────────────────────────────────────────────────────────────────┐
│ 高级学习路径 │
└─────────────────────────────────────────────────────────────────────────────┘

#### 第 1 阶段：高级缓冲区管理 (1-2 周)

1. **顶点数组对象 (VAO)**

    - 创建 VAO：`createVertexArray()` (WebGL 2.0)
    - 封装顶点状态，提高性能
    - 批量绘制优化

2. **动态缓冲区**

    - 部分更新：`bufferSubData()`
    - 选择合适的使用模式：`STATIC_DRAW`, `DYNAMIC_DRAW`, `STREAM_DRAW`

3. **实例化渲染**
    - 实例化绘制：`drawArraysInstanced()`, `drawElementsInstanced()`
    - 顶点属性除数：`vertexAttribDivisor()`

#### 第 2 阶段：高级渲染技术 (2-3 周)

1. **离屏渲染**

    - 帧缓冲对象：`createFramebuffer()`, `framebufferTexture2D()`
    - 渲染到纹理技术
    - 后处理效果

2. **高级混合**

    - 混合函数：`blendFunc()`, `blendFuncSeparate()`
    - 混合方程：`blendEquation()`
    - 透明度排序

3. **模板测试**
    - 模板缓冲：`stencilFunc()`, `stencilOp()`
    - 高级遮罩技术

#### 第 3 阶段：WebGL 2.0 高级特性 (1 周)

1. **Uniform Buffer Objects**

    - 统一缓冲区对象：`uniformBlockBinding()`
    - 减少 uniform 更新开销

2. **Transform Feedback**

    - 变换反馈：`beginTransformFeedback()`, `endTransformFeedback()`
    - GPU 计算应用

3. **多重采样抗锯齿**
    - MSAA 配置和使用

**学习目标：** 能够创建高性能的复杂 3D 应用，掌握现代 WebGL 开发技巧

═══════════════════════════════════════════════════════════════════════════════

### 📚 专业路径 (特定领域) - 持续学习

┌─────────────────────────────────────────────────────────────────────────────┐
│ 专业学习路径 │
└─────────────────────────────────────────────────────────────────────────────┘

#### 游戏开发方向

-   粒子系统
-   阴影映射
-   延迟渲染
-   物理模拟

#### 数据可视化方向

-   大数据渲染
-   实时图表
-   地理信息系统
-   科学计算可视化

#### 创意编程方向

-   着色器艺术
-   程序化生成
-   音频可视化
-   交互装置

═══════════════════════════════════════════════════════════════════════════════

### 💡 学习建议

┌─────────────────────────────────────────────────────────────────────────────┐
│ 学习建议 │
└─────────────────────────────────────────────────────────────────────────────┘

1. **实践为主**：每个概念都要通过代码实践
2. **循序渐进**：不要跳跃式学习，确保基础扎实
3. **错误调试**：学会使用浏览器开发工具调试 WebGL
4. **参考资源**：

    - MDN WebGL 文档
    - WebGL 规范
    - 开源 WebGL 项目
    - 在线教程和示例

5. **项目实践**：
    - 初级：2D 游戏（贪吃蛇、俄罗斯方块）
    - 中级：3D 模型查看器
    - 高级：小型 3D 游戏或可视化应用

═══════════════════════════════════════════════════════════════════════════════

## WebGL 1.0 核心函数

═══════════════════════════════════════════════════════════════════════════════

## 上下文创建与管理

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 上下文创建与管理函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `canvas.getContext()`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
HTMLCanvasElement.getContext(contextType, contextAttributes?)
```

**作用：** 获取 WebGL 渲染上下文

**参数详解：**

-   `contextType` (string) - 上下文类型

    -   `'webgl'` - WebGL 1.0 上下文
    -   `'webgl2'` - WebGL 2.0 上下文
    -   `'experimental-webgl'` - 实验性 WebGL 1.0 上下文(已废弃)

-   `contextAttributes` (object, 可选) - 上下文属性配置对象
    -   `alpha` (boolean, 默认 true) - 是否包含 alpha 通道
    -   `depth` (boolean, 默认 true) - 是否包含深度缓冲
    -   `stencil` (boolean, 默认 false) - 是否包含模板缓冲
    -   `antialias` (boolean, 默认 true) - 是否启用抗锯齿
    -   `premultipliedAlpha` (boolean, 默认 true) - 是否预乘 alpha
    -   `preserveDrawingBuffer` (boolean, 默认 false) - 是否保留绘制缓冲
    -   `powerPreference` (string, 默认'default') - 功耗偏好
        -   `'default'` - 默认设置
        -   `'high-performance'` - 高性能
        -   `'low-power'` - 低功耗
    -   `failIfMajorPerformanceCaveat` (boolean, 默认 false) - 性能警告时是否失败
    -   `desynchronized` (boolean, 默认 false) - 是否异步渲染

**返回值：** WebGLRenderingContext 对象或 null

**代码示例：**

```javascript
// 基本用法
const gl = canvas.getContext('webgl2');

// 带配置的用法
const gl = canvas.getContext('webgl2', {
    alpha: false, // 不需要alpha通道
    depth: true, // 需要深度缓冲
    stencil: true, // 需要模板缓冲
    antialias: false, // 禁用抗锯齿以提高性能
    powerPreference: 'high-performance',
});

// 检查是否成功获取上下文
if (!gl) {
    console.error('无法获取WebGL上下文');
}
```

**注意事项：**

-   如果浏览器不支持 WebGL，返回 null
-   同一个 canvas 只能有一个活动的 WebGL 上下文
-   上下文属性在创建后无法修改

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getContextAttributes()`

**函数签名：**

```javascript
gl.getContextAttributes();
```

**作用：** 获取当前 WebGL 上下文的实际属性配置

**参数：** 无

**返回值：** WebGLContextAttributes 对象，包含以下属性：

-   `alpha` (boolean) - 是否包含 alpha 通道
-   `depth` (boolean) - 是否包含深度缓冲
-   `stencil` (boolean) - 是否包含模板缓冲
-   `antialias` (boolean) - 是否启用抗锯齿
-   `premultipliedAlpha` (boolean) - 是否预乘 alpha
-   `preserveDrawingBuffer` (boolean) - 是否保留绘制缓冲
-   `powerPreference` (string) - 功耗偏好
-   `failIfMajorPerformanceCaveat` (boolean) - 性能警告设置
-   `desynchronized` (boolean) - 是否异步渲染

**代码示例：**

```javascript
const attributes = gl.getContextAttributes();
console.log('上下文属性:', attributes);
console.log('深度缓冲:', attributes.depth);
console.log('抗锯齿:', attributes.antialias);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.isContextLost()`

**函数签名：**

```javascript
gl.isContextLost();
```

**作用：** 检查 WebGL 上下文是否已丢失

**参数：** 无

**返回值：** boolean - true 表示上下文已丢失，false 表示正常

**代码示例：**

```javascript
if (gl.isContextLost()) {
    console.warn('WebGL上下文已丢失，需要重新初始化');
    // 处理上下文丢失的逻辑
}
```

**注意事项：**

-   上下文丢失通常由于 GPU 重置、内存不足等原因
-   可以监听'webglcontextlost'和'webglcontextrestored'事件

═══════════════════════════════════════════════════════════════════════════════

## 着色器管理

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 着色器管理函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.createShader(type)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.createShader(type);
```

**作用：** 创建一个新的着色器对象

**参数详解：**

-   `type` (GLenum) - 着色器类型
    -   `gl.VERTEX_SHADER` (0x8B31) - 顶点着色器
    -   `gl.FRAGMENT_SHADER` (0x8B30) - 片段着色器

**返回值：** WebGLShader 对象，如果失败返回 null

**代码示例：**

```javascript
// 创建顶点着色器
const vertexShader = gl.createShader(gl.VERTEX_SHADER);
if (!vertexShader) {
    console.error('无法创建顶点着色器');
}

// 创建片段着色器
const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
if (!fragmentShader) {
    console.error('无法创建片段着色器');
}
```

**注意事项：**

-   创建的着色器对象需要设置源代码并编译才能使用
-   使用完毕后应调用`deleteShader()`释放资源

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.shaderSource(shader, source)`

**函数签名：**

```javascript
gl.shaderSource(shader, source);
```

**作用：** 为着色器对象设置 GLSL 源代码

**参数详解：**

-   `shader` (WebGLShader) - 着色器对象
-   `source` (string) - GLSL 着色器源代码字符串

**返回值：** 无

**代码示例：**

```javascript
// 顶点着色器源代码
const vertexShaderSource = `
    attribute vec4 a_position;
    attribute vec2 a_texCoord;

    uniform mat4 u_matrix;

    varying vec2 v_texCoord;

    void main() {
        gl_Position = u_matrix * a_position;
        v_texCoord = a_texCoord;
    }
`;

// 片段着色器源代码
const fragmentShaderSource = `
    precision mediump float;

    uniform sampler2D u_texture;
    varying vec2 v_texCoord;

    void main() {
        gl_FragColor = texture2D(u_texture, v_texCoord);
    }
`;

// 设置着色器源代码
gl.shaderSource(vertexShader, vertexShaderSource);
gl.shaderSource(fragmentShader, fragmentShaderSource);
```

**注意事项：**

-   源代码必须是有效的 GLSL 语法
-   设置源代码后需要调用`compileShader()`进行编译

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.compileShader(shader)`

**函数签名：**

```javascript
gl.compileShader(shader);
```

**作用：** 编译着色器源代码

**参数详解：**

-   `shader` (WebGLShader) - 要编译的着色器对象

**返回值：** 无

**代码示例：**

```javascript
// 编译着色器
gl.compileShader(vertexShader);
gl.compileShader(fragmentShader);

// 检查编译状态
if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
    const error = gl.getShaderInfoLog(vertexShader);
    console.error('顶点着色器编译失败:', error);
    gl.deleteShader(vertexShader);
}

if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
    const error = gl.getShaderInfoLog(fragmentShader);
    console.error('片段着色器编译失败:', error);
    gl.deleteShader(fragmentShader);
}
```

**注意事项：**

-   编译可能失败，应检查编译状态
-   编译失败时可通过`getShaderInfoLog()`获取错误信息

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getShaderParameter(shader, pname)`

**函数签名：**

```javascript
gl.getShaderParameter(shader, pname);
```

**作用：** 获取着色器的参数信息

**参数详解：**

-   `shader` (WebGLShader) - 着色器对象
-   `pname` (GLenum) - 参数名称
    -   `gl.COMPILE_STATUS` (0x8B81) - 编译状态 (boolean)
    -   `gl.DELETE_STATUS` (0x8B80) - 删除状态 (boolean)
    -   `gl.SHADER_TYPE` (0x8B4F) - 着色器类型 (GLenum)

**返回值：** 根据参数类型返回相应的值

**代码示例：**

```javascript
// 检查编译状态
const compileStatus = gl.getShaderParameter(shader, gl.COMPILE_STATUS);
console.log('编译成功:', compileStatus); // true/false

// 获取着色器类型
const shaderType = gl.getShaderParameter(shader, gl.SHADER_TYPE);
if (shaderType === gl.VERTEX_SHADER) {
    console.log('这是顶点着色器');
} else if (shaderType === gl.FRAGMENT_SHADER) {
    console.log('这是片段着色器');
}

// 检查删除状态
const deleteStatus = gl.getShaderParameter(shader, gl.DELETE_STATUS);
console.log('已标记删除:', deleteStatus);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getShaderInfoLog(shader)`

**函数签名：**

```javascript
gl.getShaderInfoLog(shader);
```

**作用：** 获取着色器编译的信息日志

**参数详解：**

-   `shader` (WebGLShader) - 着色器对象

**返回值：** string - 包含编译信息的字符串，如果没有信息则返回空字符串

**代码示例：**

```javascript
// 获取编译日志
const log = gl.getShaderInfoLog(shader);
if (log) {
    console.log('着色器编译日志:', log);
}

// 完整的着色器编译检查函数
function compileShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        const error = gl.getShaderInfoLog(shader);
        console.error('着色器编译失败:', error);
        gl.deleteShader(shader);
        return null;
    }

    return shader;
}
```

**注意事项：**

-   即使编译成功，日志也可能包含警告信息
-   编译失败时，日志包含详细的错误信息和行号

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getShaderSource(shader)`

**函数签名：**

```javascript
gl.getShaderSource(shader);
```

**作用：** 获取着色器的源代码

**参数详解：**

-   `shader` (WebGLShader) - 着色器对象

**返回值：** string - 着色器的源代码字符串

**代码示例：**

```javascript
const source = gl.getShaderSource(shader);
console.log('着色器源代码:', source);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.isShader(shader)`

**函数签名：**

```javascript
gl.isShader(shader);
```

**作用：** 检查对象是否为有效的着色器对象

**参数详解：**

-   `shader` (any) - 要检查的对象

**返回值：** boolean - true 表示是有效的着色器对象

**代码示例：**

```javascript
if (gl.isShader(shader)) {
    console.log('这是一个有效的着色器对象');
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.deleteShader(shader)`

**函数签名：**

```javascript
gl.deleteShader(shader);
```

**作用：** 删除着色器对象并释放相关资源

**参数详解：**

-   `shader` (WebGLShader) - 要删除的着色器对象

**返回值：** 无

**代码示例：**

```javascript
// 删除着色器
gl.deleteShader(vertexShader);
gl.deleteShader(fragmentShader);
```

**注意事项：**

-   如果着色器已附加到程序，删除操作会被延迟到着色器从程序分离后
-   删除后的着色器对象不能再使用

═══════════════════════════════════════════════════════════════════════════════

## 着色器程序管理

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 着色器程序管理函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.createProgram()`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.createProgram();
```

**作用：** 创建一个新的着色器程序对象

**参数：** 无

**返回值：** WebGLProgram 对象，如果失败返回 null

**代码示例：**

```javascript
const program = gl.createProgram();
if (!program) {
    console.error('无法创建着色器程序');
}
```

**注意事项：**

-   创建的程序需要附加着色器并链接才能使用
-   使用完毕后应调用`deleteProgram()`释放资源

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.attachShader(program, shader)`

**函数签名：**

```javascript
gl.attachShader(program, shader);
```

**作用：** 将编译好的着色器附加到着色器程序

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象
-   `shader` (WebGLShader) - 要附加的着色器对象

**返回值：** 无

**代码示例：**

```javascript
// 附加顶点着色器和片段着色器
gl.attachShader(program, vertexShader);
gl.attachShader(program, fragmentShader);
```

**注意事项：**

-   一个程序必须至少附加一个顶点着色器和一个片段着色器
-   同一个着色器可以附加到多个程序
-   附加后需要调用`linkProgram()`进行链接

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.detachShader(program, shader)`

**函数签名：**

```javascript
gl.detachShader(program, shader);
```

**作用：** 从着色器程序中分离着色器

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象
-   `shader` (WebGLShader) - 要分离的着色器对象

**返回值：** 无

**代码示例：**

```javascript
// 分离着色器
gl.detachShader(program, vertexShader);
gl.detachShader(program, fragmentShader);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.linkProgram(program)`

**函数签名：**

```javascript
gl.linkProgram(program);
```

**作用：** 链接着色器程序，将顶点着色器和片段着色器连接成完整的渲染管线

**参数详解：**

-   `program` (WebGLProgram) - 要链接的着色器程序对象

**返回值：** 无

**代码示例：**

```javascript
// 链接程序
gl.linkProgram(program);

// 检查链接状态
if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
    const error = gl.getProgramInfoLog(program);
    console.error('程序链接失败:', error);
    gl.deleteProgram(program);
}
```

**注意事项：**

-   链接可能失败，应检查链接状态
-   链接失败时可通过`getProgramInfoLog()`获取错误信息
-   链接成功后可以使用`useProgram()`激活程序

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getProgramParameter(program, pname)`

**函数签名：**

```javascript
gl.getProgramParameter(program, pname);
```

**作用：** 获取着色器程序的参数信息

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象
-   `pname` (GLenum) - 参数名称
    -   `gl.LINK_STATUS` (0x8B82) - 链接状态 (boolean)
    -   `gl.DELETE_STATUS` (0x8B80) - 删除状态 (boolean)
    -   `gl.VALIDATE_STATUS` (0x8B83) - 验证状态 (boolean)
    -   `gl.ACTIVE_ATTRIBUTES` (0x8B89) - 活动顶点属性数量 (GLint)
    -   `gl.ACTIVE_UNIFORMS` (0x8B86) - 活动 uniform 变量数量 (GLint)
    -   `gl.ATTACHED_SHADERS` (0x8B85) - 附加的着色器数量 (GLint)

**返回值：** 根据参数类型返回相应的值

**代码示例：**

```javascript
// 检查链接状态
const linkStatus = gl.getProgramParameter(program, gl.LINK_STATUS);
console.log('链接成功:', linkStatus);

// 获取活动属性数量
const activeAttribs = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);
console.log('活动顶点属性数量:', activeAttribs);

// 获取活动uniform数量
const activeUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
console.log('活动uniform数量:', activeUniforms);

// 获取附加的着色器数量
const attachedShaders = gl.getProgramParameter(program, gl.ATTACHED_SHADERS);
console.log('附加着色器数量:', attachedShaders);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getProgramInfoLog(program)`

**函数签名：**

```javascript
gl.getProgramInfoLog(program);
```

**作用：** 获取着色器程序链接或验证的信息日志

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象

**返回值：** string - 包含链接/验证信息的字符串

**代码示例：**

```javascript
const log = gl.getProgramInfoLog(program);
if (log) {
    console.log('程序信息日志:', log);
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.validateProgram(program)`

**函数签名：**

```javascript
gl.validateProgram(program);
```

**作用：** 验证着色器程序是否可以在当前 WebGL 状态下执行

**参数详解：**

-   `program` (WebGLProgram) - 要验证的着色器程序对象

**返回值：** 无

**代码示例：**

```javascript
// 验证程序
gl.validateProgram(program);

// 检查验证结果
const valid = gl.getProgramParameter(program, gl.VALIDATE_STATUS);
if (!valid) {
    const log = gl.getProgramInfoLog(program);
    console.warn('程序验证失败:', log);
}
```

**注意事项：**

-   验证是可选的，主要用于调试
-   验证失败不影响程序的正常使用

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.useProgram(program)`

**函数签名：**

```javascript
gl.useProgram(program);
```

**作用：** 激活指定的着色器程序，使其成为当前渲染状态的一部分

**参数详解：**

-   `program` (WebGLProgram | null) - 要使用的着色器程序对象，null 表示停用当前程序

**返回值：** 无

**代码示例：**

```javascript
// 使用着色器程序
gl.useProgram(program);

// 停用当前程序
gl.useProgram(null);
```

**注意事项：**

-   只有链接成功的程序才能被使用
-   同一时间只能有一个程序处于活动状态

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.isProgram(program)`

**函数签名：**

```javascript
gl.isProgram(program);
```

**作用：** 检查对象是否为有效的着色器程序对象

**参数详解：**

-   `program` (any) - 要检查的对象

**返回值：** boolean - true 表示是有效的程序对象

**代码示例：**

```javascript
if (gl.isProgram(program)) {
    console.log('这是一个有效的着色器程序');
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.deleteProgram(program)`

**函数签名：**

```javascript
gl.deleteProgram(program);
```

**作用：** 删除着色器程序对象并释放相关资源

**参数详解：**

-   `program` (WebGLProgram) - 要删除的着色器程序对象

**返回值：** 无

**代码示例：**

```javascript
// 删除程序
gl.deleteProgram(program);
```

**注意事项：**

-   如果程序正在使用中，删除操作会被延迟
-   删除后的程序对象不能再使用

═══════════════════════════════════════════════════════════════════════════════

## 缓冲区操作

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 缓冲区操作函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.createBuffer()`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.createBuffer();
```

**作用：** 创建一个新的缓冲区对象，用于存储顶点数据、索引数据等

**参数：** 无

**返回值：** WebGLBuffer 对象，如果失败返回 null

**代码示例：**

```javascript
// 创建顶点缓冲区
const vertexBuffer = gl.createBuffer();
if (!vertexBuffer) {
    console.error('无法创建顶点缓冲区');
}

// 创建索引缓冲区
const indexBuffer = gl.createBuffer();
if (!indexBuffer) {
    console.error('无法创建索引缓冲区');
}
```

**注意事项：**

-   创建的缓冲区需要绑定并分配数据才能使用
-   使用完毕后应调用`deleteBuffer()`释放资源

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.bindBuffer(target, buffer)`

**函数签名：**

```javascript
gl.bindBuffer(target, buffer);
```

**作用：** 将缓冲区对象绑定到指定的绑定点

**参数详解：**

-   `target` (GLenum) - 绑定目标
    -   `gl.ARRAY_BUFFER` (0x8892) - 顶点属性数据缓冲区
    -   `gl.ELEMENT_ARRAY_BUFFER` (0x8893) - 顶点索引数据缓冲区
    -   `gl.COPY_READ_BUFFER` (0x8F36) - 复制源缓冲区 (WebGL 2.0)
    -   `gl.COPY_WRITE_BUFFER` (0x8F37) - 复制目标缓冲区 (WebGL 2.0)
    -   `gl.TRANSFORM_FEEDBACK_BUFFER` (0x8C8E) - 变换反馈缓冲区 (WebGL 2.0)
    -   `gl.UNIFORM_BUFFER` (0x8A11) - Uniform 块缓冲区 (WebGL 2.0)
    -   `gl.PIXEL_PACK_BUFFER` (0x88EB) - 像素打包缓冲区 (WebGL 2.0)
    -   `gl.PIXEL_UNPACK_BUFFER` (0x88EC) - 像素解包缓冲区 (WebGL 2.0)
-   `buffer` (WebGLBuffer | null) - 要绑定的缓冲区对象，null 表示解绑

**返回值：** 无

**代码示例：**

```javascript
// 绑定顶点缓冲区
gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);

// 绑定索引缓冲区
gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);

// 解绑当前缓冲区
gl.bindBuffer(gl.ARRAY_BUFFER, null);

// WebGL 2.0 - 绑定uniform缓冲区
gl.bindBuffer(gl.UNIFORM_BUFFER, uniformBuffer);
```

**注意事项：**

-   同一时间每个绑定点只能绑定一个缓冲区
-   绑定后的操作会影响当前绑定的缓冲区

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.bufferData(target, data, usage)`

**函数签名：**

```javascript
gl.bufferData(target, data, usage);
// 或者
gl.bufferData(target, size, usage);
```

**作用：** 为缓冲区分配存储空间并可选地初始化数据

**参数详解：**

-   `target` (GLenum) - 缓冲区绑定目标（同 bindBuffer 的 target 参数）
-   `data` (ArrayBuffer | ArrayBufferView | null) - 要复制到缓冲区的数据
-   `size` (GLsizeiptr) - 要分配的字节数（当 data 为 null 时使用）
-   `usage` (GLenum) - 数据使用模式提示
    -   `gl.STATIC_DRAW` (0x88E4) - 数据不会或很少改变，用于绘制
    -   `gl.DYNAMIC_DRAW` (0x88E8) - 数据会频繁改变，用于绘制
    -   `gl.STREAM_DRAW` (0x88E0) - 数据每次使用时都会改变，用于绘制
    -   `gl.STATIC_READ` (0x88E5) - 数据不会或很少改变，用于读取 (WebGL 2.0)
    -   `gl.DYNAMIC_READ` (0x88E9) - 数据会频繁改变，用于读取 (WebGL 2.0)
    -   `gl.STREAM_READ` (0x88E1) - 数据每次使用时都会改变，用于读取 (WebGL 2.0)
    -   `gl.STATIC_COPY` (0x88E6) - 数据不会或很少改变，用于复制 (WebGL 2.0)
    -   `gl.DYNAMIC_COPY` (0x88EA) - 数据会频繁改变，用于复制 (WebGL 2.0)
    -   `gl.STREAM_COPY` (0x88E2) - 数据每次使用时都会改变，用于复制 (WebGL 2.0)

**返回值：** 无

**代码示例：**

```javascript
// 顶点数据
const vertices = new Float32Array([
    -0.5,
    -0.5,
    0.0, // 左下
    0.5,
    -0.5,
    0.0, // 右下
    0.0,
    0.5,
    0.0, // 顶部
]);

// 索引数据
const indices = new Uint16Array([0, 1, 2]);

// 绑定并填充顶点缓冲区
gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

// 绑定并填充索引缓冲区
gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);

// 只分配空间，不初始化数据
gl.bufferData(gl.ARRAY_BUFFER, 1024, gl.DYNAMIC_DRAW);
```

**注意事项：**

-   调用此函数会替换缓冲区的所有现有数据
-   使用模式提示有助于驱动程序优化性能
-   数据类型必须是 TypedArray 或 ArrayBuffer

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.bufferSubData(target, offset, data)`

**函数签名：**

```javascript
gl.bufferSubData(target, offset, data);
```

**作用：** 更新缓冲区中的部分数据，不改变缓冲区大小

**参数详解：**

-   `target` (GLenum) - 缓冲区绑定目标
-   `offset` (GLintptr) - 开始更新的字节偏移量
-   `data` (ArrayBuffer | ArrayBufferView) - 要写入的数据

**返回值：** 无

**代码示例：**

```javascript
// 原始数据
const originalData = new Float32Array([1, 2, 3, 4, 5, 6]);
gl.bufferData(gl.ARRAY_BUFFER, originalData, gl.DYNAMIC_DRAW);

// 更新部分数据
const newData = new Float32Array([10, 20]);
gl.bufferSubData(gl.ARRAY_BUFFER, 8, newData); // 从第8字节开始更新

// 结果：缓冲区数据变为 [1, 2, 10, 20, 5, 6]
```

**注意事项：**

-   偏移量和数据大小不能超出缓冲区边界
-   适用于动态更新部分顶点数据的场景

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getBufferParameter(target, pname)`

**函数签名：**

```javascript
gl.getBufferParameter(target, pname);
```

**作用：** 获取缓冲区的参数信息

**参数详解：**

-   `target` (GLenum) - 缓冲区绑定目标
-   `pname` (GLenum) - 参数名称
    -   `gl.BUFFER_SIZE` (0x8764) - 缓冲区大小（字节）
    -   `gl.BUFFER_USAGE` (0x8765) - 缓冲区使用模式

**返回值：** 根据参数类型返回相应的值

**代码示例：**

```javascript
// 获取缓冲区大小
const size = gl.getBufferParameter(gl.ARRAY_BUFFER, gl.BUFFER_SIZE);
console.log('缓冲区大小:', size, '字节');

// 获取使用模式
const usage = gl.getBufferParameter(gl.ARRAY_BUFFER, gl.BUFFER_USAGE);
console.log('使用模式:', usage === gl.STATIC_DRAW ? 'STATIC_DRAW' : '其他');
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.isBuffer(buffer)`

**函数签名：**

```javascript
gl.isBuffer(buffer);
```

**作用：** 检查对象是否为有效的缓冲区对象

**参数详解：**

-   `buffer` (any) - 要检查的对象

**返回值：** boolean - true 表示是有效的缓冲区对象

**代码示例：**

```javascript
if (gl.isBuffer(vertexBuffer)) {
    console.log('这是一个有效的缓冲区对象');
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.deleteBuffer(buffer)`

**函数签名：**

```javascript
gl.deleteBuffer(buffer);
```

**作用：** 删除缓冲区对象并释放相关资源

**参数详解：**

-   `buffer` (WebGLBuffer) - 要删除的缓冲区对象

**返回值：** 无

**代码示例：**

```javascript
// 删除缓冲区
gl.deleteBuffer(vertexBuffer);
gl.deleteBuffer(indexBuffer);
```

**注意事项：**

-   如果缓冲区正在使用中，删除操作会被延迟
-   删除后的缓冲区对象不能再使用

═══════════════════════════════════════════════════════════════════════════════

## 顶点数组对象 (VAO) - WebGL 2.0

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 顶点数组对象 (VAO) 函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.createVertexArray()`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.createVertexArray();
```

**作用：** 创建一个新的顶点数组对象，用于封装顶点属性状态

**参数：** 无

**返回值：** WebGLVertexArrayObject 对象，如果失败返回 null

**代码示例：**

```javascript
// WebGL 2.0
const vao = gl.createVertexArray();
if (!vao) {
    console.error('无法创建顶点数组对象');
}
```

**注意事项：**

-   仅在 WebGL 2.0 中可用
-   VAO 可以保存顶点属性配置，提高渲染性能

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.bindVertexArray(vertexArray)`

**函数签名：**

```javascript
gl.bindVertexArray(vertexArray);
```

**作用：** 绑定顶点数组对象，激活其保存的顶点属性状态

**参数详解：**

-   `vertexArray` (WebGLVertexArrayObject | null) - 要绑定的 VAO 对象，null 表示解绑

**返回值：** 无

**代码示例：**

```javascript
// 绑定VAO
gl.bindVertexArray(vao);

// 配置顶点属性（这些配置会保存在VAO中）
gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
gl.enableVertexAttribArray(0);
gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 0, 0);

// 解绑VAO
gl.bindVertexArray(null);

// 后续使用时只需绑定VAO即可恢复所有配置
gl.bindVertexArray(vao);
gl.drawArrays(gl.TRIANGLES, 0, 3);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.isVertexArray(vertexArray)`

**函数签名：**

```javascript
gl.isVertexArray(vertexArray);
```

**作用：** 检查对象是否为有效的顶点数组对象

**参数详解：**

-   `vertexArray` (any) - 要检查的对象

**返回值：** boolean - true 表示是有效的 VAO 对象

**代码示例：**

```javascript
if (gl.isVertexArray(vao)) {
    console.log('这是一个有效的顶点数组对象');
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.deleteVertexArray(vertexArray)`

**函数签名：**

```javascript
gl.deleteVertexArray(vertexArray);
```

**作用：** 删除顶点数组对象并释放相关资源

**参数详解：**

-   `vertexArray` (WebGLVertexArrayObject) - 要删除的 VAO 对象

**返回值：** 无

**代码示例：**

```javascript
// 删除VAO
gl.deleteVertexArray(vao);
```

═══════════════════════════════════════════════════════════════════════════════

## 顶点属性管理

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 顶点属性管理函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.getAttribLocation(program, name)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.getAttribLocation(program, name);
```

**作用：** 获取顶点属性的位置索引

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象
-   `name` (string) - 顶点属性变量名

**返回值：** GLint - 属性位置索引，如果未找到返回 -1

**代码示例：**

```javascript
const positionLocation = gl.getAttribLocation(program, 'a_position');
const colorLocation = gl.getAttribLocation(program, 'a_color');
const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');

if (positionLocation === -1) {
    console.warn('未找到顶点属性: a_position');
}
```

**注意事项：**

-   属性名必须与着色器中声明的名称完全匹配
-   如果属性未被使用，可能会被优化器移除
-   返回的位置索引在程序生命周期内保持不变

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.enableVertexAttribArray(index)`

**函数签名：**

```javascript
gl.enableVertexAttribArray(index);
```

**作用：** 启用指定位置的顶点属性数组

**参数详解：**

-   `index` (GLuint) - 顶点属性的位置索引

**返回值：** 无

**代码示例：**

```javascript
const positionLocation = gl.getAttribLocation(program, 'a_position');
gl.enableVertexAttribArray(positionLocation);
```

**注意事项：**

-   启用后的属性数组会从绑定的缓冲区读取数据
-   必须在配置 vertexAttribPointer 之后启用

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.disableVertexAttribArray(index)`

**函数签名：**

```javascript
gl.disableVertexAttribArray(index);
```

**作用：** 禁用指定位置的顶点属性数组

**参数详解：**

-   `index` (GLuint) - 顶点属性的位置索引

**返回值：** 无

**代码示例：**

```javascript
gl.disableVertexAttribArray(positionLocation);
```

**注意事项：**

-   禁用后的属性将使用常量值
-   可以通过 vertexAttrib\* 函数设置常量值

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.vertexAttribPointer(index, size, type, normalized, stride, offset)`

**函数签名：**

```javascript
gl.vertexAttribPointer(index, size, type, normalized, stride, offset);
```

**作用：** 指定顶点属性数据的格式和位置

**参数详解：**

-   `index` (GLuint) - 属性位置索引
-   `size` (GLint) - 每个属性的分量数 (1, 2, 3, 或 4)
-   `type` (GLenum) - 数据类型
    -   `gl.BYTE` (0x1400) - 有符号字节
    -   `gl.UNSIGNED_BYTE` (0x1401) - 无符号字节
    -   `gl.SHORT` (0x1402) - 有符号短整数
    -   `gl.UNSIGNED_SHORT` (0x1403) - 无符号短整数
    -   `gl.FLOAT` (0x1406) - 浮点数
-   `normalized` (GLboolean) - 是否将整数值归一化到 [0,1] 或 [-1,1] 范围
-   `stride` (GLsizei) - 连续顶点属性间的字节偏移，0 表示紧密排列
-   `offset` (GLintptr) - 第一个属性在缓冲区中的字节偏移

**返回值：** 无

**代码示例：**

```javascript
// 位置属性：3个浮点数，不归一化
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

// 颜色属性：4个无符号字节，归一化到[0,1]
gl.vertexAttribPointer(colorLocation, 4, gl.UNSIGNED_BYTE, true, 0, 0);

// 交错数据：位置(3个float) + 颜色(4个byte)
const stride = 3 * 4 + 4 * 1; // 16字节
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);
gl.vertexAttribPointer(colorLocation, 4, gl.UNSIGNED_BYTE, true, stride, 12);
```

**注意事项：**

-   必须先绑定相应的缓冲区
-   stride 和 offset 以字节为单位
-   归一化只对整数类型有效

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.vertexAttrib1f(index, x)`

**函数签名：**

```javascript
gl.vertexAttrib1f(index, x);
```

**作用：** 为顶点属性设置单个浮点常量值

**参数详解：**

-   `index` (GLuint) - 属性位置索引
-   `x` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.vertexAttrib1f(alphaLocation, 0.5); // 设置透明度
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.vertexAttrib2f(index, x, y)`

**函数签名：**

```javascript
gl.vertexAttrib2f(index, x, y);
```

**作用：** 为顶点属性设置两个浮点常量值

**参数详解：**

-   `index` (GLuint) - 属性位置索引
-   `x, y` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.vertexAttrib2f(texCoordLocation, 0.5, 0.5); // 设置纹理坐标
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.vertexAttrib3f(index, x, y, z)`

**函数签名：**

```javascript
gl.vertexAttrib3f(index, x, y, z);
```

**作用：** 为顶点属性设置三个浮点常量值

**参数详解：**

-   `index` (GLuint) - 属性位置索引
-   `x, y, z` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.vertexAttrib3f(colorLocation, 1.0, 0.0, 0.0); // 设置红色
gl.vertexAttrib3f(normalLocation, 0.0, 1.0, 0.0); // 设置法向量
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.vertexAttrib4f(index, x, y, z, w)`

**函数签名：**

```javascript
gl.vertexAttrib4f(index, x, y, z, w);
```

**作用：** 为顶点属性设置四个浮点常量值

**参数详解：**

-   `index` (GLuint) - 属性位置索引
-   `x, y, z, w` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.vertexAttrib4f(colorLocation, 1.0, 0.0, 0.0, 1.0); // 设置RGBA颜色
```

═══════════════════════════════════════════════════════════════════════════════

## 纹理处理

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 纹理处理函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.createTexture()`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.createTexture();
```

**作用：** 创建一个新的纹理对象

**参数：** 无

**返回值：** WebGLTexture 对象，如果失败返回 null

**代码示例：**

```javascript
const texture = gl.createTexture();
if (!texture) {
    console.error('无法创建纹理对象');
}
```

**注意事项：**

-   创建的纹理需要绑定并配置才能使用
-   使用完毕后应调用`deleteTexture()`释放资源

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.bindTexture(target, texture)`

**函数签名：**

```javascript
gl.bindTexture(target, texture);
```

**作用：** 将纹理对象绑定到指定的纹理目标

**参数详解：**

-   `target` (GLenum) - 纹理绑定目标
    -   `gl.TEXTURE_2D` (0x0DE1) - 2D 纹理
    -   `gl.TEXTURE_CUBE_MAP` (0x8513) - 立方体贴图纹理
    -   `gl.TEXTURE_3D` (0x806F) - 3D 纹理 (WebGL 2.0)
    -   `gl.TEXTURE_2D_ARRAY` (0x8C1A) - 2D 纹理数组 (WebGL 2.0)
-   `texture` (WebGLTexture | null) - 要绑定的纹理对象，null 表示解绑

**返回值：** 无

**代码示例：**

```javascript
// 绑定2D纹理
gl.bindTexture(gl.TEXTURE_2D, texture);

// 绑定立方体贴图
gl.bindTexture(gl.TEXTURE_CUBE_MAP, cubeTexture);

// WebGL 2.0 - 绑定3D纹理
gl.bindTexture(gl.TEXTURE_3D, texture3D);

// 解绑当前纹理
gl.bindTexture(gl.TEXTURE_2D, null);
```

**注意事项：**

-   同一时间每个纹理目标只能绑定一个纹理
-   绑定后的操作会影响当前绑定的纹理

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.texImage2D(target, level, internalFormat, width, height, border, format, type, data)`

**函数签名：**

```javascript
// 方式1：使用图像元素
gl.texImage2D(target, level, internalFormat, format, type, source);

// 方式2：使用像素数据
gl.texImage2D(target, level, internalFormat, width, height, border, format, type, pixels);
```

**作用：** 为 2D 纹理指定图像数据

**参数详解：**

-   `target` (GLenum) - 纹理目标
    -   `gl.TEXTURE_2D` - 2D 纹理
    -   `gl.TEXTURE_CUBE_MAP_POSITIVE_X` - 立方体贴图+X 面
    -   `gl.TEXTURE_CUBE_MAP_NEGATIVE_X` - 立方体贴图-X 面
    -   `gl.TEXTURE_CUBE_MAP_POSITIVE_Y` - 立方体贴图+Y 面
    -   `gl.TEXTURE_CUBE_MAP_NEGATIVE_Y` - 立方体贴图-Y 面
    -   `gl.TEXTURE_CUBE_MAP_POSITIVE_Z` - 立方体贴图+Z 面
    -   `gl.TEXTURE_CUBE_MAP_NEGATIVE_Z` - 立方体贴图-Z 面
-   `level` (GLint) - mipmap 级别，0 为基础级别
-   `internalFormat` (GLint) - 纹理内部格式
    -   `gl.ALPHA` - Alpha 通道
    -   `gl.RGB` - RGB 颜色
    -   `gl.RGBA` - RGBA 颜色
    -   `gl.LUMINANCE` - 亮度
    -   `gl.LUMINANCE_ALPHA` - 亮度+Alpha
-   `width` (GLsizei) - 纹理宽度
-   `height` (GLsizei) - 纹理高度
-   `border` (GLint) - 边框宽度，必须为 0
-   `format` (GLenum) - 像素数据格式（通常与 internalFormat 相同）
-   `type` (GLenum) - 像素数据类型
    -   `gl.UNSIGNED_BYTE` - 无符号字节
    -   `gl.UNSIGNED_SHORT_5_6_5` - RGB565 格式
    -   `gl.UNSIGNED_SHORT_4_4_4_4` - RGBA4444 格式
    -   `gl.UNSIGNED_SHORT_5_5_5_1` - RGBA5551 格式
-   `source` - 图像源（HTMLImageElement、HTMLCanvasElement、HTMLVideoElement 等）
-   `pixels` (ArrayBufferView | null) - 像素数据数组

**代码示例：**

```javascript
// 使用图像元素
const image = new Image();
image.onload = function () {
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);
    gl.generateMipmap(gl.TEXTURE_2D);
};
image.src = 'texture.jpg';

// 使用像素数据
const pixels = new Uint8Array([
    255,
    0,
    0,
    255, // 红色像素
    0,
    255,
    0,
    255, // 绿色像素
    0,
    0,
    255,
    255, // 蓝色像素
    255,
    255,
    0,
    255, // 黄色像素
]);
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 2, 2, 0, gl.RGBA, gl.UNSIGNED_BYTE, pixels);

// 创建空纹理
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 512, 512, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);
```

**注意事项：**

-   纹理尺寸在 WebGL 1.0 中通常需要是 2 的幂次方
-   WebGL 2.0 支持任意尺寸的纹理
-   使用图像元素时需要等待图像加载完成

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.texParameteri(target, pname, param)`

**函数签名：**

```javascript
gl.texParameteri(target, pname, param);
```

**作用：** 设置纹理的整数参数

**参数详解：**

-   `target` (GLenum) - 纹理目标
-   `pname` (GLenum) - 参数名称
    -   `gl.TEXTURE_MIN_FILTER` (0x2801) - 缩小过滤方式
        -   `gl.NEAREST` (0x2600) - 最近邻过滤
        -   `gl.LINEAR` (0x2601) - 线性过滤
        -   `gl.NEAREST_MIPMAP_NEAREST` (0x2700) - 最近邻 mipmap，最近邻过滤
        -   `gl.LINEAR_MIPMAP_NEAREST` (0x2701) - 最近邻 mipmap，线性过滤
        -   `gl.NEAREST_MIPMAP_LINEAR` (0x2702) - 线性 mipmap，最近邻过滤
        -   `gl.LINEAR_MIPMAP_LINEAR` (0x2703) - 线性 mipmap，线性过滤
    -   `gl.TEXTURE_MAG_FILTER` (0x2800) - 放大过滤方式
        -   `gl.NEAREST` - 最近邻过滤
        -   `gl.LINEAR` - 线性过滤
    -   `gl.TEXTURE_WRAP_S` (0x2802) - S 轴（水平）包装模式
        -   `gl.REPEAT` (0x2901) - 重复
        -   `gl.CLAMP_TO_EDGE` (0x812F) - 夹紧到边缘
        -   `gl.MIRRORED_REPEAT` (0x8370) - 镜像重复
    -   `gl.TEXTURE_WRAP_T` (0x2803) - T 轴（垂直）包装模式
        -   值同 TEXTURE_WRAP_S
    -   `gl.TEXTURE_WRAP_R` (0x8072) - R 轴包装模式 (WebGL 2.0, 3D 纹理)
-   `param` (GLint) - 参数值

**代码示例：**

```javascript
// 设置过滤方式
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

// 设置包装模式
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

// 使用mipmap的过滤
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR_MIPMAP_LINEAR);

// 重复纹理
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.REPEAT);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.REPEAT);
```

**注意事项：**

-   使用 mipmap 过滤需要先生成 mipmap
-   非 2 的幂次方纹理在 WebGL 1.0 中只能使用 CLAMP_TO_EDGE 包装模式

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.texParameterf(target, pname, param)`

**函数签名：**

```javascript
gl.texParameterf(target, pname, param);
```

**作用：** 设置纹理的浮点参数

**参数详解：**

-   `target` (GLenum) - 纹理目标
-   `pname` (GLenum) - 参数名称（同 texParameteri）
-   `param` (GLfloat) - 浮点参数值

**代码示例：**

```javascript
// 设置浮点过滤参数
gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.generateMipmap(target)`

**函数签名：**

```javascript
gl.generateMipmap(target);
```

**作用：** 为指定的纹理目标生成 mipmap 链

**参数详解：**

-   `target` (GLenum) - 纹理目标
    -   `gl.TEXTURE_2D` - 2D 纹理
    -   `gl.TEXTURE_CUBE_MAP` - 立方体贴图

**返回值：** 无

**代码示例：**

```javascript
// 为2D纹理生成mipmap
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);
gl.generateMipmap(gl.TEXTURE_2D);

// 为立方体贴图生成mipmap
gl.bindTexture(gl.TEXTURE_CUBE_MAP, cubeTexture);
// ... 设置6个面的纹理数据
gl.generateMipmap(gl.TEXTURE_CUBE_MAP);
```

**注意事项：**

-   纹理必须是 2 的幂次方尺寸才能生成 mipmap（WebGL 1.0）
-   WebGL 2.0 支持任意尺寸纹理的 mipmap 生成
-   生成 mipmap 后可以使用 mipmap 过滤模式

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.activeTexture(texture)`

**函数签名：**

```javascript
gl.activeTexture(texture);
```

**作用：** 激活指定的纹理单元，后续的纹理操作将作用于该单元

**参数详解：**

-   `texture` (GLenum) - 纹理单元标识符
    -   `gl.TEXTURE0` 到 `gl.TEXTURE31` - 纹理单元 0 到 31
    -   实际可用单元数量取决于硬件支持

**返回值：** 无

**代码示例：**

```javascript
// 激活纹理单元0
gl.activeTexture(gl.TEXTURE0);
gl.bindTexture(gl.TEXTURE_2D, texture1);

// 激活纹理单元1
gl.activeTexture(gl.TEXTURE1);
gl.bindTexture(gl.TEXTURE_2D, texture2);

// 在着色器中使用多个纹理
const texture1Location = gl.getUniformLocation(program, 'u_texture1');
const texture2Location = gl.getUniformLocation(program, 'u_texture2');
gl.uniform1i(texture1Location, 0); // 使用纹理单元0
gl.uniform1i(texture2Location, 1); // 使用纹理单元1

// 查询最大纹理单元数量
const maxTextureUnits = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);
console.log('最大纹理单元数量:', maxTextureUnits);
```

**注意事项：**

-   默认激活的是 TEXTURE0
-   不同的纹理单元可以绑定不同的纹理
-   纹理单元编号从 0 开始

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.isTexture(texture)`

**函数签名：**

```javascript
gl.isTexture(texture);
```

**作用：** 检查对象是否为有效的纹理对象

**参数详解：**

-   `texture` (any) - 要检查的对象

**返回值：** boolean - true 表示是有效的纹理对象

**代码示例：**

```javascript
if (gl.isTexture(texture)) {
    console.log('这是一个有效的纹理对象');
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.deleteTexture(texture)`

**函数签名：**

```javascript
gl.deleteTexture(texture);
```

**作用：** 删除纹理对象并释放相关资源

**参数详解：**

-   `texture` (WebGLTexture) - 要删除的纹理对象

**返回值：** 无

**代码示例：**

```javascript
// 删除纹理
gl.deleteTexture(texture);
```

**注意事项：**

-   如果纹理正在使用中，删除操作会被延迟
-   删除后的纹理对象不能再使用

═══════════════════════════════════════════════════════════════════════════════

## Uniform 变量函数

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Uniform 变量函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.getUniformLocation(program, name)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.getUniformLocation(program, name);
```

**作用：** 获取着色器程序中 uniform 变量的位置

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象
-   `name` (string) - uniform 变量名

**返回值：** WebGLUniformLocation 对象，如果未找到返回 null

**代码示例：**

```javascript
const modelMatrixLocation = gl.getUniformLocation(program, 'u_modelMatrix');
const colorLocation = gl.getUniformLocation(program, 'u_color');
const textureLocation = gl.getUniformLocation(program, 'u_texture');

if (!modelMatrixLocation) {
    console.warn('未找到uniform变量: u_modelMatrix');
}
```

**注意事项：**

-   变量名必须与着色器中声明的名称完全匹配
-   如果变量未被使用，可能会被优化器移除
-   返回的位置对象在程序生命周期内保持不变

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform1f(location, x)`

**函数签名：**

```javascript
gl.uniform1f(location, x);
```

**作用：** 设置单个浮点数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.uniform1f(timeLocation, currentTime);
gl.uniform1f(alphaLocation, 0.5);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform2f(location, x, y)`

**函数签名：**

```javascript
gl.uniform2f(location, x, y);
```

**作用：** 设置两个浮点数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x, y` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.uniform2f(resolutionLocation, canvas.width, canvas.height);
gl.uniform2f(mouseLocation, mouseX, mouseY);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform3f(location, x, y, z)`

**函数签名：**

```javascript
gl.uniform3f(location, x, y, z);
```

**作用：** 设置三个浮点数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x, y, z` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.uniform3f(colorLocation, 1.0, 0.0, 0.0); // 红色
gl.uniform3f(lightPositionLocation, 10.0, 10.0, 10.0);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform4f(location, x, y, z, w)`

**函数签名：**

```javascript
gl.uniform4f(location, x, y, z, w);
```

**作用：** 设置四个浮点数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x, y, z, w` (GLfloat) - 浮点值

**代码示例：**

```javascript
gl.uniform4f(colorLocation, 1.0, 0.0, 0.0, 1.0); // RGBA颜色
gl.uniform4f(vectorLocation, x, y, z, w);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform1i(location, x)`

**函数签名：**

```javascript
gl.uniform1i(location, x);
```

**作用：** 设置单个整数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x` (GLint) - 整数值

**代码示例：**

```javascript
gl.uniform1i(textureLocation, 0); // 纹理单元0
gl.uniform1i(enableLightingLocation, 1); // 启用光照
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform2i(location, x, y)`

**函数签名：**

```javascript
gl.uniform2i(location, x, y);
```

**作用：** 设置两个整数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x, y` (GLint) - 整数值

**代码示例：**

```javascript
gl.uniform2i(offsetLocation, tileX, tileY);
gl.uniform2i(gridSizeLocation, gridWidth, gridHeight);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform3i(location, x, y, z)`

**函数签名：**

```javascript
gl.uniform3i(location, x, y, z);
```

**作用：** 设置三个整数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x, y, z` (GLint) - 整数值

**代码示例：**

```javascript
gl.uniform3i(rgbLocation, 255, 128, 64);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform4i(location, x, y, z, w)`

**函数签名：**

```javascript
gl.uniform4i(location, x, y, z, w);
```

**作用：** 设置四个整数 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `x, y, z, w` (GLint) - 整数值

**代码示例：**

```javascript
gl.uniform4i(rgbaLocation, 255, 128, 64, 255);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform1fv(location, array)`

**函数签名：**

```javascript
gl.uniform1fv(location, array);
```

**作用：** 设置浮点数数组 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `array` (Float32Array | Array) - 浮点数数组

**代码示例：**

```javascript
const weights = new Float32Array([0.2, 0.3, 0.5]);
gl.uniform1fv(weightsLocation, weights);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform2fv(location, array)`

**函数签名：**

```javascript
gl.uniform2fv(location, array);
```

**作用：** 设置二维向量数组 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `array` (Float32Array | Array) - 浮点数数组，长度必须是 2 的倍数

**代码示例：**

```javascript
const positions = new Float32Array([1.0, 2.0, 3.0, 4.0]); // 两个vec2
gl.uniform2fv(positionsLocation, positions);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform3fv(location, array)`

**函数签名：**

```javascript
gl.uniform3fv(location, array);
```

**作用：** 设置三维向量数组 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `array` (Float32Array | Array) - 浮点数数组，长度必须是 3 的倍数

**代码示例：**

```javascript
const lightPositions = new Float32Array([
    10.0,
    10.0,
    10.0, // 第一个光源位置
    -5.0,
    15.0,
    8.0, // 第二个光源位置
]);
gl.uniform3fv(lightPositionsLocation, lightPositions);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniform4fv(location, array)`

**函数签名：**

```javascript
gl.uniform4fv(location, array);
```

**作用：** 设置四维向量数组 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `array` (Float32Array | Array) - 浮点数数组，长度必须是 4 的倍数

**代码示例：**

```javascript
const colors = new Float32Array([
    1.0,
    0.0,
    0.0,
    1.0, // 红色
    0.0,
    1.0,
    0.0,
    1.0, // 绿色
    0.0,
    0.0,
    1.0,
    1.0, // 蓝色
]);
gl.uniform4fv(colorsLocation, colors);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniformMatrix2fv(location, transpose, matrix)`

**函数签名：**

```javascript
gl.uniformMatrix2fv(location, transpose, matrix);
```

**作用：** 设置 2x2 矩阵 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `transpose` (GLboolean) - 是否转置矩阵，WebGL 中通常为 false
-   `matrix` (Float32Array | Array) - 矩阵数据，长度必须是 4 的倍数

**代码示例：**

```javascript
const matrix2x2 = new Float32Array([1.0, 0.0, 0.0, 1.0]);
gl.uniformMatrix2fv(matrix2Location, false, matrix2x2);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniformMatrix3fv(location, transpose, matrix)`

**函数签名：**

```javascript
gl.uniformMatrix3fv(location, transpose, matrix);
```

**作用：** 设置 3x3 矩阵 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `transpose` (GLboolean) - 是否转置矩阵，WebGL 中通常为 false
-   `matrix` (Float32Array | Array) - 矩阵数据，长度必须是 9 的倍数

**代码示例：**

```javascript
const normalMatrix = new Float32Array([1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0]);
gl.uniformMatrix3fv(normalMatrixLocation, false, normalMatrix);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.uniformMatrix4fv(location, transpose, matrix)`

**函数签名：**

```javascript
gl.uniformMatrix4fv(location, transpose, matrix);
```

**作用：** 设置 4x4 矩阵 uniform 值

**参数详解：**

-   `location` (WebGLUniformLocation) - uniform 变量位置
-   `transpose` (GLboolean) - 是否转置矩阵，WebGL 中通常为 false
-   `matrix` (Float32Array | Array) - 矩阵数据，长度必须是 16 的倍数

**代码示例：**

```javascript
const modelMatrix = new Float32Array([1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]);
gl.uniformMatrix4fv(modelMatrixLocation, false, modelMatrix);

// 使用矩阵库
const projectionMatrix = mat4.perspective((45 * Math.PI) / 180, aspect, 0.1, 100.0);
gl.uniformMatrix4fv(projectionMatrixLocation, false, projectionMatrix);
```

**注意事项：**

-   WebGL 使用列主序存储矩阵
-   transpose 参数在 WebGL 中通常设为 false
-   矩阵数据必须是 Float32Array 或普通数组

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getUniform(program, location)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.getUniform(program, location);
```

**作用：** 获取指定 uniform 变量的当前值，用于调试和状态检查

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象，必须是已链接的有效程序
-   `location` (WebGLUniformLocation) - uniform 变量的位置对象，通过 getUniformLocation() 获取

**返回值：** 根据 uniform 变量类型返回相应的值：

-   **标量类型**：返回对应的 JavaScript 数值
    -   `float` → `number`
    -   `int`/`bool` → `number` (整数)
-   **向量类型**：返回 Float32Array 或 Int32Array
    -   `vec2`/`vec3`/`vec4` → `Float32Array`
    -   `ivec2`/`ivec3`/`ivec4` → `Int32Array`
    -   `bvec2`/`bvec3`/`bvec4` → `Array<boolean>`
-   **矩阵类型**：返回 Float32Array（列主序）
    -   `mat2`/`mat3`/`mat4` → `Float32Array`
-   **采样器类型**：返回绑定的纹理单元索引
    -   `sampler2D`/`samplerCube` → `number`

**代码示例：**

```javascript
// 基本用法示例
const program = createShaderProgram(gl, vertexShader, fragmentShader);
const timeLocation = gl.getUniformLocation(program, 'u_time');
const colorLocation = gl.getUniformLocation(program, 'u_color');
const modelMatrixLocation = gl.getUniformLocation(program, 'u_modelMatrix');

// 设置一些 uniform 值
gl.useProgram(program);
gl.uniform1f(timeLocation, 1.5);
gl.uniform3f(colorLocation, 1.0, 0.5, 0.2);
const modelMatrix = new Float32Array([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);
gl.uniformMatrix4fv(modelMatrixLocation, false, modelMatrix);

// 获取 uniform 值进行验证
const currentTime = gl.getUniform(program, timeLocation);
console.log('当前时间值:', currentTime); // 输出: 1.5

const currentColor = gl.getUniform(program, colorLocation);
console.log('当前颜色值:', currentColor); // 输出: Float32Array [1, 0.5, 0.2]

const currentMatrix = gl.getUniform(program, modelMatrixLocation);
console.log('当前模型矩阵:', currentMatrix); // 输出: Float32Array [1, 0, 0, 0, ...]

// 调试工具函数：打印所有 uniform 值
function debugUniforms(gl, program) {
    const numUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    console.log(`程序包含 ${numUniforms} 个活跃的 uniform 变量:`);

    for (let i = 0; i < numUniforms; i++) {
        const uniformInfo = gl.getActiveUniform(program, i);
        const location = gl.getUniformLocation(program, uniformInfo.name);

        if (location) {
            const value = gl.getUniform(program, location);
            console.log(`${uniformInfo.name}:`, value);
        }
    }
}

// 状态验证函数
function validateUniformState(gl, program, expectedValues) {
    let isValid = true;

    for (const [uniformName, expectedValue] of Object.entries(expectedValues)) {
        const location = gl.getUniformLocation(program, uniformName);
        if (!location) {
            console.warn(`Uniform ${uniformName} 不存在`);
            continue;
        }

        const actualValue = gl.getUniform(program, location);

        // 比较值（需要考虑浮点精度）
        if (Array.isArray(expectedValue) || expectedValue instanceof Float32Array) {
            for (let i = 0; i < expectedValue.length; i++) {
                if (Math.abs(actualValue[i] - expectedValue[i]) > 0.0001) {
                    console.error(`Uniform ${uniformName}[${i}] 值不匹配: 期望 ${expectedValue[i]}, 实际 ${actualValue[i]}`);
                    isValid = false;
                }
            }
        } else {
            if (Math.abs(actualValue - expectedValue) > 0.0001) {
                console.error(`Uniform ${uniformName} 值不匹配: 期望 ${expectedValue}, 实际 ${actualValue}`);
                isValid = false;
            }
        }
    }

    return isValid;
}

// 使用示例
const expectedState = {
    u_time: 1.5,
    u_color: [1.0, 0.5, 0.2],
    u_opacity: 1.0,
};

if (validateUniformState(gl, program, expectedState)) {
    console.log('所有 uniform 状态正确');
} else {
    console.error('uniform 状态验证失败');
}

// 性能监控：检查 uniform 更新频率
class UniformMonitor {
    constructor(gl, program) {
        this.gl = gl;
        this.program = program;
        this.lastValues = new Map();
        this.updateCounts = new Map();
    }

    checkUniform(name) {
        const location = this.gl.getUniformLocation(this.program, name);
        if (!location) return;

        const currentValue = this.gl.getUniform(this.program, location);
        const lastValue = this.lastValues.get(name);

        if (!this.valuesEqual(currentValue, lastValue)) {
            this.updateCounts.set(name, (this.updateCounts.get(name) || 0) + 1);
            this.lastValues.set(name, currentValue);
        }
    }

    valuesEqual(a, b) {
        if (a === b) return true;
        if (!a || !b) return false;
        if (a.length !== b.length) return false;

        for (let i = 0; i < a.length; i++) {
            if (Math.abs(a[i] - b[i]) > 0.0001) return false;
        }
        return true;
    }

    getStats() {
        return Object.fromEntries(this.updateCounts);
    }
}
```

**使用场景：**

1. **调试和验证**：检查 uniform 值是否正确设置
2. **状态管理**：在复杂应用中跟踪 uniform 状态
3. **性能分析**：监控 uniform 更新频率
4. **单元测试**：验证着色器程序的状态

**注意事项：**

-   只能获取当前绑定程序的 uniform 值
-   返回值类型取决于 uniform 的 GLSL 类型
-   对于数组 uniform，需要分别获取每个元素
-   采样器类型返回的是纹理单元索引，不是纹理对象
-   频繁调用可能影响性能，建议仅用于调试
-   如果 uniform 未被使用，可能被优化器移除，导致 location 为 null

**错误处理：**

```javascript
function safeGetUniform(gl, program, location) {
    try {
        if (!location) {
            console.warn('Invalid uniform location');
            return null;
        }

        const value = gl.getUniform(program, location);

        // 检查 WebGL 错误
        const error = gl.getError();
        if (error !== gl.NO_ERROR) {
            console.error('getUniform 错误:', error);
            return null;
        }

        return value;
    } catch (e) {
        console.error('getUniform 异常:', e);
        return null;
    }
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.getActiveUniform(program, index)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.getActiveUniform(program, index);
```

**作用：** 获取着色器程序中指定索引的活跃 uniform 变量的详细信息，包括名称、类型、大小等元数据

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序对象，必须是已链接的有效程序
-   `index` (GLuint) - uniform 变量的索引，范围 [0, ACTIVE_UNIFORMS-1]

**返回值：** WebGLActiveInfo 对象，包含以下属性：

-   `name` (string) - uniform 变量名称
    -   对于数组：可能包含 `[0]` 后缀（如 `u_lights[0]`）
    -   对于结构体：可能包含成员名（如 `u_material.diffuse`）
-   `type` (GLenum) - uniform 变量的 GLSL 类型常量：
    -   **标量类型**：
        -   `gl.FLOAT` (5126) - `float`
        -   `gl.INT` (5124) - `int`
        -   `gl.BOOL` (35670) - `bool`
    -   **向量类型**：
        -   `gl.FLOAT_VEC2` (35664) - `vec2`
        -   `gl.FLOAT_VEC3` (35665) - `vec3`
        -   `gl.FLOAT_VEC4` (35666) - `vec4`
        -   `gl.INT_VEC2` (35667) - `ivec2`
        -   `gl.INT_VEC3` (35668) - `ivec3`
        -   `gl.INT_VEC4` (35669) - `ivec4`
        -   `gl.BOOL_VEC2` (35671) - `bvec2`
        -   `gl.BOOL_VEC3` (35672) - `bvec3`
        -   `gl.BOOL_VEC4` (35673) - `bvec4`
    -   **矩阵类型**：
        -   `gl.FLOAT_MAT2` (35674) - `mat2`
        -   `gl.FLOAT_MAT3` (35675) - `mat3`
        -   `gl.FLOAT_MAT4` (35676) - `mat4`
    -   **采样器类型**：
        -   `gl.SAMPLER_2D` (35678) - `sampler2D`
        -   `gl.SAMPLER_CUBE` (35680) - `samplerCube`
-   `size` (GLint) - 数组大小，非数组变量为 1

**代码示例：**

```javascript
// 基本用法：遍历所有活跃的 uniform 变量
function analyzeUniforms(gl, program) {
    const numUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    console.log(`程序包含 ${numUniforms} 个活跃的 uniform 变量:`);

    const uniformInfo = [];

    for (let i = 0; i < numUniforms; i++) {
        const info = gl.getActiveUniform(program, i);
        const location = gl.getUniformLocation(program, info.name);

        uniformInfo.push({
            index: i,
            name: info.name,
            type: info.type,
            size: info.size,
            location: location,
            typeName: getUniformTypeName(info.type),
            isArray: info.size > 1,
        });

        console.log(`[${i}] ${info.name}: ${getUniformTypeName(info.type)}${info.size > 1 ? `[${info.size}]` : ''}`);
    }

    return uniformInfo;
}

// 类型名称映射函数
function getUniformTypeName(type) {
    const typeMap = {
        [5126]: 'float', // gl.FLOAT
        [5124]: 'int', // gl.INT
        [35670]: 'bool', // gl.BOOL
        [35664]: 'vec2', // gl.FLOAT_VEC2
        [35665]: 'vec3', // gl.FLOAT_VEC3
        [35666]: 'vec4', // gl.FLOAT_VEC4
        [35667]: 'ivec2', // gl.INT_VEC2
        [35668]: 'ivec3', // gl.INT_VEC3
        [35669]: 'ivec4', // gl.INT_VEC4
        [35671]: 'bvec2', // gl.BOOL_VEC2
        [35672]: 'bvec3', // gl.BOOL_VEC3
        [35673]: 'bvec4', // gl.BOOL_VEC4
        [35674]: 'mat2', // gl.FLOAT_MAT2
        [35675]: 'mat3', // gl.FLOAT_MAT3
        [35676]: 'mat4', // gl.FLOAT_MAT4
        [35678]: 'sampler2D', // gl.SAMPLER_2D
        [35680]: 'samplerCube', // gl.SAMPLER_CUBE
    };
    return typeMap[type] || `未知类型(${type})`;
}

// 高级分析：按类型分组 uniform 变量
function categorizeUniforms(gl, program) {
    const numUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    const categories = {
        scalars: [],
        vectors: [],
        matrices: [],
        samplers: [],
        arrays: [],
        structs: [],
    };

    for (let i = 0; i < numUniforms; i++) {
        const info = gl.getActiveUniform(program, i);
        const location = gl.getUniformLocation(program, info.name);

        const uniformData = {
            name: info.name,
            type: info.type,
            size: info.size,
            location: location,
            typeName: getUniformTypeName(info.type),
        };

        // 按类型分类
        if (info.size > 1) {
            categories.arrays.push(uniformData);
        } else if (info.name.includes('.')) {
            categories.structs.push(uniformData);
        } else if (info.type >= 35678) {
            // 采样器类型
            categories.samplers.push(uniformData);
        } else if (info.type >= 35674 && info.type <= 35676) {
            // 矩阵类型
            categories.matrices.push(uniformData);
        } else if (info.type >= 35664 && info.type <= 35673) {
            // 向量类型
            categories.vectors.push(uniformData);
        } else {
            // 标量类型
            categories.scalars.push(uniformData);
        }
    }

    return categories;
}

// 自动生成 uniform 设置代码
function generateUniformSetters(gl, program) {
    const numUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    const setters = {};

    for (let i = 0; i < numUniforms; i++) {
        const info = gl.getActiveUniform(program, i);
        const location = gl.getUniformLocation(program, info.name);

        if (!location) continue;

        // 生成对应的设置函数
        const setterName = info.name.replace(/\[.*\]/, ''); // 移除数组后缀

        switch (info.type) {
            case gl.FLOAT:
                setters[setterName] = (value) => gl.uniform1f(location, value);
                break;
            case gl.FLOAT_VEC2:
                setters[setterName] = (value) => gl.uniform2fv(location, value);
                break;
            case gl.FLOAT_VEC3:
                setters[setterName] = (value) => gl.uniform3fv(location, value);
                break;
            case gl.FLOAT_VEC4:
                setters[setterName] = (value) => gl.uniform4fv(location, value);
                break;
            case gl.INT:
            case gl.BOOL:
            case gl.SAMPLER_2D:
            case gl.SAMPLER_CUBE:
                setters[setterName] = (value) => gl.uniform1i(location, value);
                break;
            case gl.INT_VEC2:
                setters[setterName] = (value) => gl.uniform2iv(location, value);
                break;
            case gl.INT_VEC3:
                setters[setterName] = (value) => gl.uniform3iv(location, value);
                break;
            case gl.INT_VEC4:
                setters[setterName] = (value) => gl.uniform4iv(location, value);
                break;
            case gl.FLOAT_MAT2:
                setters[setterName] = (value) => gl.uniformMatrix2fv(location, false, value);
                break;
            case gl.FLOAT_MAT3:
                setters[setterName] = (value) => gl.uniformMatrix3fv(location, false, value);
                break;
            case gl.FLOAT_MAT4:
                setters[setterName] = (value) => gl.uniformMatrix4fv(location, false, value);
                break;
        }
    }

    return setters;
}

// 使用示例
const program = createShaderProgram(gl, vertexShader, fragmentShader);

// 分析程序中的所有 uniform
const uniformAnalysis = analyzeUniforms(gl, program);

// 按类型分类
const categories = categorizeUniforms(gl, program);
console.log('标量 uniforms:', categories.scalars);
console.log('向量 uniforms:', categories.vectors);
console.log('矩阵 uniforms:', categories.matrices);
console.log('采样器 uniforms:', categories.samplers);

// 生成自动设置器
const setters = generateUniformSetters(gl, program);

// 使用生成的设置器
if (setters.u_time) {
    setters.u_time(performance.now() * 0.001);
}
if (setters.u_color) {
    setters.u_color([1.0, 0.5, 0.2]);
}

// 验证 uniform 完整性
function validateUniformCompleteness(gl, program, requiredUniforms) {
    const numUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    const activeUniforms = new Set();

    for (let i = 0; i < numUniforms; i++) {
        const info = gl.getActiveUniform(program, i);
        activeUniforms.add(info.name.replace(/\[.*\]/, '')); // 移除数组索引
    }

    const missing = requiredUniforms.filter((name) => !activeUniforms.has(name));
    const extra = Array.from(activeUniforms).filter((name) => !requiredUniforms.includes(name));

    return {
        isComplete: missing.length === 0,
        missing: missing,
        extra: extra,
        active: Array.from(activeUniforms),
    };
}

// 使用验证
const requiredUniforms = ['u_time', 'u_modelMatrix', 'u_viewMatrix', 'u_projectionMatrix'];
const validation = validateUniformCompleteness(gl, program, requiredUniforms);

if (!validation.isComplete) {
    console.error('缺少必需的 uniform:', validation.missing);
}
if (validation.extra.length > 0) {
    console.warn('额外的 uniform:', validation.extra);
}
```

**使用场景：**

1. **程序分析**：了解着色器程序的 uniform 接口
2. **自动化工具**：生成 uniform 设置代码或文档
3. **调试和验证**：检查 uniform 是否正确声明
4. **性能优化**：识别未使用的 uniform 变量
5. **接口验证**：确保着色器接口与应用代码匹配

**注意事项：**

-   只返回"活跃"的 uniform（被着色器实际使用的）
-   未使用的 uniform 可能被编译器优化掉
-   数组 uniform 的名称可能包含 `[0]` 后缀
-   结构体成员会作为单独的 uniform 返回
-   索引必须在有效范围内，否则返回 null
-   程序必须已成功链接

**与 32 位限制和位标志冲突的关系：**

在复杂的 WebGL 应用中，uniform 变量的管理对于避免位标志冲突和处理 32 位限制非常重要：

```javascript
// 处理大量 uniform 的策略
class UniformManager {
    constructor(gl, program) {
        this.gl = gl;
        this.program = program;
        this.uniformCache = new Map();
        this.initializeUniforms();
    }

    initializeUniforms() {
        const numUniforms = this.gl.getProgramParameter(this.program, this.gl.ACTIVE_UNIFORMS);

        for (let i = 0; i < numUniforms; i++) {
            const info = this.gl.getActiveUniform(this.program, i);
            const location = this.gl.getUniformLocation(this.program, info.name);

            this.uniformCache.set(info.name, {
                location: location,
                type: info.type,
                size: info.size,
                lastValue: null,
                updateCount: 0,
            });
        }
    }

    // 智能更新：只在值改变时更新
    setUniform(name, value) {
        const uniform = this.uniformCache.get(name);
        if (!uniform || !uniform.location) return;

        // 避免重复设置相同的值
        if (this.valuesEqual(uniform.lastValue, value)) return;

        // 根据类型设置值
        this.setUniformByType(uniform.location, uniform.type, value);
        uniform.lastValue = value;
        uniform.updateCount++;
    }

    valuesEqual(a, b) {
        if (a === b) return true;
        if (!a || !b) return false;
        if (a.length !== b.length) return false;

        for (let i = 0; i < a.length; i++) {
            if (Math.abs(a[i] - b[i]) > 0.0001) return false;
        }
        return true;
    }

    setUniformByType(location, type, value) {
        switch (type) {
            case this.gl.FLOAT:
                this.gl.uniform1f(location, value);
                break;
            case this.gl.FLOAT_VEC3:
                this.gl.uniform3fv(location, value);
                break;
            case this.gl.FLOAT_MAT4:
                this.gl.uniformMatrix4fv(location, false, value);
                break;
            // ... 其他类型
        }
    }

    getStats() {
        const stats = {};
        for (const [name, uniform] of this.uniformCache) {
            stats[name] = uniform.updateCount;
        }
        return stats;
    }
}
```

═══════════════════════════════════════════════════════════════════════════════

## 渲染状态管理

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 渲染状态管理函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.viewport(x, y, width, height)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.viewport(x, y, width, height);
```

**作用：** 设置视口变换参数，定义从标准化设备坐标到窗口坐标的变换

**参数详解：**

-   `x` (GLint) - 视口左下角的 x 坐标（像素）
-   `y` (GLint) - 视口左下角的 y 坐标（像素）
-   `width` (GLsizei) - 视口宽度（像素）
-   `height` (GLsizei) - 视口高度（像素）

**返回值：** 无

**代码示例：**

```javascript
// 设置全屏视口
gl.viewport(0, 0, canvas.width, canvas.height);

// 设置左半屏视口
gl.viewport(0, 0, canvas.width / 2, canvas.height);

// 设置右半屏视口
gl.viewport(canvas.width / 2, 0, canvas.width / 2, canvas.height);

// 获取当前视口设置
const viewport = gl.getParameter(gl.VIEWPORT);
console.log('视口:', viewport); // [x, y, width, height]

// 响应画布大小变化
function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    gl.viewport(0, 0, canvas.width, canvas.height);
}
```

**注意事项：**

-   视口坐标系原点在左下角
-   宽度和高度必须为正数
-   超出画布范围的视口会被裁剪

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.scissor(x, y, width, height)`

**函数签名：**

```javascript
gl.scissor(x, y, width, height);
```

**作用：** 设置裁剪矩形区域，只有在此区域内的像素才会被渲染

**参数详解：**

-   `x` (GLint) - 裁剪区域左下角的 x 坐标
-   `y` (GLint) - 裁剪区域左下角的 y 坐标
-   `width` (GLsizei) - 裁剪区域宽度
-   `height` (GLsizei) - 裁剪区域高度

**代码示例：**

```javascript
// 启用裁剪测试
gl.enable(gl.SCISSOR_TEST);

// 设置裁剪区域为画布中央的正方形
const size = Math.min(canvas.width, canvas.height) / 2;
const x = (canvas.width - size) / 2;
const y = (canvas.height - size) / 2;
gl.scissor(x, y, size, size);

// 渲染（只有裁剪区域内的像素会显示）
gl.clear(gl.COLOR_BUFFER_BIT);

// 禁用裁剪测试
gl.disable(gl.SCISSOR_TEST);
```

═══════════════════════════════════════════════════════════════════════════════

## 清除操作

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 清除操作函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.clearColor(red, green, blue, alpha)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.clearColor(red, green, blue, alpha);
```

**作用：** 设置清除颜色缓冲区时使用的颜色值

**参数详解：**

-   `red` (GLfloat) - 红色分量，范围[0.0, 1.0]
-   `green` (GLfloat) - 绿色分量，范围[0.0, 1.0]
-   `blue` (GLfloat) - 蓝色分量，范围[0.0, 1.0]
-   `alpha` (GLfloat) - Alpha 分量，范围[0.0, 1.0]

**代码示例：**

```javascript
// 设置黑色背景
gl.clearColor(0.0, 0.0, 0.0, 1.0);

// 设置白色背景
gl.clearColor(1.0, 1.0, 1.0, 1.0);

// 设置半透明蓝色背景
gl.clearColor(0.0, 0.5, 1.0, 0.8);

// 设置随机颜色背景
gl.clearColor(Math.random(), Math.random(), Math.random(), 1.0);

// 获取当前清除颜色
const clearColor = gl.getParameter(gl.COLOR_CLEAR_VALUE);
console.log('清除颜色:', clearColor); // [r, g, b, a]
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.clearDepth(depth)`

**函数签名：**

```javascript
gl.clearDepth(depth);
```

**作用：** 设置清除深度缓冲区时使用的深度值

**参数详解：**

-   `depth` (GLfloat) - 深度值，范围[0.0, 1.0]，默认为 1.0

**代码示例：**

```javascript
// 设置默认深度清除值（最远）
gl.clearDepth(1.0);

// 设置中等深度清除值
gl.clearDepth(0.5);

// 获取当前深度清除值
const clearDepth = gl.getParameter(gl.DEPTH_CLEAR_VALUE);
console.log('深度清除值:', clearDepth);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.clearStencil(stencil)`

**函数签名：**

```javascript
gl.clearStencil(stencil);
```

**作用：** 设置清除模板缓冲区时使用的模板值

**参数详解：**

-   `stencil` (GLint) - 模板值，通常为 0

**代码示例：**

```javascript
// 设置模板清除值
gl.clearStencil(0);

// 获取当前模板清除值
const clearStencil = gl.getParameter(gl.STENCIL_CLEAR_VALUE);
console.log('模板清除值:', clearStencil);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.clear(mask)`

**函数签名：**

```javascript
gl.clear(mask);
```

**作用：** 清除指定的缓冲区

**参数详解：**

-   `mask` (GLbitfield) - 缓冲区掩码，可以是以下值的组合：
    -   `gl.COLOR_BUFFER_BIT` (0x00004000) - 颜色缓冲区
    -   `gl.DEPTH_BUFFER_BIT` (0x00000100) - 深度缓冲区
    -   `gl.STENCIL_BUFFER_BIT` (0x00000400) - 模板缓冲区

**代码示例：**

```javascript
// 清除颜色缓冲区
gl.clear(gl.COLOR_BUFFER_BIT);

// 清除深度缓冲区
gl.clear(gl.DEPTH_BUFFER_BIT);

// 清除颜色和深度缓冲区
gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

// 清除所有缓冲区
gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT | gl.STENCIL_BUFFER_BIT);

// 完整的帧开始清除
function clearFrame() {
    gl.clearColor(0.2, 0.2, 0.2, 1.0);
    gl.clearDepth(1.0);
    gl.clearStencil(0);
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT | gl.STENCIL_BUFFER_BIT);
}
```

═══════════════════════════════════════════════════════════════════════════════

## 渲染状态开关

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 渲染状态开关函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.enable(cap)` / `gl.disable(cap)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.enable(cap);
gl.disable(cap);
```

**作用：** 启用或禁用特定的 WebGL 渲染功能

**参数详解：**

-   `cap` (GLenum) - 要启用/禁用的功能，可选值：
    -   `gl.DEPTH_TEST` (0x0B71) - 深度测试
    -   `gl.STENCIL_TEST` (0x0B90) - 模板测试
    -   `gl.SCISSOR_TEST` (0x0C11) - 裁剪测试
    -   `gl.BLEND` (0x0BE2) - 颜色混合
    -   `gl.CULL_FACE` (0x0B44) - 面剔除
    -   `gl.POLYGON_OFFSET_FILL` (0x8037) - 多边形偏移
    -   `gl.SAMPLE_ALPHA_TO_COVERAGE` (0x809E) - 采样 alpha 覆盖
    -   `gl.SAMPLE_COVERAGE` (0x80A0) - 采样覆盖
    -   `gl.DITHER` (0x0BD0) - 抖动（默认启用）

**代码示例：**

```javascript
// 启用深度测试
gl.enable(gl.DEPTH_TEST);

// 启用混合
gl.enable(gl.BLEND);

// 启用面剔除
gl.enable(gl.CULL_FACE);

// 启用裁剪测试
gl.enable(gl.SCISSOR_TEST);

// 禁用深度测试
gl.disable(gl.DEPTH_TEST);

// 检查功能是否启用
if (gl.isEnabled(gl.DEPTH_TEST)) {
    console.log('深度测试已启用');
}

// 状态管理类
class RenderState {
    constructor(gl) {
        this.gl = gl;
        this.states = new Map();
    }

    enable(cap) {
        if (!this.states.get(cap)) {
            this.gl.enable(cap);
            this.states.set(cap, true);
        }
    }

    disable(cap) {
        if (this.states.get(cap)) {
            this.gl.disable(cap);
            this.states.set(cap, false);
        }
    }
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.isEnabled(cap)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.isEnabled(cap);
```

**作用：** 检查指定的 WebGL 功能是否已启用

**参数详解：**

-   `cap` (GLenum) - 要检查的功能（同 enable/disable 的参数）

**返回值：** boolean - true 表示功能已启用，false 表示功能已禁用

**代码示例：**

```javascript
// 检查各种状态
console.log('深度测试:', gl.isEnabled(gl.DEPTH_TEST));
console.log('混合:', gl.isEnabled(gl.BLEND));
console.log('面剔除:', gl.isEnabled(gl.CULL_FACE));
console.log('裁剪测试:', gl.isEnabled(gl.SCISSOR_TEST));
console.log('模板测试:', gl.isEnabled(gl.STENCIL_TEST));

// 条件性启用功能
if (!gl.isEnabled(gl.DEPTH_TEST)) {
    gl.enable(gl.DEPTH_TEST);
    console.log('已启用深度测试');
}

// 状态检查函数
function checkRenderState() {
    const states = [
        { name: '深度测试', cap: gl.DEPTH_TEST },
        { name: '混合', cap: gl.BLEND },
        { name: '面剔除', cap: gl.CULL_FACE },
        { name: '裁剪测试', cap: gl.SCISSOR_TEST },
    ];

    states.forEach((state) => {
        console.log(`${state.name}: ${gl.isEnabled(state.cap) ? '启用' : '禁用'}`);
    });
}
```

**注意事项：**

-   此函数不会产生 WebGL 错误
-   可以用于调试和状态管理
-   返回值反映当前的渲染状态

═══════════════════════════════════════════════════════════════════════════════

## 深度测试

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 深度测试函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.depthFunc(func)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.depthFunc(func);
```

**作用：** 设置深度测试比较函数

**参数详解：**

-   `func` (GLenum) - 深度测试函数，可选值：
    -   `gl.NEVER` (0x0200) - 从不通过测试
    -   `gl.LESS` (0x0201) - 新深度值小于缓冲区值时通过（默认）
    -   `gl.EQUAL` (0x0202) - 新深度值等于缓冲区值时通过
    -   `gl.LEQUAL` (0x0203) - 新深度值小于等于缓冲区值时通过
    -   `gl.GREATER` (0x0204) - 新深度值大于缓冲区值时通过
    -   `gl.NOTEQUAL` (0x0205) - 新深度值不等于缓冲区值时通过
    -   `gl.GEQUAL` (0x0206) - 新深度值大于等于缓冲区值时通过
    -   `gl.ALWAYS` (0x0207) - 总是通过测试

**代码示例：**

```javascript
// 启用深度测试
gl.enable(gl.DEPTH_TEST);

// 设置标准深度测试（近的物体遮挡远的物体）
gl.depthFunc(gl.LESS);

// 设置反向深度测试（远的物体遮挡近的物体）
gl.depthFunc(gl.GREATER);

// 只渲染相同深度的像素
gl.depthFunc(gl.EQUAL);

// 总是通过深度测试（相当于禁用深度测试）
gl.depthFunc(gl.ALWAYS);

// 获取当前深度测试函数
const depthFunc = gl.getParameter(gl.DEPTH_FUNC);
console.log('深度测试函数:', depthFunc);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.depthMask(flag)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.depthMask(flag);
```

**作用：** 设置是否允许写入深度缓冲区

**参数详解：**

-   `flag` (GLboolean) - true 允许写入深度缓冲区，false 禁止写入

**返回值：** 无

**代码示例：**

```javascript
// 允许写入深度缓冲区（默认）
gl.depthMask(true);

// 禁止写入深度缓冲区（只读深度测试）
gl.depthMask(false);

// 透明物体渲染示例
function renderTransparentObjects() {
    // 禁用深度写入，但保持深度测试
    gl.depthMask(false);
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

    // 渲染透明物体
    renderTransparent();

    // 恢复深度写入
    gl.depthMask(true);
}

// 获取当前深度写入状态
const depthWriteEnabled = gl.getParameter(gl.DEPTH_WRITEMASK);
console.log('深度写入:', depthWriteEnabled ? '启用' : '禁用');
```

**注意事项：**

-   深度写入禁用时仍可进行深度测试
-   常用于透明物体渲染
-   不影响深度缓冲区的清除操作

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.depthRange(near, far)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.depthRange(near, far);
```

**作用：** 设置深度值的映射范围，将标准化设备坐标的深度值映射到窗口坐标

**参数详解：**

-   `near` (GLfloat) - 近平面深度值，范围[0.0, 1.0]，默认 0.0
-   `far` (GLfloat) - 远平面深度值，范围[0.0, 1.0]，默认 1.0

**返回值：** 无

**代码示例：**

```javascript
// 默认深度范围（标准）
gl.depthRange(0.0, 1.0);

// 反向深度范围（提高精度）
gl.depthRange(1.0, 0.0);
gl.depthFunc(gl.GREATER); // 配合反向深度测试

// 压缩深度范围到中间部分
gl.depthRange(0.25, 0.75);

// 只使用前半部分深度
gl.depthRange(0.0, 0.5);

// 获取当前深度范围
const depthRange = gl.getParameter(gl.DEPTH_RANGE);
console.log('深度范围:', depthRange); // [near, far]

// 反向深度缓冲区设置（提高远距离精度）
function setupReverseDepth() {
    gl.depthRange(1.0, 0.0);
    gl.depthFunc(gl.GREATER);
    gl.clearDepth(0.0);
}
```

**注意事项：**

-   near 和 far 值都必须在 [0.0, 1.0] 范围内
-   near 可以大于 far（反向深度）
-   反向深度可以提高远距离物体的深度精度
-   不影响投影矩阵的近远平面设置

═══════════════════════════════════════════════════════════════════════════════

## 颜色混合

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 颜色混合函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.blendFunc(sfactor, dfactor)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.blendFunc(sfactor, dfactor);
```

**作用：** 设置颜色混合函数，控制新像素颜色与帧缓冲区现有颜色的混合方式

**参数详解：**

-   `sfactor` (GLenum) - 源混合因子，可选值：
    -   `gl.ZERO` (0) - 0
    -   `gl.ONE` (1) - 1
    -   `gl.SRC_COLOR` (0x0300) - 源颜色
    -   `gl.ONE_MINUS_SRC_COLOR` (0x0301) - 1-源颜色
    -   `gl.DST_COLOR` (0x0306) - 目标颜色
    -   `gl.ONE_MINUS_DST_COLOR` (0x0307) - 1-目标颜色
    -   `gl.SRC_ALPHA` (0x0302) - 源 alpha
    -   `gl.ONE_MINUS_SRC_ALPHA` (0x0303) - 1-源 alpha
    -   `gl.DST_ALPHA` (0x0304) - 目标 alpha
    -   `gl.ONE_MINUS_DST_ALPHA` (0x0305) - 1-目标 alpha
    -   `gl.CONSTANT_COLOR` (0x8001) - 常量颜色
    -   `gl.ONE_MINUS_CONSTANT_COLOR` (0x8002) - 1-常量颜色
    -   `gl.CONSTANT_ALPHA` (0x8003) - 常量 alpha
    -   `gl.ONE_MINUS_CONSTANT_ALPHA` (0x8004) - 1-常量 alpha
    -   `gl.SRC_ALPHA_SATURATE` (0x0308) - 源 alpha 饱和
-   `dfactor` (GLenum) - 目标混合因子（可选值同 sfactor，但不包括 SRC_ALPHA_SATURATE）

**代码示例：**

```javascript
// 启用混合
gl.enable(gl.BLEND);

// 标准alpha混合（透明度）
gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

// 加法混合（发光效果）
gl.blendFunc(gl.SRC_ALPHA, gl.ONE);

// 乘法混合（阴影效果）
gl.blendFunc(gl.DST_COLOR, gl.ZERO);

// 屏幕混合
gl.blendFunc(gl.ONE_MINUS_DST_COLOR, gl.ONE);

// 预乘alpha混合
gl.blendFunc(gl.ONE, gl.ONE_MINUS_SRC_ALPHA);

// 获取当前混合函数
const srcBlend = gl.getParameter(gl.BLEND_SRC_RGB);
const dstBlend = gl.getParameter(gl.BLEND_DST_RGB);
console.log('混合函数:', srcBlend, dstBlend);
```

### `gl.blendFuncSeparate(srcRGB, dstRGB, srcAlpha, dstAlpha)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.blendFuncSeparate(srcRGB, dstRGB, srcAlpha, dstAlpha);
```

**作用：** 分别设置 RGB 和 Alpha 通道的混合函数，允许对颜色和透明度使用不同的混合策略

**参数详解：**

-   `srcRGB` (GLenum) - RGB 通道的源混合因子
-   `dstRGB` (GLenum) - RGB 通道的目标混合因子
-   `srcAlpha` (GLenum) - Alpha 通道的源混合因子
-   `dstAlpha` (GLenum) - Alpha 通道的目标混合因子

**返回值：** 无

**代码示例：**

```javascript
// RGB和Alpha使用不同的混合方式
gl.blendFuncSeparate(
    gl.SRC_ALPHA,
    gl.ONE_MINUS_SRC_ALPHA, // RGB混合：标准透明度
    gl.ONE,
    gl.ONE_MINUS_SRC_ALPHA // Alpha混合：保持透明度
);

// 预乘alpha混合
gl.blendFuncSeparate(
    gl.ONE,
    gl.ONE_MINUS_SRC_ALPHA, // RGB
    gl.ONE,
    gl.ONE_MINUS_SRC_ALPHA // Alpha
);

// 加法混合RGB，保持Alpha
gl.blendFuncSeparate(
    gl.SRC_ALPHA,
    gl.ONE, // RGB：加法混合
    gl.ZERO,
    gl.ONE // Alpha：保持目标alpha
);

// 获取当前混合函数
const srcRGB = gl.getParameter(gl.BLEND_SRC_RGB);
const dstRGB = gl.getParameter(gl.BLEND_DST_RGB);
const srcAlpha = gl.getParameter(gl.BLEND_SRC_ALPHA);
const dstAlpha = gl.getParameter(gl.BLEND_DST_ALPHA);
console.log('RGB混合:', srcRGB, dstRGB);
console.log('Alpha混合:', srcAlpha, dstAlpha);
```

**注意事项：**

-   提供比 blendFunc 更精细的控制
-   常用于复杂的透明度效果
-   可以独立控制颜色和透明度的混合方式

### `gl.blendEquation(mode)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.blendEquation(mode);
```

**作用：** 设置混合方程，定义源和目标颜色如何组合

**参数详解：**

-   `mode` (GLenum) - 混合方程，可选值：
    -   `gl.FUNC_ADD` (0x8006) - 加法：source + destination（默认）
    -   `gl.FUNC_SUBTRACT` (0x800A) - 减法：source - destination
    -   `gl.FUNC_REVERSE_SUBTRACT` (0x800B) - 反向减法：destination - source

**返回值：** 无

**代码示例：**

```javascript
// 加法混合（默认）
gl.blendEquation(gl.FUNC_ADD);
gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

// 减法混合（阴影效果）
gl.blendEquation(gl.FUNC_SUBTRACT);
gl.blendFunc(gl.ONE, gl.ONE);

// 反向减法混合
gl.blendEquation(gl.FUNC_REVERSE_SUBTRACT);
gl.blendFunc(gl.ONE, gl.ONE);

// 获取当前混合方程
const blendEquation = gl.getParameter(gl.BLEND_EQUATION_RGB);
console.log('混合方程:', blendEquation);

// 创建发光效果
function setupGlowEffect() {
    gl.enable(gl.BLEND);
    gl.blendEquation(gl.FUNC_ADD);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE);
}

// 创建减法效果
function setupSubtractEffect() {
    gl.enable(gl.BLEND);
    gl.blendEquation(gl.FUNC_SUBTRACT);
    gl.blendFunc(gl.ONE, gl.ONE);
}
```

**注意事项：**

-   混合方程决定了混合因子计算后的组合方式
-   减法混合可能产生负值，会被截断到 0
-   常与特定的混合因子组合使用

### `gl.blendColor(red, green, blue, alpha)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.blendColor(red, green, blue, alpha);
```

**作用：** 设置混合常量颜色，用于 CONSTANT_COLOR 和 CONSTANT_ALPHA 混合因子

**参数详解：**

-   `red` (GLfloat) - 红色分量，范围[0.0, 1.0]
-   `green` (GLfloat) - 绿色分量，范围[0.0, 1.0]
-   `blue` (GLfloat) - 蓝色分量，范围[0.0, 1.0]
-   `alpha` (GLfloat) - Alpha 分量，范围[0.0, 1.0]

**返回值：** 无

**代码示例：**

```javascript
// 设置混合常量颜色为半透明红色
gl.blendColor(1.0, 0.0, 0.0, 0.5);

// 使用常量颜色进行混合
gl.blendFunc(gl.CONSTANT_COLOR, gl.ONE_MINUS_CONSTANT_COLOR);

// 使用常量alpha进行混合
gl.blendColor(0.0, 0.0, 0.0, 0.3); // 只关心alpha值
gl.blendFunc(gl.CONSTANT_ALPHA, gl.ONE_MINUS_CONSTANT_ALPHA);

// 动态调整混合强度
function setBlendStrength(strength) {
    gl.blendColor(1.0, 1.0, 1.0, strength);
    gl.blendFunc(gl.CONSTANT_ALPHA, gl.ONE_MINUS_CONSTANT_ALPHA);
}

// 获取当前混合颜色
const blendColor = gl.getParameter(gl.BLEND_COLOR);
console.log('混合颜色:', blendColor); // [r, g, b, a]

// 创建彩色滤镜效果
function setupColorFilter(r, g, b, intensity) {
    gl.enable(gl.BLEND);
    gl.blendColor(r, g, b, intensity);
    gl.blendFunc(gl.CONSTANT_COLOR, gl.ONE_MINUS_CONSTANT_COLOR);
}
```

**注意事项：**

-   只有在使用 CONSTANT_COLOR 或 CONSTANT_ALPHA 混合因子时才有效
-   常用于创建滤镜效果或动态调整混合强度
-   所有分量值都会被截断到 [0.0, 1.0] 范围

═══════════════════════════════════════════════════════════════════════════════

## 面剔除

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 面剔除函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.cullFace(mode)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.cullFace(mode);
```

**作用：** 设置要剔除的面（正面或背面）

**参数详解：**

-   `mode` (GLenum) - 剔除模式，可选值：
    -   `gl.FRONT` (0x0404) - 剔除正面
    -   `gl.BACK` (0x0405) - 剔除背面（默认）
    -   `gl.FRONT_AND_BACK` (0x0408) - 剔除正面和背面

**代码示例：**

```javascript
// 启用面剔除
gl.enable(gl.CULL_FACE);

// 剔除背面（默认，常用于实体对象）
gl.cullFace(gl.BACK);

// 剔除正面（用于内部视角）
gl.cullFace(gl.FRONT);

// 剔除所有面（相当于不渲染）
gl.cullFace(gl.FRONT_AND_BACK);

// 获取当前剔除模式
const cullMode = gl.getParameter(gl.CULL_FACE_MODE);
console.log('剔除模式:', cullMode);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.frontFace(mode)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.frontFace(mode);
```

**作用：** 设置正面的顶点绕序方向，决定哪个方向的三角形被认为是正面

**参数详解：**

-   `mode` (GLenum) - 正面方向，可选值：
    -   `gl.CW` (0x0900) - 顺时针为正面
    -   `gl.CCW` (0x0901) - 逆时针为正面（默认）

**返回值：** 无

**代码示例：**

```javascript
// 设置逆时针为正面（OpenGL标准）
gl.frontFace(gl.CCW);

// 设置顺时针为正面（DirectX标准）
gl.frontFace(gl.CW);

// 完整的面剔除设置
function setupFaceCulling() {
    gl.enable(gl.CULL_FACE);
    gl.cullFace(gl.BACK);
    gl.frontFace(gl.CCW);
}

// 获取当前正面方向
const frontFace = gl.getParameter(gl.FRONT_FACE);
console.log('正面方向:', frontFace === gl.CCW ? '逆时针' : '顺时针');

// 根据坐标系调整正面方向
function setupCoordinateSystem(isRightHanded) {
    if (isRightHanded) {
        gl.frontFace(gl.CCW); // 右手坐标系
    } else {
        gl.frontFace(gl.CW); // 左手坐标系
    }
}

// 镜像变换时调整正面方向
function handleMirrorTransform(hasMirror) {
    if (hasMirror) {
        // 镜像变换会翻转绕序
        gl.frontFace(gl.CW);
    } else {
        gl.frontFace(gl.CCW);
    }
}
```

**注意事项：**

-   影响面剔除的判断
-   与顶点数据的绕序方向相关
-   镜像变换会改变绕序方向
-   不同的建模软件可能使用不同的绕序约定

═══════════════════════════════════════════════════════════════════════════════

## 模板测试

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 模板测试函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.stencilFunc(func, ref, mask)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.stencilFunc(func, ref, mask);
```

**作用：** 设置模板测试函数

**参数详解：**

-   `func` (GLenum) - 模板测试函数，可选值：
    -   `gl.NEVER` (0x0200) - 从不通过
    -   `gl.LESS` (0x0201) - 小于通过
    -   `gl.LEQUAL` (0x0203) - 小于等于通过
    -   `gl.GREATER` (0x0204) - 大于通过
    -   `gl.GEQUAL` (0x0206) - 大于等于通过
    -   `gl.EQUAL` (0x0202) - 等于通过
    -   `gl.NOTEQUAL` (0x0205) - 不等于通过
    -   `gl.ALWAYS` (0x0207) - 总是通过（默认）
-   `ref` (GLint) - 参考值，范围[0, 2^n-1]，n 为模板缓冲区位数
-   `mask` (GLuint) - 测试掩码，默认为全 1

**代码示例：**

```javascript
// 启用模板测试
gl.enable(gl.STENCIL_TEST);

// 设置模板测试：当模板值等于1时通过
gl.stencilFunc(gl.EQUAL, 1, 0xff);

// 设置模板测试：当模板值不等于0时通过
gl.stencilFunc(gl.NOTEQUAL, 0, 0xff);

// 获取当前模板测试设置
const stencilFunc = gl.getParameter(gl.STENCIL_FUNC);
const stencilRef = gl.getParameter(gl.STENCIL_REF);
const stencilMask = gl.getParameter(gl.STENCIL_VALUE_MASK);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.stencilOp(fail, zfail, zpass)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.stencilOp(fail, zfail, zpass);
```

**作用：** 设置模板测试的操作，定义在不同测试结果下如何更新模板缓冲区

**参数详解：**

-   `fail` (GLenum) - 模板测试失败时的操作
-   `zfail` (GLenum) - 模板测试通过但深度测试失败时的操作
-   `zpass` (GLenum) - 模板测试和深度测试都通过时的操作

**操作类型可选值：**

-   `gl.KEEP` (0x1E00) - 保持当前值（默认）
-   `gl.ZERO` (0) - 设置为 0
-   `gl.REPLACE` (0x1E01) - 替换为参考值
-   `gl.INCR` (0x1E02) - 递增（有上限）
-   `gl.INCR_WRAP` (0x8507) - 递增（循环）
-   `gl.DECR` (0x1E03) - 递减（有下限）
-   `gl.DECR_WRAP` (0x8508) - 递减（循环）
-   `gl.INVERT` (0x150A) - 按位取反

**返回值：** 无

**代码示例：**

```javascript
// 标准模板操作：通过时替换为参考值
gl.stencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);

// 计数操作：通过时递增
gl.stencilOp(gl.KEEP, gl.KEEP, gl.INCR);

// 阴影体积算法
gl.stencilOp(gl.KEEP, gl.INCR_WRAP, gl.KEEP); // 正面
gl.stencilOp(gl.KEEP, gl.DECR_WRAP, gl.KEEP); // 背面

// 模板缓冲区清零
gl.stencilOp(gl.ZERO, gl.ZERO, gl.ZERO);

// 获取当前模板操作设置
const stencilFail = gl.getParameter(gl.STENCIL_FAIL);
const stencilZFail = gl.getParameter(gl.STENCIL_PASS_DEPTH_FAIL);
const stencilZPass = gl.getParameter(gl.STENCIL_PASS_DEPTH_PASS);

// 创建模板遮罩
function createStencilMask() {
    gl.enable(gl.STENCIL_TEST);
    gl.stencilFunc(gl.ALWAYS, 1, 0xff);
    gl.stencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);
    gl.colorMask(false, false, false, false); // 不写入颜色
    gl.depthMask(false); // 不写入深度

    // 渲染遮罩几何体
    renderMaskGeometry();

    // 恢复写入
    gl.colorMask(true, true, true, true);
    gl.depthMask(true);
}
```

**注意事项：**

-   操作顺序：模板测试 → 深度测试 → 像素着色
-   INCR/DECR 有边界限制，INCR_WRAP/DECR_WRAP 会循环
-   常用于阴影、反射、遮罩等效果

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.stencilMask(mask)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.stencilMask(mask);
```

**作用：** 设置模板缓冲区写入掩码，控制哪些位可以被写入

**参数详解：**

-   `mask` (GLuint) - 写入掩码，默认为全 1 (0xFFFFFFFF)
    -   位值为 1 的位置允许写入
    -   位值为 0 的位置禁止写入

**返回值：** 无

**代码示例：**

```javascript
// 允许写入所有位（默认）
gl.stencilMask(0xff);

// 禁止写入模板缓冲区
gl.stencilMask(0x00);

// 只允许写入低4位
gl.stencilMask(0x0f);

// 只允许写入高4位
gl.stencilMask(0xf0);

// 获取当前写入掩码
const stencilWriteMask = gl.getParameter(gl.STENCIL_WRITEMASK);
console.log('模板写入掩码:', stencilWriteMask.toString(16));

// 分层模板缓冲区使用
function setupLayeredStencil() {
    // 第一层使用低4位
    gl.stencilMask(0x0f);
    gl.stencilFunc(gl.ALWAYS, 1, 0x0f);
    gl.stencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);
    renderLayer1();

    // 第二层使用高4位
    gl.stencilMask(0xf0);
    gl.stencilFunc(gl.ALWAYS, 0x10, 0xf0);
    gl.stencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);
    renderLayer2();

    // 恢复全部写入
    gl.stencilMask(0xff);
}

// 保护特定位
function protectStencilBits(protectedBits) {
    const currentMask = gl.getParameter(gl.STENCIL_WRITEMASK);
    const newMask = currentMask & ~protectedBits;
    gl.stencilMask(newMask);
}
```

**注意事项：**

-   写入掩码只影响模板缓冲区的写入操作
-   不影响模板测试的读取操作
-   可以用于实现多层模板效果
-   与 stencilFunc 的测试掩码是独立的

═══════════════════════════════════════════════════════════════════════════════

## 其他渲染状态

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 其他渲染状态函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.colorMask(red, green, blue, alpha)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.colorMask(red, green, blue, alpha);
```

**作用：** 设置颜色缓冲区写入掩码

**参数详解：**

-   `red` (GLboolean) - 是否允许写入红色通道
-   `green` (GLboolean) - 是否允许写入绿色通道
-   `blue` (GLboolean) - 是否允许写入蓝色通道
-   `alpha` (GLboolean) - 是否允许写入 Alpha 通道

**代码示例：**

```javascript
// 允许写入所有颜色通道（默认）
gl.colorMask(true, true, true, true);

// 只写入红色通道
gl.colorMask(true, false, false, false);

// 禁止写入颜色缓冲区（只进行深度/模板测试）
gl.colorMask(false, false, false, false);

// 不写入Alpha通道
gl.colorMask(true, true, true, false);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.lineWidth(width)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.lineWidth(width);
```

**作用：** 设置线条宽度，影响 gl.LINES、gl.LINE_STRIP、gl.LINE_LOOP 的渲染

**参数详解：**

-   `width` (GLfloat) - 线条宽度，默认为 1.0，必须大于 0

**返回值：** 无

**代码示例：**

```javascript
// 设置线条宽度
gl.lineWidth(2.0);

// 获取线条宽度范围
const lineWidthRange = gl.getParameter(gl.ALIASED_LINE_WIDTH_RANGE);
console.log('线条宽度范围:', lineWidthRange); // [min, max]

// 获取当前线条宽度
const currentWidth = gl.getParameter(gl.LINE_WIDTH);
console.log('当前线条宽度:', currentWidth);

// 绘制不同宽度的线条
function drawLines() {
    // 细线
    gl.lineWidth(1.0);
    drawLineGeometry();

    // 粗线（如果支持）
    gl.lineWidth(3.0);
    drawLineGeometry();
}

// 检查线条宽度支持
function checkLineWidthSupport() {
    const range = gl.getParameter(gl.ALIASED_LINE_WIDTH_RANGE);
    console.log(`支持的线条宽度范围: ${range[0]} - ${range[1]}`);

    if (range[1] <= 1.0) {
        console.warn('此设备只支持1像素宽度的线条');
        return false;
    }
    return true;
}
```

**注意事项：**

-   大多数现代 GPU 只支持宽度为 1.0 的线条
-   要绘制粗线条，建议使用三角形或几何着色器模拟
-   线条宽度可能受到硬件限制
-   在移动设备上通常只支持 1.0 宽度

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.polygonOffset(factor, units)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.polygonOffset(factor, units);
```

**作用：** 设置多边形深度偏移，用于解决 Z-fighting（深度冲突）问题

**参数详解：**

-   `factor` (GLfloat) - 比例因子，与多边形的深度斜率相关
-   `units` (GLfloat) - 单位偏移，与深度缓冲区的最小可分辨单位相关

**返回值：** 无

**深度偏移计算公式：**

```
offset = factor × DZ + units × r
```

其中：

-   DZ 是多边形的深度斜率
-   r 是深度缓冲区的最小可分辨单位

**代码示例：**

```javascript
// 启用多边形偏移
gl.enable(gl.POLYGON_OFFSET_FILL);

// 设置偏移参数
gl.polygonOffset(1.0, 1.0);

// 渲染阴影或贴花时使用
function renderDecal() {
    gl.enable(gl.POLYGON_OFFSET_FILL);
    gl.polygonOffset(-1.0, -1.0); // 向前偏移
    renderDecalGeometry();
    gl.disable(gl.POLYGON_OFFSET_FILL);
}

// 渲染阴影贴图
function renderShadowMap() {
    gl.enable(gl.POLYGON_OFFSET_FILL);
    gl.polygonOffset(2.0, 4.0); // 向后偏移，避免阴影痤疮
    renderShadowCasters();
    gl.disable(gl.POLYGON_OFFSET_FILL);
}

// 获取偏移参数
const offsetFactor = gl.getParameter(gl.POLYGON_OFFSET_FACTOR);
const offsetUnits = gl.getParameter(gl.POLYGON_OFFSET_UNITS);
console.log('多边形偏移:', offsetFactor, offsetUnits);

// 解决共面几何体的Z-fighting
function renderCoplanarGeometry() {
    // 渲染底层几何体
    renderBaseGeometry();

    // 渲染上层几何体（稍微向前偏移）
    gl.enable(gl.POLYGON_OFFSET_FILL);
    gl.polygonOffset(-1.0, -1.0);
    renderOverlayGeometry();
    gl.disable(gl.POLYGON_OFFSET_FILL);
}
```

**注意事项：**

-   必须启用 `gl.POLYGON_OFFSET_FILL` 才能生效
-   正值向远离观察者方向偏移，负值向观察者方向偏移
-   factor 参数对倾斜表面影响更大
-   units 参数提供恒定的偏移量
-   常用于阴影贴图、贴花、轮廓线等效果

═══════════════════════════════════════════════════════════════════════════════

## 绘制命令

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 绘制命令函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.drawArrays(mode, first, count)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.drawArrays(mode, first, count);
```

**作用：** 使用当前绑定的顶点缓冲区数据绘制图元

**参数详解：**

-   `mode` (GLenum) - 图元类型，可选值：
    -   `gl.POINTS` (0x0000) - 点
    -   `gl.LINES` (0x0001) - 线段（每 2 个顶点一条线）
    -   `gl.LINE_LOOP` (0x0002) - 闭合线条
    -   `gl.LINE_STRIP` (0x0003) - 连续线条
    -   `gl.TRIANGLES` (0x0004) - 三角形（每 3 个顶点一个三角形）
    -   `gl.TRIANGLE_STRIP` (0x0005) - 三角形带
    -   `gl.TRIANGLE_FAN` (0x0006) - 三角形扇
-   `first` (GLint) - 起始顶点索引
-   `count` (GLsizei) - 要绘制的顶点数量

**返回值：** 无

**代码示例：**

```javascript
// 绘制三角形
const vertices = new Float32Array([
    -0.5,
    -0.5,
    0.0, // 顶点1
    0.5,
    -0.5,
    0.0, // 顶点2
    0.0,
    0.5,
    0.0, // 顶点3
]);

gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 0, 0);
gl.enableVertexAttribArray(0);

// 绘制一个三角形
gl.drawArrays(gl.TRIANGLES, 0, 3);

// 绘制多个三角形
gl.drawArrays(gl.TRIANGLES, 0, 6); // 绘制2个三角形

// 绘制线条
gl.drawArrays(gl.LINES, 0, 4); // 绘制2条线段

// 绘制连续线条
gl.drawArrays(gl.LINE_STRIP, 0, 5); // 绘制4条连接的线段

// 绘制点
gl.drawArrays(gl.POINTS, 0, 10); // 绘制10个点
```

**注意事项：**

-   必须先设置顶点属性和着色器程序
-   顶点数量必须与图元类型匹配
-   超出缓冲区范围会导致未定义行为

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.drawElements(mode, count, type, offset)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.drawElements(mode, count, type, offset);
```

**作用：** 使用索引缓冲区绘制图元，可以重用顶点数据，减少内存使用

**参数详解：**

-   `mode` (GLenum) - 图元类型（同 drawArrays）
-   `count` (GLsizei) - 要绘制的索引数量
-   `type` (GLenum) - 索引数据类型，可选值：
    -   `gl.UNSIGNED_BYTE` (0x1401) - 8 位无符号整数（0-255）
    -   `gl.UNSIGNED_SHORT` (0x1403) - 16 位无符号整数（0-65535）
    -   `gl.UNSIGNED_INT` (0x1405) - 32 位无符号整数（WebGL 2.0 或扩展）
-   `offset` (GLintptr) - 索引缓冲区中的字节偏移量

**返回值：** 无

**代码示例：**

```javascript
// 矩形的顶点数据（4个顶点）
const vertices = new Float32Array([
    -0.5,
    -0.5,
    0.0, // 左下
    0.5,
    -0.5,
    0.0, // 右下
    0.5,
    0.5,
    0.0, // 右上
    -0.5,
    0.5,
    0.0, // 左上
]);

// 索引数据（2个三角形组成矩形）
const indices = new Uint16Array([
    0,
    1,
    2, // 第一个三角形
    2,
    3,
    0, // 第二个三角形
]);

// 设置顶点缓冲区
gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 0, 0);
gl.enableVertexAttribArray(0);

// 设置索引缓冲区
gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);

// 绘制矩形
gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_SHORT, 0);

// 绘制部分索引
gl.drawElements(gl.TRIANGLES, 3, gl.UNSIGNED_SHORT, 0); // 只绘制第一个三角形

// 使用偏移量
gl.drawElements(gl.TRIANGLES, 3, gl.UNSIGNED_SHORT, 6); // 从第二个三角形开始

// 复杂模型示例
function drawMesh(mesh) {
    gl.bindBuffer(gl.ARRAY_BUFFER, mesh.vertexBuffer);
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, mesh.indexBuffer);

    // 设置顶点属性
    setupVertexAttributes();

    // 绘制所有三角形
    gl.drawElements(gl.TRIANGLES, mesh.indexCount, gl.UNSIGNED_SHORT, 0);
}
```

**注意事项：**

-   必须先绑定 ELEMENT_ARRAY_BUFFER
-   索引值不能超出顶点缓冲区范围
-   使用索引可以显著减少顶点数据重复
-   offset 参数以字节为单位，不是索引数量

**优势：**

-   减少顶点数据重复，节省内存
-   提高渲染性能
-   适合绘制复杂几何体

### 图元类型详解

#### `gl.POINTS` - 点

```javascript
// 每个顶点绘制一个点
gl.drawArrays(gl.POINTS, 0, vertexCount);
```

-   用途：粒子系统、星空效果
-   点的大小可在顶点着色器中设置`gl_PointSize`

#### `gl.LINES` - 线段

```javascript
// 每2个顶点绘制一条独立的线段
gl.drawArrays(gl.LINES, 0, vertexCount); // vertexCount必须是偶数
```

-   用途：网格线、坐标轴

#### `gl.LINE_STRIP` - 连续线条

```javascript
// 顶点按顺序连接成连续的线条
gl.drawArrays(gl.LINE_STRIP, 0, vertexCount);
```

-   用途：路径、轮廓线

#### `gl.LINE_LOOP` - 闭合线条

```javascript
// 类似LINE_STRIP，但最后一个顶点会连接到第一个顶点
gl.drawArrays(gl.LINE_LOOP, 0, vertexCount);
```

-   用途：多边形轮廓

#### `gl.TRIANGLES` - 三角形

```javascript
// 每3个顶点绘制一个独立的三角形
gl.drawArrays(gl.TRIANGLES, 0, vertexCount); // vertexCount必须是3的倍数
```

-   用途：最常用的图元，适合绘制实体对象

#### `gl.TRIANGLE_STRIP` - 三角形带

```javascript
// 顶点按特定顺序共享边形成三角形带
gl.drawArrays(gl.TRIANGLE_STRIP, 0, vertexCount);
```

-   用途：地形、曲面，减少顶点数量

#### `gl.TRIANGLE_FAN` - 三角形扇

```javascript
// 所有三角形共享第一个顶点
gl.drawArrays(gl.TRIANGLE_FAN, 0, vertexCount);
```

-   用途：圆形、扇形

═══════════════════════════════════════════════════════════════════════════════

## WebGL 2.0 实例化绘制

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ WebGL 2.0 实例化绘制函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.drawArraysInstanced(mode, first, count, instanceCount)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.drawArraysInstanced(mode, first, count, instanceCount);
```

**作用：** 实例化绘制顶点数组，一次绘制调用渲染多个相同几何体的实例

**参数详解：**

-   `mode` (GLenum) - 图元类型（同 drawArrays）
-   `first` (GLint) - 起始顶点索引
-   `count` (GLsizei) - 每个实例的顶点数量
-   `instanceCount` (GLsizei) - 要绘制的实例数量

**代码示例：**

```javascript
// 绘制100个三角形实例
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 100);

// 在顶点着色器中使用gl_InstanceID区分不同实例
const vertexShaderSource = `
    attribute vec3 a_position;
    uniform mat4 u_viewProjection;

    void main() {
        // 根据实例ID计算不同的位置
        vec3 offset = vec3(
            float(gl_InstanceID % 10) * 2.0,
            float(gl_InstanceID / 10) * 2.0,
            0.0
        );

        gl_Position = u_viewProjection * vec4(a_position + offset, 1.0);
    }
`;

// 使用实例化属性
gl.enableVertexAttribArray(1);
gl.vertexAttribPointer(1, 3, gl.FLOAT, false, 0, 0);
gl.vertexAttribDivisor(1, 1); // 每个实例使用一个属性值
```

### `gl.drawElementsInstanced(mode, count, type, offset, instanceCount)`

**函数签名：**

```javascript
gl.drawElementsInstanced(mode, count, type, offset, instanceCount);
```

**作用：** 实例化绘制索引数组

**参数详解：**

-   `mode` (GLenum) - 图元类型
-   `count` (GLsizei) - 每个实例的索引数量
-   `type` (GLenum) - 索引数据类型
-   `offset` (GLintptr) - 索引缓冲区偏移量
-   `instanceCount` (GLsizei) - 要绘制的实例数量

**代码示例：**

```javascript
// 实例化绘制立方体
const cubeIndices = new Uint16Array([
    // 前面
    0, 1, 2, 2, 3, 0,
    // 后面
    4, 5, 6, 6, 7, 4,
    // ... 其他面
]);

// 实例位置数据
const instancePositions = new Float32Array([
    0,
    0,
    0, // 实例1位置
    2,
    0,
    0, // 实例2位置
    4,
    0,
    0, // 实例3位置
    // ... 更多实例位置
]);

// 设置实例属性
gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
gl.bufferData(gl.ARRAY_BUFFER, instancePositions, gl.STATIC_DRAW);
gl.enableVertexAttribArray(2);
gl.vertexAttribPointer(2, 3, gl.FLOAT, false, 0, 0);
gl.vertexAttribDivisor(2, 1); // 每个实例一个位置

// 绘制1000个立方体实例
gl.drawElementsInstanced(gl.TRIANGLES, 36, gl.UNSIGNED_SHORT, 0, 1000);
```

### `gl.drawRangeElements(mode, start, end, count, type, offset)`

**函数签名：**

```javascript
gl.drawRangeElements(mode, start, end, count, type, offset);
```

**作用：** 绘制指定范围内的索引元素，提供额外的优化提示

**参数详解：**

-   `mode` (GLenum) - 图元类型
-   `start` (GLuint) - 最小索引值
-   `end` (GLuint) - 最大索引值
-   `count` (GLsizei) - 索引数量
-   `type` (GLenum) - 索引数据类型
-   `offset` (GLintptr) - 索引缓冲区偏移量

**代码示例：**

```javascript
// 绘制索引范围在0-99之间的三角形
gl.drawRangeElements(gl.TRIANGLES, 0, 99, indexCount, gl.UNSIGNED_SHORT, 0);

// 绘制大模型的一部分
gl.drawRangeElements(gl.TRIANGLES, 1000, 1999, 3000, gl.UNSIGNED_SHORT, 6000);
```

**优势：**

-   提供索引范围信息有助于驱动程序优化
-   可以减少顶点缓存未命中

═══════════════════════════════════════════════════════════════════════════════

## 绘制命令最佳实践

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 绘制命令最佳实践 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### 1. 选择合适的绘制方式

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
// 简单几何体 - 使用drawArrays
function drawTriangle() {
    gl.drawArrays(gl.TRIANGLES, 0, 3);
}

// 复杂几何体 - 使用drawElements
function drawCube() {
    gl.drawElements(gl.TRIANGLES, 36, gl.UNSIGNED_SHORT, 0);
}

// 大量相同对象 - 使用实例化绘制
function drawForest() {
    gl.drawArraysInstanced(gl.TRIANGLES, 0, treeVertexCount, 1000);
}
```

### 2. 批量绘制优化

```javascript
// ❌ 不好的做法 - 多次绘制调用
for (const object of objects) {
    setupObject(object);
    gl.drawElements(gl.TRIANGLES, object.indexCount, gl.UNSIGNED_SHORT, 0);
}

// ✅ 好的做法 - 批量绘制
function batchDraw(objects) {
    // 按材质分组
    const batches = groupByMaterial(objects);

    for (const batch of batches) {
        setupMaterial(batch.material);

        // 合并几何数据或使用实例化
        if (batch.canInstance) {
            gl.drawArraysInstanced(gl.TRIANGLES, 0, batch.vertexCount, batch.objects.length);
        } else {
            // 合并到单个缓冲区
            gl.drawElements(gl.TRIANGLES, batch.totalIndexCount, gl.UNSIGNED_SHORT, 0);
        }
    }
}
```

### 3. 错误检查

```javascript
function safeDrawElements(mode, count, type, offset) {
    // 检查参数有效性
    if (count <= 0) {
        console.warn('绘制数量必须大于0');
        return;
    }

    // 检查是否有活动的着色器程序
    const currentProgram = gl.getParameter(gl.CURRENT_PROGRAM);
    if (!currentProgram) {
        console.error('没有活动的着色器程序');
        return;
    }

    // 执行绘制
    gl.drawElements(mode, count, type, offset);

    // 检查WebGL错误
    const error = gl.getError();
    if (error !== gl.NO_ERROR) {
        console.error('绘制错误:', error);
    }
}
```

═══════════════════════════════════════════════════════════════════════════════

## WebGL 2.0 新增函数

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ WebGL 2.0 新增函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### Transform Feedback

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

Transform Feedback 允许将顶点着色器的输出捕获到缓冲区中，实现 GPU 端的数据处理。

#### `gl.createTransformFeedback()`

**函数签名：**

```javascript
gl.createTransformFeedback();
```

**作用：** 创建 Transform Feedback 对象

**返回值：** WebGLTransformFeedback 对象

**代码示例：**

```javascript
const transformFeedback = gl.createTransformFeedback();
```

#### `gl.bindTransformFeedback(target, transformFeedback)`

**函数签名：**

```javascript
gl.bindTransformFeedback(target, transformFeedback);
```

**作用：** 绑定 Transform Feedback 对象

**参数详解：**

-   `target` (GLenum) - 必须是`gl.TRANSFORM_FEEDBACK`
-   `transformFeedback` (WebGLTransformFeedback | null) - 要绑定的对象

**代码示例：**

```javascript
gl.bindTransformFeedback(gl.TRANSFORM_FEEDBACK, transformFeedback);
```

#### `gl.beginTransformFeedback(primitiveMode)`

**函数签名：**

```javascript
gl.beginTransformFeedback(primitiveMode);
```

**作用：** 开始 Transform Feedback 捕获

**参数详解：**

-   `primitiveMode` (GLenum) - 图元模式：
    -   `gl.POINTS`
    -   `gl.LINES`
    -   `gl.TRIANGLES`

**代码示例：**

```javascript
// 设置Transform Feedback
gl.bindTransformFeedback(gl.TRANSFORM_FEEDBACK, transformFeedback);
gl.bindBufferBase(gl.TRANSFORM_FEEDBACK_BUFFER, 0, outputBuffer);

// 开始捕获
gl.beginTransformFeedback(gl.POINTS);

// 禁用光栅化（只进行顶点处理）
gl.enable(gl.RASTERIZER_DISCARD);

// 绘制（顶点着色器输出将被捕获）
gl.drawArrays(gl.POINTS, 0, inputVertexCount);

// 结束捕获
gl.disable(gl.RASTERIZER_DISCARD);
gl.endTransformFeedback();
```

#### `gl.endTransformFeedback()`

**函数签名：**

```javascript
gl.endTransformFeedback();
```

**作用：** 结束 Transform Feedback 捕获

#### `gl.transformFeedbackVaryings(program, varyings, bufferMode)`

**函数签名：**

```javascript
gl.transformFeedbackVaryings(program, varyings, bufferMode);
```

**作用：** 指定要捕获的 varying 变量

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序
-   `varyings` (Array<string>) - 要捕获的 varying 变量名数组
-   `bufferMode` (GLenum) - 缓冲模式：
    -   `gl.INTERLEAVED_ATTRIBS` - 交错存储到单个缓冲区
    -   `gl.SEPARATE_ATTRIBS` - 分别存储到不同缓冲区

**代码示例：**

```javascript
// 顶点着色器
const vertexShaderSource = `#version 300 es
in vec3 a_position;
in vec3 a_velocity;

out vec3 v_newPosition;
out vec3 v_newVelocity;

uniform float u_deltaTime;

void main() {
    v_newPosition = a_position + a_velocity * u_deltaTime;
    v_newVelocity = a_velocity;

    gl_Position = vec4(v_newPosition, 1.0);
}
`;

// 设置Transform Feedback变量
gl.transformFeedbackVaryings(program, ['v_newPosition', 'v_newVelocity'], gl.INTERLEAVED_ATTRIBS);

// 重新链接程序
gl.linkProgram(program);
```

---

### Uniform Buffer Objects (UBO)

UBO 允许将多个 uniform 变量组织到缓冲区中，提高性能并支持更大的 uniform 数据。

#### `gl.getUniformBlockIndex(program, uniformBlockName)`

**函数签名：**

```javascript
gl.getUniformBlockIndex(program, uniformBlockName);
```

**作用：** 获取 uniform 块的索引

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序
-   `uniformBlockName` (string) - uniform 块名称

**返回值：** GLuint - uniform 块索引，如果不存在返回`gl.INVALID_INDEX`

#### `gl.uniformBlockBinding(program, uniformBlockIndex, uniformBlockBinding)`

**函数签名：**

```javascript
gl.uniformBlockBinding(program, uniformBlockIndex, uniformBlockBinding);
```

**作用：** 将 uniform 块绑定到指定的绑定点

**参数详解：**

-   `program` (WebGLProgram) - 着色器程序
-   `uniformBlockIndex` (GLuint) - uniform 块索引
-   `uniformBlockBinding` (GLuint) - 绑定点索引

**代码示例：**

```javascript
// 着色器中的uniform块
const fragmentShaderSource = `#version 300 es
precision mediump float;

layout(std140) uniform Matrices {
    mat4 u_view;
    mat4 u_projection;
    mat4 u_model;
};

layout(std140) uniform Material {
    vec3 u_diffuseColor;
    float u_shininess;
    vec3 u_specularColor;
    float u_opacity;
};

out vec4 fragColor;

void main() {
    // 使用uniform块中的变量
    fragColor = vec4(u_diffuseColor, u_opacity);
}
`;

// 获取uniform块索引
const matricesBlockIndex = gl.getUniformBlockIndex(program, 'Matrices');
const materialBlockIndex = gl.getUniformBlockIndex(program, 'Material');

// 绑定到绑定点
gl.uniformBlockBinding(program, matricesBlockIndex, 0);
gl.uniformBlockBinding(program, materialBlockIndex, 1);

// 创建和绑定UBO
const matricesUBO = gl.createBuffer();
gl.bindBuffer(gl.UNIFORM_BUFFER, matricesUBO);
gl.bufferData(gl.UNIFORM_BUFFER, matricesData, gl.DYNAMIC_DRAW);
gl.bindBufferBase(gl.UNIFORM_BUFFER, 0, matricesUBO);

const materialUBO = gl.createBuffer();
gl.bindBuffer(gl.UNIFORM_BUFFER, materialUBO);
gl.bufferData(gl.UNIFORM_BUFFER, materialData, gl.STATIC_DRAW);
gl.bindBufferBase(gl.UNIFORM_BUFFER, 1, materialUBO);
```

---

### 查询对象 (Query Objects)

查询对象用于获取 GPU 执行的统计信息，如渲染的像素数量、执行时间等。

#### `gl.createQuery()`

**函数签名：**

```javascript
gl.createQuery();
```

**作用：** 创建查询对象

**返回值：** WebGLQuery 对象

#### `gl.beginQuery(target, query)`

**函数签名：**

```javascript
gl.beginQuery(target, query);
```

**作用：** 开始查询

**参数详解：**

-   `target` (GLenum) - 查询类型：
    -   `gl.ANY_SAMPLES_PASSED` - 是否有像素通过测试
    -   `gl.ANY_SAMPLES_PASSED_CONSERVATIVE` - 保守的像素通过测试
    -   `gl.TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN` - Transform Feedback 写入的图元数量
-   `query` (WebGLQuery) - 查询对象

#### `gl.endQuery(target)`

**函数签名：**

```javascript
gl.endQuery(target);
```

**作用：** 结束查询

**代码示例：**

```javascript
// 创建遮挡查询
const occlusionQuery = gl.createQuery();

// 开始查询
gl.beginQuery(gl.ANY_SAMPLES_PASSED, occlusionQuery);

// 渲染可能被遮挡的对象
gl.drawElements(gl.TRIANGLES, indexCount, gl.UNSIGNED_SHORT, 0);

// 结束查询
gl.endQuery(gl.ANY_SAMPLES_PASSED);

// 稍后检查结果
function checkQueryResult() {
    const available = gl.getQueryParameter(occlusionQuery, gl.QUERY_RESULT_AVAILABLE);
    if (available) {
        const result = gl.getQueryParameter(occlusionQuery, gl.QUERY_RESULT);
        if (result > 0) {
            console.log('对象可见');
        } else {
            console.log('对象被遮挡');
        }
    } else {
        // 查询还未完成，稍后再检查
        requestAnimationFrame(checkQueryResult);
    }
}
checkQueryResult();
```

---

### 采样器对象 (Sampler Objects)

采样器对象允许独立于纹理设置采样参数。

### `gl.createSampler()`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

**函数签名：**

```javascript
gl.createSampler();
```

**作用：** 创建采样器对象，用于控制纹理采样参数

**参数：** 无

**返回值：** WebGLSampler 对象，如果失败返回 null

**代码示例：**

```javascript
const sampler = gl.createSampler();
if (!sampler) {
    console.error('无法创建采样器对象');
}
```

**注意事项：**

-   仅在 WebGL 2.0 中可用
-   采样器可以独立于纹理设置采样参数
-   使用完毕后应调用 deleteSampler() 释放资源

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.bindSampler(unit, sampler)`

**函数签名：**

```javascript
gl.bindSampler(unit, sampler);
```

**作用：** 将采样器绑定到指定的纹理单元

**参数详解：**

-   `unit` (GLuint) - 纹理单元索引
-   `sampler` (WebGLSampler | null) - 采样器对象，null 表示解绑

**返回值：** 无

**代码示例：**

```javascript
// 绑定采样器到纹理单元0
gl.bindSampler(0, sampler);

// 解绑采样器
gl.bindSampler(0, null);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.samplerParameteri(sampler, pname, param)`

**函数签名：**

```javascript
gl.samplerParameteri(sampler, pname, param);
```

**作用：** 设置采样器的整数参数

**参数详解：**

-   `sampler` (WebGLSampler) - 采样器对象
-   `pname` (GLenum) - 参数名称（同 texParameteri）
-   `param` (GLint) - 参数值

**返回值：** 无

**代码示例：**

```javascript
// 创建不同的采样器
const nearestSampler = gl.createSampler();
gl.samplerParameteri(nearestSampler, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
gl.samplerParameteri(nearestSampler, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
gl.samplerParameteri(nearestSampler, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
gl.samplerParameteri(nearestSampler, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

const linearSampler = gl.createSampler();
gl.samplerParameteri(linearSampler, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
gl.samplerParameteri(linearSampler, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
gl.samplerParameteri(linearSampler, gl.TEXTURE_WRAP_S, gl.REPEAT);
gl.samplerParameteri(linearSampler, gl.TEXTURE_WRAP_T, gl.REPEAT);

// 使用不同的采样器
gl.activeTexture(gl.TEXTURE0);
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.bindSampler(0, nearestSampler); // 使用最近邻采样

gl.activeTexture(gl.TEXTURE1);
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.bindSampler(1, linearSampler); // 使用线性采样
```

**注意事项：**

-   采样器参数会覆盖纹理对象的相应参数
-   可以为同一个纹理使用不同的采样器

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.fenceSync(condition, flags)`

**函数签名：**

```javascript
gl.fenceSync(condition, flags);
```

**作用：** 创建同步对象，用于 CPU 和 GPU 之间的同步

**参数详解：**

-   `condition` (GLenum) - 同步条件，必须是 `gl.SYNC_GPU_COMMANDS_COMPLETE`
-   `flags` (GLbitfield) - 标志位，必须是 0

**返回值：** WebGLSync 对象，如果失败返回 null

**代码示例：**

```javascript
// 创建同步点
const sync = gl.fenceSync(gl.SYNC_GPU_COMMANDS_COMPLETE, 0);
if (!sync) {
    console.error('无法创建同步对象');
}
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.clientWaitSync(sync, flags, timeout)`

**函数签名：**

```javascript
gl.clientWaitSync(sync, flags, timeout);
```

**作用：** 等待同步对象完成

**参数详解：**

-   `sync` (WebGLSync) - 同步对象
-   `flags` (GLbitfield) - 等待标志
    -   `gl.SYNC_FLUSH_COMMANDS_BIT` - 刷新命令缓冲区
-   `timeout` (GLuint64) - 超时时间（纳秒），0 表示立即返回

**返回值：** GLenum - 等待结果

-   `gl.ALREADY_SIGNALED` - 同步对象已经完成
-   `gl.TIMEOUT_EXPIRED` - 超时
-   `gl.CONDITION_SATISFIED` - 在超时前完成
-   `gl.WAIT_FAILED` - 等待失败

**代码示例：**

```javascript
// 创建同步点
const sync = gl.fenceSync(gl.SYNC_GPU_COMMANDS_COMPLETE, 0);

// 继续执行其他工作...

// 等待GPU完成
const result = gl.clientWaitSync(sync, gl.SYNC_FLUSH_COMMANDS_BIT, 0);
if (result === gl.ALREADY_SIGNALED || result === gl.CONDITION_SATISFIED) {
    console.log('GPU操作已完成');
} else if (result === gl.TIMEOUT_EXPIRED) {
    console.log('GPU操作仍在进行中');
} else {
    console.error('等待失败');
}

// 清理
gl.deleteSync(sync);
```

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### `gl.vertexAttribDivisor(index, divisor)`

**函数签名：**

```javascript
gl.vertexAttribDivisor(index, divisor);
```

**作用：** 设置顶点属性的实例化除数，用于实例化渲染

**参数详解：**

-   `index` (GLuint) - 顶点属性索引
-   `divisor` (GLuint) - 除数值：
    -   0 - 每个顶点使用一次属性（默认）
    -   1 - 每个实例使用一次属性
    -   N - 每 N 个实例使用一次属性

**返回值：** 无

**代码示例：**

```javascript
// 设置实例化属性
gl.enableVertexAttribArray(1);
gl.vertexAttribPointer(1, 3, gl.FLOAT, false, 0, 0);
gl.vertexAttribDivisor(1, 1); // 每个实例一个位置

gl.enableVertexAttribArray(2);
gl.vertexAttribPointer(2, 4, gl.FLOAT, false, 0, 0);
gl.vertexAttribDivisor(2, 1); // 每个实例一个颜色

// 实例化绘制
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 1000);

// 重置除数
gl.vertexAttribDivisor(1, 0);
gl.vertexAttribDivisor(2, 0);
```

**注意事项：**

-   仅在 WebGL 2.0 中可用
-   除数为 0 时恢复正常的顶点属性行为
-   实例化渲染可以大幅提高绘制大量相似对象的性能

═══════════════════════════════════════════════════════════════════════════════

## WebGL 扩展函数

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ WebGL 扩展函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

WebGL 扩展提供了额外的功能，这些功能可能不是所有设备都支持，需要先检查支持性再使用。

### 扩展检查和获取

#### `gl.getSupportedExtensions()`

**函数签名：**

```javascript
gl.getSupportedExtensions();
```

**作用：** 获取当前 WebGL 上下文支持的所有扩展列表

**返回值：** Array<string> - 支持的扩展名称数组

**代码示例：**

```javascript
const supportedExtensions = gl.getSupportedExtensions();
console.log('支持的扩展:', supportedExtensions);

// 检查特定扩展是否支持
const hasVAO = supportedExtensions.includes('OES_vertex_array_object');
const hasInstancedArrays = supportedExtensions.includes('ANGLE_instanced_arrays');
```

#### `gl.getExtension(name)`

**函数签名：**

```javascript
gl.getExtension(name);
```

**作用：** 获取指定的扩展对象

**参数详解：**

-   `name` (string) - 扩展名称

**返回值：** 扩展对象或 null（如果不支持）

**代码示例：**

```javascript
// 获取扩展
const ext = gl.getExtension('OES_vertex_array_object');
if (ext) {
    console.log('VAO扩展可用');
} else {
    console.log('VAO扩展不支持');
}
```

---

### 常用 WebGL 扩展

#### OES_vertex_array_object

在 WebGL 1.0 中提供顶点数组对象功能（WebGL 2.0 中已内置）。

**扩展函数：**

-   `ext.createVertexArrayOES()`
-   `ext.bindVertexArrayOES(vao)`
-   `ext.deleteVertexArrayOES(vao)`
-   `ext.isVertexArrayOES(vao)`

**代码示例：**

```javascript
const vaoExt = gl.getExtension('OES_vertex_array_object');
if (vaoExt) {
    // 创建VAO
    const vao = vaoExt.createVertexArrayOES();

    // 绑定VAO并设置顶点属性
    vaoExt.bindVertexArrayOES(vao);
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.enableVertexAttribArray(0);
    gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 0, 0);

    // 解绑VAO
    vaoExt.bindVertexArrayOES(null);

    // 使用VAO
    vaoExt.bindVertexArrayOES(vao);
    gl.drawArrays(gl.TRIANGLES, 0, 3);

    // 清理
    vaoExt.deleteVertexArrayOES(vao);
}
```

#### ANGLE_instanced_arrays

在 WebGL 1.0 中提供实例化渲染功能。

**扩展函数：**

-   `ext.drawArraysInstancedANGLE(mode, first, count, primcount)`
-   `ext.drawElementsInstancedANGLE(mode, count, type, offset, primcount)`
-   `ext.vertexAttribDivisorANGLE(index, divisor)`

**代码示例：**

```javascript
const instancedExt = gl.getExtension('ANGLE_instanced_arrays');
if (instancedExt) {
    // 设置实例化属性
    gl.enableVertexAttribArray(1);
    gl.vertexAttribPointer(1, 3, gl.FLOAT, false, 0, 0);
    instancedExt.vertexAttribDivisorANGLE(1, 1);

    // 实例化绘制
    instancedExt.drawArraysInstancedANGLE(gl.TRIANGLES, 0, 3, 100);
}
```

#### OES_texture_float

允许使用浮点纹理。

**代码示例：**

```javascript
const floatTextureExt = gl.getExtension('OES_texture_float');
if (floatTextureExt) {
    // 创建浮点纹理
    const texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, texture);

    const floatData = new Float32Array(width * height * 4);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.FLOAT, floatData);

    // 设置纹理参数（浮点纹理通常不支持线性过滤）
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
}
```

#### WEBGL_depth_texture

允许将深度缓冲区作为纹理使用。

**代码示例：**

```javascript
const depthTextureExt = gl.getExtension('WEBGL_depth_texture');
if (depthTextureExt) {
    // 创建深度纹理
    const depthTexture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, depthTexture);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.DEPTH_COMPONENT, width, height, 0, gl.DEPTH_COMPONENT, gl.UNSIGNED_SHORT, null);

    // 创建帧缓冲区并附加深度纹理
    const framebuffer = gl.createFramebuffer();
    gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);
    gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.DEPTH_ATTACHMENT, gl.TEXTURE_2D, depthTexture, 0);

    // 检查帧缓冲区完整性
    if (gl.checkFramebufferStatus(gl.FRAMEBUFFER) === gl.FRAMEBUFFER_COMPLETE) {
        console.log('深度纹理帧缓冲区创建成功');
    }
}
```

#### EXT_texture_filter_anisotropic

提供各向异性过滤，改善纹理质量。

**代码示例：**

```javascript
const anisotropicExt = gl.getExtension('EXT_texture_filter_anisotropic');
if (anisotropicExt) {
    // 获取最大各向异性级别
    const maxAnisotropy = gl.getParameter(anisotropicExt.MAX_TEXTURE_MAX_ANISOTROPY_EXT);
    console.log('最大各向异性级别:', maxAnisotropy);

    // 设置各向异性过滤
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texParameterf(gl.TEXTURE_2D, anisotropicExt.TEXTURE_MAX_ANISOTROPY_EXT, Math.min(4, maxAnisotropy));
}
```

#### WEBGL_debug_renderer_info

获取 GPU 和驱动程序信息，主要用于调试。

**代码示例：**

```javascript
const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
if (debugInfo) {
    const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);

    console.log('GPU厂商:', vendor);
    console.log('GPU型号:', renderer);
}
```

#### WEBGL_lose_context

用于测试上下文丢失和恢复。

**代码示例：**

```javascript
const loseContextExt = gl.getExtension('WEBGL_lose_context');
if (loseContextExt) {
    // 模拟上下文丢失
    loseContextExt.loseContext();

    // 恢复上下文
    setTimeout(() => {
        loseContextExt.restoreContext();
    }, 1000);
}
```

#### OES_element_index_uint

允许使用 32 位无符号整数作为索引。

**代码示例：**

```javascript
const uint32IndexExt = gl.getExtension('OES_element_index_uint');
if (uint32IndexExt) {
    // 可以使用32位索引
    const indices = new Uint32Array([0, 1, 2, 3, 4, 5]);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);
    gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_INT, 0);
}
```

#### WEBGL_compressed_texture_s3tc

支持 S3TC 压缩纹理格式。

**代码示例：**

```javascript
const s3tcExt = gl.getExtension('WEBGL_compressed_texture_s3tc');
if (s3tcExt) {
    // 可用的压缩格式
    console.log('支持的S3TC格式:');
    console.log('DXT1:', s3tcExt.COMPRESSED_RGB_S3TC_DXT1_EXT);
    console.log('DXT3:', s3tcExt.COMPRESSED_RGBA_S3TC_DXT3_EXT);
    console.log('DXT5:', s3tcExt.COMPRESSED_RGBA_S3TC_DXT5_EXT);

    // 使用压缩纹理
    gl.compressedTexImage2D(gl.TEXTURE_2D, 0, s3tcExt.COMPRESSED_RGBA_S3TC_DXT5_EXT, width, height, 0, compressedData);
}
```

#### EXT_disjoint_timer_query

提供 GPU 时间查询功能（主要用于性能分析）。

**代码示例：**

```javascript
const timerExt = gl.getExtension('EXT_disjoint_timer_query');
if (timerExt) {
    // 创建查询对象
    const query = timerExt.createQueryEXT();

    // 开始时间查询
    timerExt.beginQueryEXT(timerExt.TIME_ELAPSED_EXT, query);

    // 执行渲染操作
    gl.drawElements(gl.TRIANGLES, indexCount, gl.UNSIGNED_SHORT, 0);

    // 结束查询
    timerExt.endQueryEXT(timerExt.TIME_ELAPSED_EXT);

    // 稍后检查结果
    function checkTimerResult() {
        const available = timerExt.getQueryObjectEXT(query, timerExt.QUERY_RESULT_AVAILABLE_EXT);
        const disjoint = gl.getParameter(timerExt.GPU_DISJOINT_EXT);

        if (available && !disjoint) {
            const timeElapsed = timerExt.getQueryObjectEXT(query, timerExt.QUERY_RESULT_EXT);
            console.log('GPU渲染时间:', timeElapsed / 1000000, 'ms');
        } else if (!available) {
            requestAnimationFrame(checkTimerResult);
        }
    }

    requestAnimationFrame(checkTimerResult);
}
```

---

### 扩展使用最佳实践

#### 1. 扩展检查模式

```javascript
class WebGLExtensions {
    constructor(gl) {
        this.gl = gl;
        this.extensions = new Map();
        this.checkExtensions();
    }

    checkExtensions() {
        const extensionList = [
            'OES_vertex_array_object',
            'ANGLE_instanced_arrays',
            'OES_texture_float',
            'WEBGL_depth_texture',
            'EXT_texture_filter_anisotropic',
            'WEBGL_debug_renderer_info',
            'OES_element_index_uint',
            'WEBGL_compressed_texture_s3tc',
            'EXT_disjoint_timer_query',
        ];

        extensionList.forEach((name) => {
            const ext = this.gl.getExtension(name);
            if (ext) {
                this.extensions.set(name, ext);
                console.log(`✓ ${name} 扩展可用`);
            } else {
                console.log(`✗ ${name} 扩展不支持`);
            }
        });
    }

    get(name) {
        return this.extensions.get(name);
    }

    has(name) {
        return this.extensions.has(name);
    }
}

// 使用示例
const extensions = new WebGLExtensions(gl);

if (extensions.has('OES_vertex_array_object')) {
    const vaoExt = extensions.get('OES_vertex_array_object');
    // 使用VAO扩展
}
```

#### 2. 功能降级处理

```javascript
function createVertexArray(gl, extensions) {
    if (gl.createVertexArray) {
        // WebGL 2.0 原生支持
        return gl.createVertexArray();
    } else if (extensions.has('OES_vertex_array_object')) {
        // WebGL 1.0 扩展支持
        const vaoExt = extensions.get('OES_vertex_array_object');
        return vaoExt.createVertexArrayOES();
    } else {
        // 不支持VAO，返回null或使用替代方案
        console.warn('VAO不支持，将使用传统方式');
        return null;
    }
}

function bindVertexArray(gl, extensions, vao) {
    if (gl.bindVertexArray) {
        gl.bindVertexArray(vao);
    } else if (extensions.has('OES_vertex_array_object')) {
        const vaoExt = extensions.get('OES_vertex_array_object');
        vaoExt.bindVertexArrayOES(vao);
    } else {
        // 手动设置顶点属性
        setupVertexAttributes();
    }
}
```

#### 3. 性能优化扩展使用

```javascript
function optimizeRendering(gl, extensions) {
    // 使用实例化渲染减少绘制调用
    if (extensions.has('ANGLE_instanced_arrays')) {
        const instancedExt = extensions.get('ANGLE_instanced_arrays');
        instancedExt.drawArraysInstancedANGLE(gl.TRIANGLES, 0, 3, 1000);
    } else {
        // 降级到多次绘制调用
        for (let i = 0; i < 1000; i++) {
            updateInstanceUniforms(i);
            gl.drawArrays(gl.TRIANGLES, 0, 3);
        }
    }

    // 使用各向异性过滤改善纹理质量
    if (extensions.has('EXT_texture_filter_anisotropic')) {
        const anisotropicExt = extensions.get('EXT_texture_filter_anisotropic');
        const maxAnisotropy = gl.getParameter(anisotropicExt.MAX_TEXTURE_MAX_ANISOTROPY_EXT);
        gl.texParameterf(gl.TEXTURE_2D, anisotropicExt.TEXTURE_MAX_ANISOTROPY_EXT, maxAnisotropy);
    }
}
```

---

## 总结

这个 WebGL 函数详解大全现在已经包含了：

### 📚 完整的函数覆盖

-   **WebGL 1.0 核心函数** - 所有基础 WebGL 功能
-   **WebGL 2.0 新增函数** - Transform Feedback、UBO、查询对象等高级特性
-   **WebGL 扩展函数** - 常用扩展的详细使用方法
-   **渲染状态管理** - 完整的渲染管线控制
-   **绘制命令** - 从基础到高级的所有绘制方式

### 🎯 详细的文档内容

-   **完整的函数签名** - 包含所有参数和返回值
-   **详细的参数说明** - 每个参数的所有可能取值和效果
-   **实用的代码示例** - 真实可用的代码片段
-   **最佳实践指导** - 性能优化和错误避免
-   **学习路径规划** - 从初学者到专家的完整路径

### 🚀 实用的特性

-   **中文编写** - 便于中文开发者学习
-   **结构化组织** - 清晰的目录和分类
-   **性能优化指南** - 实际项目中的优化技巧
-   **错误处理建议** - 常见问题的解决方案
-   **扩展兼容性** - 跨平台和降级处理

这个文档可以作为 WebGL 开发的完整参考手册，适合不同水平的开发者使用。

### 📖 使用建议

1. **初学者** - 从学习路径指南开始，按照推荐的顺序学习
2. **中级开发者** - 重点关注性能优化指南和最佳实践
3. **高级开发者** - 深入研究 WebGL 2.0 特性和扩展函数
4. **项目开发** - 将此文档作为 API 参考手册使用

### 🔄 持续更新

随着 WebGL 技术的发展，这个文档将持续更新，添加新的函数、优化建议和最佳实践。建议定期查看更新内容，保持技术的前沿性。

```javascript
gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);
gl.bindFramebuffer(gl.FRAMEBUFFER, null); // 绑定到默认帧缓冲区
```

**作用：** 绑定帧缓冲区
**目标类型：**

-   `gl.FRAMEBUFFER` - 读写帧缓冲区
-   `gl.DRAW_FRAMEBUFFER` - 绘制帧缓冲区(WebGL2)
-   `gl.READ_FRAMEBUFFER` - 读取帧缓冲区(WebGL2)

### `gl.framebufferTexture2D(target, attachment, textarget, texture, level)`

```javascript
gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);
```

**作用：** 将纹理附加到帧缓冲区
**参数：**

-   `target` - 帧缓冲区目标
-   `attachment` - 附着点
-   `textarget` - 纹理目标
-   `texture` - 纹理对象
-   `level` - mipmap 级别

### `gl.createRenderbuffer()`

```javascript
const renderbuffer = gl.createRenderbuffer();
```

**作用：** 创建渲染缓冲区对象

### `gl.bindRenderbuffer(target, renderbuffer)`

```javascript
gl.bindRenderbuffer(gl.RENDERBUFFER, renderbuffer);
```

**作用：** 绑定渲染缓冲区

### `gl.renderbufferStorage(target, internalformat, width, height)`

```javascript
gl.renderbufferStorage(gl.RENDERBUFFER, gl.DEPTH_COMPONENT16, width, height);
```

**作用：** 设置渲染缓冲区存储格式

### `gl.framebufferRenderbuffer(target, attachment, renderbuffertarget, renderbuffer)`

```javascript
gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.DEPTH_ATTACHMENT, gl.RENDERBUFFER, depthBuffer);
```

**作用：** 将渲染缓冲区附加到帧缓冲区

### `gl.checkFramebufferStatus(target)`

```javascript
const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);
if (status !== gl.FRAMEBUFFER_COMPLETE) {
    console.error('帧缓冲区不完整');
}
```

**作用：** 检查帧缓冲区完整性状态
**返回状态：**

-   `gl.FRAMEBUFFER_COMPLETE` - 完整
-   `gl.FRAMEBUFFER_INCOMPLETE_ATTACHMENT` - 附着不完整
-   `gl.FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT` - 缺少附着
-   `gl.FRAMEBUFFER_UNSUPPORTED` - 不支持的格式

### `gl.deleteFramebuffer(framebuffer)`

```javascript
gl.deleteFramebuffer(framebuffer);
```

**作用：** 删除帧缓冲区对象

### `gl.deleteRenderbuffer(renderbuffer)`

```javascript
gl.deleteRenderbuffer(renderbuffer);
```

**作用：** 删除渲染缓冲区对象

## 查询函数

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 查询函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.getParameter(pname)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
const viewport = gl.getParameter(gl.VIEWPORT);
const version = gl.getParameter(gl.VERSION);
```

**作用：** 获取 WebGL 参数值
**常用参数：**

-   `gl.VERSION` - WebGL 版本
-   `gl.VENDOR` - 厂商信息
-   `gl.RENDERER` - 渲染器信息
-   `gl.MAX_TEXTURE_SIZE` - 最大纹理尺寸
-   `gl.MAX_VERTEX_ATTRIBS` - 最大顶点属性数
-   `gl.MAX_TEXTURE_IMAGE_UNITS` - 最大纹理单元数

### `gl.getError()`

```javascript
const error = gl.getError();
if (error !== gl.NO_ERROR) {
    console.error('WebGL错误:', error);
}
```

**作用：** 获取错误状态
**错误类型：**

-   `gl.NO_ERROR` - 无错误
-   `gl.INVALID_ENUM` - 无效枚举
-   `gl.INVALID_VALUE` - 无效值
-   `gl.INVALID_OPERATION` - 无效操作
-   `gl.OUT_OF_MEMORY` - 内存不足

### `gl.getSupportedExtensions()`

```javascript
const extensions = gl.getSupportedExtensions();
console.log('支持的扩展:', extensions);
```

**作用：** 获取支持的扩展列表

### `gl.getExtension(name)`

```javascript
const ext = gl.getExtension('OES_vertex_array_object');
if (ext) {
    // 使用扩展功能
}
```

**作用：** 获取指定扩展对象

### `gl.isEnabled(cap)`

```javascript
const depthTestEnabled = gl.isEnabled(gl.DEPTH_TEST);
```

**作用：** 检查指定功能是否启用

═══════════════════════════════════════════════════════════════════════════════

## 像素操作函数

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 像素操作函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.readPixels(x, y, width, height, format, type, pixels)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
const pixels = new Uint8Array(width * height * 4);
gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels);
```

**作用：** 从帧缓冲区读取像素数据

### `gl.pixelStorei(pname, param)`

```javascript
gl.pixelStorei(gl.UNPACK_FLIP_Y_WEBGL, true);
gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, false);
```

**作用：** 设置像素存储参数
**常用参数：**

-   `gl.UNPACK_FLIP_Y_WEBGL` - 垂直翻转
-   `gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL` - 预乘 alpha
-   `gl.UNPACK_ALIGNMENT` - 对齐方式

═══════════════════════════════════════════════════════════════════════════════

## 同步和查询对象 (WebGL 2.0)

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 同步和查询对象 (WebGL 2.0) ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.fenceSync(condition, flags)`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
const sync = gl.fenceSync(gl.SYNC_GPU_COMMANDS_COMPLETE, 0);
```

**作用：** 创建同步对象

### `gl.clientWaitSync(sync, flags, timeout)`

```javascript
const result = gl.clientWaitSync(sync, 0, 0);
```

**作用：** 等待同步对象完成

### `gl.deleteSync(sync)`

```javascript
gl.deleteSync(sync);
```

**作用：** 删除同步对象

═══════════════════════════════════════════════════════════════════════════════

## 变换反馈 (WebGL 2.0)

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 变换反馈 (WebGL 2.0) ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### `gl.createTransformFeedback()`

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
const transformFeedback = gl.createTransformFeedback();
```

**作用：** 创建变换反馈对象

### `gl.bindTransformFeedback(target, transformFeedback)`

```javascript
gl.bindTransformFeedback(gl.TRANSFORM_FEEDBACK, transformFeedback);
```

**作用：** 绑定变换反馈对象

### `gl.beginTransformFeedback(primitiveMode)`

```javascript
gl.beginTransformFeedback(gl.TRIANGLES);
```

**作用：** 开始变换反馈

### `gl.endTransformFeedback()`

```javascript
gl.endTransformFeedback();
```

**作用：** 结束变换反馈

### `gl.transformFeedbackVaryings(program, varyings, bufferMode)`

```javascript
gl.transformFeedbackVaryings(program, ['v_position', 'v_velocity'], gl.SEPARATE_ATTRIBS);
```

**作用：** 指定变换反馈变量

═══════════════════════════════════════════════════════════════════════════════

## 常用函数组合模式

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 常用函数组合模式 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### 基础渲染流程

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
// 1. 创建和编译着色器
function createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
    }
    return shader;
}

// 2. 创建着色器程序
function createProgram(gl, vertexShader, fragmentShader) {
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('程序链接错误:', gl.getProgramInfoLog(program));
        gl.deleteProgram(program);
        return null;
    }
    return program;
}

// 3. 设置几何体数据
function setupGeometry(gl, program, vertices, indices) {
    // 创建VAO
    const vao = gl.createVertexArray();
    gl.bindVertexArray(vao);

    // 创建顶点缓冲区
    const vertexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

    // 配置顶点属性
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

    // 创建索引缓冲区
    const indexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);

    gl.bindVertexArray(null);

    return { vao, vertexBuffer, indexBuffer, indexCount: indices.length };
}

// 4. 渲染循环
function render(gl, program, geometry, uniforms) {
    // 清除缓冲区
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

    // 使用着色器程序
    gl.useProgram(program);

    // 设置uniform变量
    for (const [name, value] of Object.entries(uniforms)) {
        const location = gl.getUniformLocation(program, name);
        if (location) {
            if (value.length === 16) {
                gl.uniformMatrix4fv(location, false, value);
            } else if (value.length === 3) {
                gl.uniform3fv(location, value);
            }
            // ... 其他类型
        }
    }

    // 绑定几何体并绘制
    gl.bindVertexArray(geometry.vao);
    gl.drawElements(gl.TRIANGLES, geometry.indexCount, gl.UNSIGNED_SHORT, 0);
    gl.bindVertexArray(null);
}
```

### 纹理加载和设置

```javascript
function createTexture(gl, image) {
    const texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, texture);

    // 设置纹理参数
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

    // 上传纹理数据
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

    return texture;
}

function useTexture(gl, program, texture, uniformName, textureUnit = 0) {
    gl.activeTexture(gl.TEXTURE0 + textureUnit);
    gl.bindTexture(gl.TEXTURE_2D, texture);

    const location = gl.getUniformLocation(program, uniformName);
    gl.uniform1i(location, textureUnit);
}
```

### 帧缓冲区设置

```javascript
function createFramebuffer(gl, width, height) {
    // 创建帧缓冲区
    const framebuffer = gl.createFramebuffer();
    gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);

    // 创建颜色纹理
    const colorTexture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, colorTexture);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
    gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, colorTexture, 0);

    // 创建深度缓冲区
    const depthBuffer = gl.createRenderbuffer();
    gl.bindRenderbuffer(gl.RENDERBUFFER, depthBuffer);
    gl.renderbufferStorage(gl.RENDERBUFFER, gl.DEPTH_COMPONENT16, width, height);
    gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.DEPTH_ATTACHMENT, gl.RENDERBUFFER, depthBuffer);

    // 检查完整性
    if (gl.checkFramebufferStatus(gl.FRAMEBUFFER) !== gl.FRAMEBUFFER_COMPLETE) {
        console.error('帧缓冲区不完整');
    }

    gl.bindFramebuffer(gl.FRAMEBUFFER, null);

    return { framebuffer, colorTexture, depthBuffer };
}
```

═══════════════════════════════════════════════════════════════════════════════

## 性能优化相关函数

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 性能优化相关函数 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### 状态缓存

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
class WebGLStateManager {
    constructor(gl) {
        this.gl = gl;
        this.currentProgram = null;
        this.currentVAO = null;
        this.currentTextures = new Map();
    }

    useProgram(program) {
        if (this.currentProgram !== program) {
            this.gl.useProgram(program);
            this.currentProgram = program;
        }
    }

    bindVertexArray(vao) {
        if (this.currentVAO !== vao) {
            this.gl.bindVertexArray(vao);
            this.currentVAO = vao;
        }
    }

    bindTexture(target, texture, unit = 0) {
        const key = `${target}_${unit}`;
        if (this.currentTextures.get(key) !== texture) {
            this.gl.activeTexture(this.gl.TEXTURE0 + unit);
            this.gl.bindTexture(target, texture);
            this.currentTextures.set(key, texture);
        }
    }
}
```

### 批量绘制

```javascript
function drawBatch(gl, program, objects) {
    gl.useProgram(program);

    // 按VAO分组
    const groups = new Map();
    for (const obj of objects) {
        if (!groups.has(obj.vao)) {
            groups.set(obj.vao, []);
        }
        groups.get(obj.vao).push(obj);
    }

    // 批量绘制每组
    for (const [vao, group] of groups) {
        gl.bindVertexArray(vao);

        for (const obj of group) {
            // 设置对象特定的uniform
            gl.uniformMatrix4fv(obj.modelMatrixLocation, false, obj.modelMatrix);
            gl.drawElements(gl.TRIANGLES, obj.indexCount, gl.UNSIGNED_SHORT, 0);
        }
    }

    gl.bindVertexArray(null);
}
```

═══════════════════════════════════════════════════════════════════════════════

## 错误处理和调试

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 错误处理和调试 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### 错误检查包装器

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

```javascript
function wrapGLFunction(gl, functionName) {
    const originalFunction = gl[functionName];
    gl[functionName] = function (...args) {
        const result = originalFunction.apply(this, args);
        const error = gl.getError();
        if (error !== gl.NO_ERROR) {
            console.error(`WebGL错误在 ${functionName}:`, getErrorString(error));
        }
        return result;
    };
}

function getErrorString(error) {
    switch (error) {
        case WebGLRenderingContext.NO_ERROR:
            return 'NO_ERROR';
        case WebGLRenderingContext.INVALID_ENUM:
            return 'INVALID_ENUM';
        case WebGLRenderingContext.INVALID_VALUE:
            return 'INVALID_VALUE';
        case WebGLRenderingContext.INVALID_OPERATION:
            return 'INVALID_OPERATION';
        case WebGLRenderingContext.OUT_OF_MEMORY:
            return 'OUT_OF_MEMORY';
        default:
            return `未知错误: ${error}`;
    }
}
```

### 资源管理

```javascript
class WebGLResourceManager {
    constructor(gl) {
        this.gl = gl;
        this.resources = {
            buffers: new Set(),
            textures: new Set(),
            programs: new Set(),
            shaders: new Set(),
            vaos: new Set(),
            framebuffers: new Set(),
            renderbuffers: new Set(),
        };
    }

    createBuffer() {
        const buffer = this.gl.createBuffer();
        this.resources.buffers.add(buffer);
        return buffer;
    }

    createTexture() {
        const texture = this.gl.createTexture();
        this.resources.textures.add(texture);
        return texture;
    }

    // ... 其他创建方法

    cleanup() {
        // 清理所有资源
        for (const buffer of this.resources.buffers) {
            this.gl.deleteBuffer(buffer);
        }
        for (const texture of this.resources.textures) {
            this.gl.deleteTexture(texture);
        }
        // ... 清理其他资源

        // 清空集合
        Object.values(this.resources).forEach((set) => set.clear());
    }
}
```

═══════════════════════════════════════════════════════════════════════════════

## 性能优化指南

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 性能优化指南 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### 1. 状态管理优化

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

#### 减少状态切换

```javascript
// ❌ 不好的做法 - 频繁切换状态
for (const object of objects) {
    gl.useProgram(object.program);
    gl.bindTexture(gl.TEXTURE_2D, object.texture);
    // 绘制对象
}

// ✅ 好的做法 - 按状态分组
const groups = groupByState(objects);
for (const [state, group] of groups) {
    gl.useProgram(state.program);
    gl.bindTexture(gl.TEXTURE_2D, state.texture);
    for (const object of group) {
        // 绘制对象
    }
}
```

#### 状态缓存

```javascript
class StateManager {
    constructor(gl) {
        this.gl = gl;
        this.currentProgram = null;
        this.currentTexture = null;
    }

    useProgram(program) {
        if (this.currentProgram !== program) {
            this.gl.useProgram(program);
            this.currentProgram = program;
        }
    }
}
```

### 2. 缓冲区优化

#### 使用 VAO 减少状态设置

```javascript
// ✅ 使用VAO封装顶点状态
const vao = gl.createVertexArray();
gl.bindVertexArray(vao);
// 设置所有顶点属性
gl.bindVertexArray(null);

// 渲染时只需绑定VAO
gl.bindVertexArray(vao);
gl.drawElements(gl.TRIANGLES, indexCount, gl.UNSIGNED_SHORT, 0);
```

#### 选择合适的缓冲区使用模式

```javascript
// 静态数据
gl.bufferData(gl.ARRAY_BUFFER, staticVertices, gl.STATIC_DRAW);

// 每帧更新的数据
gl.bufferData(gl.ARRAY_BUFFER, dynamicVertices, gl.STREAM_DRAW);

// 偶尔更新的数据
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.DYNAMIC_DRAW);
```

### 3. 纹理优化

#### 纹理尺寸和格式

```javascript
// ✅ 使用2的幂次方尺寸（WebGL 1.0）
const powerOfTwoTexture = createTexture(512, 512);

// ✅ 选择合适的纹理格式
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGB, gl.RGB, gl.UNSIGNED_BYTE, image); // 无alpha通道
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image); // 有alpha通道
```

#### 纹理压缩

```javascript
// 检查压缩纹理支持
const ext = gl.getExtension('WEBGL_compressed_texture_s3tc');
if (ext) {
    gl.compressedTexImage2D(gl.TEXTURE_2D, 0, ext.COMPRESSED_RGBA_S3TC_DXT5_EXT, width, height, 0, compressedData);
}
```

### 4. 绘制调用优化

#### 实例化渲染

```javascript
// WebGL 2.0 - 实例化绘制
gl.drawArraysInstanced(gl.TRIANGLES, 0, vertexCount, instanceCount);
gl.drawElementsInstanced(gl.TRIANGLES, indexCount, gl.UNSIGNED_SHORT, 0, instanceCount);
```

#### 批量绘制

```javascript
// 合并相同材质的对象
function batchDraw(objects) {
    const batches = groupByMaterial(objects);
    for (const batch of batches) {
        setupMaterial(batch.material);
        for (const object of batch.objects) {
            updateUniforms(object);
            draw(object);
        }
    }
}
```

### 5. 着色器优化

#### 减少 uniform 更新

```javascript
// ❌ 每次绘制都更新所有uniform
function draw(object) {
    gl.uniformMatrix4fv(modelLocation, false, object.modelMatrix);
    gl.uniformMatrix4fv(viewLocation, false, camera.viewMatrix);
    gl.uniformMatrix4fv(projLocation, false, camera.projMatrix);
}

// ✅ 只更新变化的uniform
function draw(object) {
    if (object.modelMatrixChanged) {
        gl.uniformMatrix4fv(modelLocation, false, object.modelMatrix);
        object.modelMatrixChanged = false;
    }
}
```

#### 使用 Uniform Buffer Objects (WebGL 2.0)

```javascript
// 创建UBO
const ubo = gl.createBuffer();
gl.bindBuffer(gl.UNIFORM_BUFFER, ubo);
gl.bufferData(gl.UNIFORM_BUFFER, uniformData, gl.DYNAMIC_DRAW);

// 绑定到uniform块
const blockIndex = gl.getUniformBlockIndex(program, 'Matrices');
gl.uniformBlockBinding(program, blockIndex, 0);
gl.bindBufferBase(gl.UNIFORM_BUFFER, 0, ubo);
```

### 6. 内存管理

#### 及时释放资源

```javascript
class ResourceManager {
    constructor(gl) {
        this.gl = gl;
        this.resources = new Set();
    }

    createTexture() {
        const texture = this.gl.createTexture();
        this.resources.add({ type: 'texture', resource: texture });
        return texture;
    }

    cleanup() {
        for (const item of this.resources) {
            if (item.type === 'texture') {
                this.gl.deleteTexture(item.resource);
            }
            // ... 其他资源类型
        }
        this.resources.clear();
    }
}
```

### 7. 性能监控

#### 使用扩展获取性能信息

```javascript
// GPU时间查询 (WebGL 2.0)
const ext = gl.getExtension('EXT_disjoint_timer_query_webgl2');
if (ext) {
    const query = gl.createQuery();
    gl.beginQuery(ext.TIME_ELAPSED_EXT, query);
    // 渲染代码
    gl.endQuery(ext.TIME_ELAPSED_EXT);

    // 稍后检查结果
    if (gl.getQueryParameter(query, gl.QUERY_RESULT_AVAILABLE)) {
        const timeElapsed = gl.getQueryParameter(query, gl.QUERY_RESULT);
        console.log('GPU时间:', timeElapsed / 1000000, 'ms');
    }
}
```

#### 帧率监控

```javascript
class PerformanceMonitor {
    constructor() {
        this.frameCount = 0;
        this.lastTime = performance.now();
    }

    update() {
        this.frameCount++;
        const currentTime = performance.now();

        if (currentTime - this.lastTime >= 1000) {
            const fps = this.frameCount;
            console.log('FPS:', fps);
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }
}
```

═══════════════════════════════════════════════════════════════════════════════

## 常见错误和注意事项

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 常见错误和注意事项 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

### 1. 上下文相关错误

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

#### 上下文丢失处理

```javascript
canvas.addEventListener('webglcontextlost', function (event) {
    event.preventDefault();
    console.warn('WebGL上下文丢失');
    // 停止渲染循环
});

canvas.addEventListener('webglcontextrestored', function () {
    console.log('WebGL上下文恢复');
    // 重新初始化资源
    initWebGL();
});
```

### 2. 着色器错误

#### 常见编译错误

```javascript
// ❌ 精度限定符缺失（片段着色器）
// 错误：没有指定精度
uniform sampler2D u_texture;

// ✅ 正确：指定精度
precision mediump float;
uniform sampler2D u_texture;
```

### 3. 纹理错误

#### 纹理完整性检查

```javascript
function isTexturePowerOfTwo(width, height) {
    return (width & (width - 1)) === 0 && (height & (height - 1)) === 0;
}

// WebGL 1.0纹理限制检查
if (!isTexturePowerOfTwo(image.width, image.height)) {
    // 非2的幂次方纹理只能使用特定设置
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
}
```

### 4. 缓冲区错误

#### 数据类型匹配

```javascript
// ❌ 错误：类型不匹配
const vertices = [1.0, 2.0, 3.0]; // 普通数组
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

// ✅ 正确：使用TypedArray
const vertices = new Float32Array([1.0, 2.0, 3.0]);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
```

═══════════════════════════════════════════════════════════════════════════════

## 总结

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 总结 ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

这个 WebGL 函数详解大全涵盖了 WebGL 开发中最重要的函数和概念：

### 核心函数类别

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼

1. **着色器管理** - 创建、编译、链接着色器程序
2. **缓冲区操作** - 管理顶点数据和索引数据
3. **顶点属性** - 配置 GPU 如何解释顶点数据
4. **纹理操作** - 加载和使用纹理资源
5. **渲染状态** - 控制渲染管线的各种状态
6. **绘制命令** - 实际的图形绘制操作

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

### 最佳实践要点

-   **状态管理** - 避免不必要的状态切换，使用状态缓存
-   **资源清理** - 及时释放 GPU 资源，防止内存泄漏
-   **错误处理** - 检查和处理 WebGL 错误，监听上下文事件
-   **性能优化** - 使用 VAO、批量绘制、实例化渲染等技术
-   **内存管理** - 合理使用缓冲区，选择适当的数据格式
-   **调试工具** - 利用浏览器开发工具和 WebGL 扩展进行性能分析

掌握这些函数的正确使用方法是成为高效 WebGL 开发者的基础。建议在实际项目中逐步应用这些函数，并结合具体的渲染需求来深入理解它们的作用和最佳使用场景。通过遵循性能优化指南和避免常见错误，可以创建出高性能、稳定的 WebGL 应用程序。
