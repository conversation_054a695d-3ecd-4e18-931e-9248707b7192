# Texture 构造函数参数详解

## 基本参数

### 1. `gl` (必需)

-   **类型**: `WebGLRenderingContext`
-   **作用**: WebGL 上下文对象，所有 WebGL 操作都需要通过它进行
-   **示例**: `const texture = new Texture(gl, options)`

### 2. `image` (可选)

-   **类型**: `HTMLImageElement | HTMLCanvasElement | HTMLVideoElement | ArrayBufferView | Array`
-   **默认值**: `undefined`
-   **作用**: 纹理的数据源
-   **可能的值**:
    -   `HTMLImageElement`: 图片元素 (`<img>`)
    -   `HTMLCanvasElement`: 画布元素 (`<canvas>`)
    -   `HTMLVideoElement`: 视频元素 (`<video>`)
    -   `ArrayBufferView`: 类型化数组 (如 `Uint8Array`, `Float32Array`)
    -   `Array`: 立方体贴图的 6 个面图像数组
-   **示例**:
    ```javascript
    const img = new Image();
    img.src = 'texture.jpg';
    const texture = new Texture(gl, { image: img });
    ```

## 纹理类型参数

### 3. `target` (可选)

-   **类型**: `number`
-   **默认值**: `gl.TEXTURE_2D`
-   **作用**: 指定纹理目标类型，决定纹理的维度和使用方式
-   **WebGL1 可能的值**:
    -   `gl.TEXTURE_2D`: 2D 纹理
        -   最常用的纹理类型
        -   单张平面图像
        -   适用于大多数渲染场景
        -   支持所有过滤和包裹模式
    -   `gl.TEXTURE_CUBE_MAP`: 立方体贴图
        -   由 6 个面组成的立方体纹理
        -   用于环境映射、反射、天空盒
        -   需要 6 张图像 (±X, ±Y, ±Z 方向)
        -   使用 3D 方向向量进行采样
-   **WebGL2 额外的值**:
    -   `gl.TEXTURE_3D`: 3D 纹理
        -   三维体积纹理
        -   用于体积渲染、3D 噪声
        -   使用 3D 坐标 (s,t,r) 进行采样
        -   占用大量显存
    -   `gl.TEXTURE_2D_ARRAY`: 2D 纹理数组
        -   多层 2D 纹理的集合
        -   所有层具有相同尺寸和格式
        -   用于纹理图集、动画帧
        -   比多个独立纹理更高效
-   **使用场景**:
    -   **普通贴图**: `TEXTURE_2D`
    -   **天空盒/环境映射**: `TEXTURE_CUBE_MAP`
    -   **体积效果**: `TEXTURE_3D`
    -   **纹理图集**: `TEXTURE_2D_ARRAY`
-   **性能考虑**:
    -   2D 纹理: 最快，兼容性最好
    -   立方体贴图: 中等开销
    -   3D 纹理: 高内存占用
    -   纹理数组: 减少绑定开销

### 4. `type` (可选)

-   **类型**: `number`
-   **默认值**: `gl.UNSIGNED_BYTE`
-   **作用**: 指定纹理数据的数据类型，决定每个像素分量的数据格式
-   **WebGL1 可能的值**:
    -   `gl.UNSIGNED_BYTE`: 8 位无符号整数 (0-255)
        -   最常用，适合普通图像
        -   每个颜色分量占 1 字节
    -   `gl.UNSIGNED_SHORT_5_6_5`: 16 位 RGB 格式
        -   R:5 位, G:6 位, B:5 位，无 alpha
        -   节省内存但精度较低
    -   `gl.UNSIGNED_SHORT_4_4_4_4`: 16 位 RGBA 格式
        -   每个分量 4 位，总共 16 位
        -   低精度但包含 alpha 通道
    -   `gl.UNSIGNED_SHORT_5_5_5_1`: 16 位 RGBA 格式
        -   RGB 各 5 位，alpha 1 位
        -   适合需要简单透明度的场景
    -   `gl.FLOAT`: 32 位浮点数 (需要 `OES_texture_float` 扩展)
        -   高精度，支持 HDR
        -   每个分量占 4 字节
-   **WebGL2 额外的值**:
    -   `gl.HALF_FLOAT`: 16 位浮点数
        -   平衡精度和内存使用
        -   每个分量占 2 字节
    -   `gl.UNSIGNED_SHORT`: 16 位无符号整数
        -   适合深度纹理
    -   `gl.UNSIGNED_INT`: 32 位无符号整数
        -   高精度整数数据
    -   `gl.BYTE`: 8 位有符号整数 (-128 到 127)
    -   `gl.SHORT`: 16 位有符号整数
    -   `gl.INT`: 32 位有符号整数
    -   `gl.UNSIGNED_INT_24_8`: 24 位深度 + 8 位模板
        -   专用于深度模板缓冲
    -   `gl.FLOAT_32_UNSIGNED_INT_24_8_REV`: 32 位浮点深度 + 8 位模板
-   **选择指南**:
    -   **普通图像**: `gl.UNSIGNED_BYTE` (最常用)
    -   **HDR 图像**: `gl.FLOAT` 或 `gl.HALF_FLOAT`
    -   **深度缓冲**: `gl.UNSIGNED_SHORT` 或 `gl.UNSIGNED_INT`
    -   **数据纹理**: 根据数据范围选择合适类型
    -   **移动设备**: 优先使用 `gl.UNSIGNED_BYTE` 以节省带宽
-   **性能影响**:
    -   `UNSIGNED_BYTE`: 最快，兼容性最好
    -   `FLOAT`: 最慢，但精度最高
    -   `HALF_FLOAT`: 平衡选择

### 5. `format` (可选)

-   **类型**: `number`
-   **默认值**: `gl.RGBA`
-   **作用**: 指定纹理的像素格式，决定每个像素包含哪些颜色分量
-   **WebGL1 可能的值**:
    -   `gl.RGBA`: 红绿蓝透明度 (4 通道)
        -   最常用的格式
        -   支持完整的颜色和透明度信息
        -   每像素 4 个分量
    -   `gl.RGB`: 红绿蓝 (3 通道)
        -   不包含透明度信息
        -   节省 25%的内存
        -   适合不透明图像
    -   `gl.LUMINANCE`: 灰度 (1 通道)
        -   单色图像，只有亮度信息
        -   最节省内存
        -   适合黑白图像、高度图
    -   `gl.LUMINANCE_ALPHA`: 灰度+透明度 (2 通道)
        -   灰度图像带透明度
        -   适合单色但需要透明效果的纹理
    -   `gl.ALPHA`: 仅透明度 (1 通道)
        -   只包含透明度信息
        -   适合遮罩纹理
-   **WebGL2 额外的值**:
    -   `gl.RED`: 单个红色通道
        -   替代 LUMINANCE，语义更清晰
    -   `gl.RG`: 红绿双通道
        -   适合法线贴图的 XY 分量
        -   或存储两个独立的单通道数据
    -   `gl.RED_INTEGER`: 整数红色通道
        -   用于整数纹理
    -   `gl.RG_INTEGER`: 整数红绿通道
    -   `gl.RGB_INTEGER`: 整数 RGB 通道
    -   `gl.RGBA_INTEGER`: 整数 RGBA 通道
    -   `gl.DEPTH_COMPONENT`: 深度分量
        -   用于深度纹理
    -   `gl.DEPTH_STENCIL`: 深度+模板
        -   用于深度模板缓冲
-   **选择指南**:
    -   **彩色图像**: `gl.RGBA` 或 `gl.RGB`
    -   **法线贴图**: `gl.RG` (只需 XY，Z 可计算)
    -   **高度图**: `gl.RED` 或 `gl.LUMINANCE`
    -   **遮罩纹理**: `gl.ALPHA` 或 `gl.RED`
    -   **数据纹理**: 根据数据维度选择
-   **内存占用** (以 UNSIGNED_BYTE 为例):
    -   `RGBA`: 4 字节/像素
    -   `RGB`: 3 字节/像素
    -   `RG`: 2 字节/像素
    -   `RED/LUMINANCE`: 1 字节/像素

### 6. `internalFormat` (可选)

-   **类型**: `number`
-   **默认值**: `format` 的值
-   **作用**: 指定 GPU 内部存储格式，决定纹理在显存中的存储方式和精度
-   **WebGL1 可能的值**:
    -   `gl.RGBA`: 红绿蓝透明度，每通道 8 位
    -   `gl.RGB`: 红绿蓝，每通道 8 位
    -   `gl.LUMINANCE`: 灰度，8 位
    -   `gl.LUMINANCE_ALPHA`: 灰度+透明度，各 8 位
    -   `gl.ALPHA`: 仅透明度，8 位
-   **WebGL2 额外的值**:
    -   **8 位整数格式**:
        -   `gl.R8`: 单通道 8 位无符号整数
        -   `gl.RG8`: 双通道 8 位无符号整数
        -   `gl.RGB8`: 三通道 8 位无符号整数
        -   `gl.RGBA8`: 四通道 8 位无符号整数
    -   **16 位整数格式**:
        -   `gl.R16UI`: 单通道 16 位无符号整数
        -   `gl.RG16UI`: 双通道 16 位无符号整数
        -   `gl.RGB16UI`: 三通道 16 位无符号整数
        -   `gl.RGBA16UI`: 四通道 16 位无符号整数
    -   **32 位整数格式**:
        -   `gl.R32UI`: 单通道 32 位无符号整数
        -   `gl.RG32UI`: 双通道 32 位无符号整数
        -   `gl.RGB32UI`: 三通道 32 位无符号整数
        -   `gl.RGBA32UI`: 四通道 32 位无符号整数
    -   **浮点格式**:
        -   `gl.R16F`: 单通道 16 位浮点
        -   `gl.RG16F`: 双通道 16 位浮点
        -   `gl.RGB16F`: 三通道 16 位浮点
        -   `gl.RGBA16F`: 四通道 16 位浮点
        -   `gl.R32F`: 单通道 32 位浮点
        -   `gl.RG32F`: 双通道 32 位浮点
        -   `gl.RGB32F`: 三通道 32 位浮点
        -   `gl.RGBA32F`: 四通道 32 位浮点
    -   **压缩格式** (需要扩展支持):
        -   `gl.COMPRESSED_RGB_S3TC_DXT1_EXT`: DXT1 压缩 (无 alpha)
        -   `gl.COMPRESSED_RGBA_S3TC_DXT1_EXT`: DXT1 压缩 (1 位 alpha)
        -   `gl.COMPRESSED_RGBA_S3TC_DXT3_EXT`: DXT3 压缩
        -   `gl.COMPRESSED_RGBA_S3TC_DXT5_EXT`: DXT5 压缩
    -   **深度/模板格式**:
        -   `gl.DEPTH_COMPONENT16`: 16 位深度
        -   `gl.DEPTH_COMPONENT24`: 24 位深度
        -   `gl.DEPTH_COMPONENT32F`: 32 位浮点深度
        -   `gl.DEPTH24_STENCIL8`: 24 位深度 + 8 位模板
        -   `gl.DEPTH32F_STENCIL8`: 32 位浮点深度 + 8 位模板
-   **选择指南**:
    -   **普通图像**: `gl.RGBA8` 或 `gl.RGB8`
    -   **HDR 图像**: `gl.RGBA16F` 或 `gl.RGBA32F`
    -   **数据纹理**: 根据数据精度需求选择
    -   **法线贴图**: `gl.RG8` (只需 XY 分量)
    -   **深度缓冲**: `gl.DEPTH_COMPONENT24`
-   **性能考虑**:
    -   更高精度 = 更多显存占用
    -   压缩格式可大幅减少显存使用
    -   选择合适精度避免浪费资源

## 纹理包裹参数

### 7. `wrapS` (可选)

-   **类型**: `number`
-   **默认值**: `gl.CLAMP_TO_EDGE`
-   **作用**: S 方向(水平/U 轴)的纹理包裹模式，决定纹理坐标超出[0,1]范围时的行为
-   **可能的值**:
    -   `gl.CLAMP_TO_EDGE`: 边缘拉伸
        -   超出边界的坐标使用边缘像素的颜色
        -   避免边缘渗色问题
        -   推荐用于大多数情况
        -   WebGL1 中非 2 的幂纹理必须使用此模式
    -   `gl.REPEAT`: 重复平铺
        -   纹理在超出边界时重复
        -   创建无缝平铺效果
        -   适合地面、墙面等重复纹理
        -   WebGL1 要求纹理尺寸为 2 的幂
    -   `gl.MIRRORED_REPEAT`: 镜像重复 (仅 WebGL2)
        -   纹理重复时交替镜像翻转
        -   创建更自然的平铺效果
        -   减少重复模式的可见性
-   **使用场景**:
    -   **UI 纹理**: `CLAMP_TO_EDGE`
    -   **地面纹理**: `REPEAT` 或 `MIRRORED_REPEAT`
    -   **天空盒**: `CLAMP_TO_EDGE`
    -   **单次使用纹理**: `CLAMP_TO_EDGE`

### 8. `wrapT` (可选)

-   **类型**: `number`
-   **默认值**: `gl.CLAMP_TO_EDGE`
-   **作用**: T 方向(垂直/V 轴)的纹理包裹模式
-   **可能的值**: 与 wrapS 完全相同
-   **说明**: 通常与 wrapS 设置相同的值，但也可以独立设置以创建特殊效果

### 9. `wrapR` (可选)

-   **类型**: `number`
-   **默认值**: `gl.CLAMP_TO_EDGE`
-   **作用**: R 方向(深度/W 轴)的纹理包裹模式，仅用于 3D 纹理和纹理数组
-   **可能的值**: 与 wrapS 完全相同
-   **使用场景**: 3D 纹理、体积渲染、纹理数组等

## 纹理过滤参数

### 10. `generateMipmaps` (可选)

-   **类型**: `boolean`
-   **默认值**: `target === (gl.TEXTURE_2D || gl.TEXTURE_CUBE_MAP)`
-   **作用**: 是否自动生成 mipmap (多级渐远纹理)
-   **说明**:
    -   Mipmap 提供不同距离下的纹理细节
    -   减少摩尔纹，提高渲染性能
    -   WebGL1 要求纹理尺寸为 2 的幂才能生成 mipmap

### 11. `minFilter` (可选)

-   **类型**: `number`
-   **默认值**: `generateMipmaps ? gl.NEAREST_MIPMAP_LINEAR : gl.LINEAR`
-   **作用**: 纹理缩小时的过滤方式，决定当纹理被缩小显示时如何采样
-   **不使用 Mipmap 的值**:
    -   `gl.NEAREST`: 最近邻过滤
        -   选择最接近的像素
        -   产生像素化、锐利的效果
        -   性能最好，适合像素艺术风格
    -   `gl.LINEAR`: 线性过滤
        -   在相邻像素间插值
        -   产生平滑的效果
        -   适合大多数情况
-   **使用 Mipmap 的值** (需要 generateMipmaps=true):
    -   `gl.NEAREST_MIPMAP_NEAREST`: 最近 mipmap + 最近邻
        -   选择最接近的 mipmap 级别
        -   在该级别内使用最近邻采样
        -   可能在 mipmap 级别间产生突变
    -   `gl.LINEAR_MIPMAP_NEAREST`: 最近 mipmap + 线性
        -   选择最接近的 mipmap 级别
        -   在该级别内使用线性插值
        -   平滑但可能在级别间突变
    -   `gl.NEAREST_MIPMAP_LINEAR`: mipmap 间线性 + 最近邻
        -   在两个 mipmap 级别间插值
        -   在每个级别内使用最近邻
        -   消除级别间突变但保持像素化
    -   `gl.LINEAR_MIPMAP_LINEAR`: 三线性过滤
        -   在 mipmap 级别间插值
        -   在每个级别内也使用线性插值
        -   最高质量，最平滑的效果
        -   性能开销最大
-   **选择指南**:
    -   **像素艺术**: `gl.NEAREST`
    -   **普通纹理**: `gl.LINEAR` 或 `gl.LINEAR_MIPMAP_LINEAR`
    -   **性能优先**: `gl.NEAREST` 或 `gl.LINEAR`
    -   **质量优先**: `gl.LINEAR_MIPMAP_LINEAR`

### 12. `magFilter` (可选)

-   **类型**: `number`
-   **默认值**: `gl.LINEAR`
-   **作用**: 纹理放大时的过滤方式，决定当纹理被放大显示时如何采样
-   **可能的值** (注意：放大过滤器不能使用 mipmap):
    -   `gl.NEAREST`: 最近邻过滤
        -   选择最接近的像素值
        -   产生清晰的像素化效果
        -   性能最好
        -   适合像素艺术、复古游戏风格
        -   放大时会看到明显的像素块
    -   `gl.LINEAR`: 线性过滤
        -   在相邻像素间进行双线性插值
        -   产生平滑的放大效果
        -   适合大多数真实感渲染
        -   放大时边缘会变模糊但更自然
-   **选择指南**:
    -   **像素艺术/复古风格**: `gl.NEAREST`
    -   **照片/真实感纹理**: `gl.LINEAR`
    -   **UI 元素**: 通常使用 `gl.LINEAR`
    -   **文字纹理**: 可能需要 `gl.NEAREST` 保持清晰度

### 13. `anisotropy` (可选)

-   **类型**: `number`
-   **默认值**: `0`
-   **作用**: 各向异性过滤级别 (需要扩展支持)
-   **范围**: 0 到硬件支持的最大值 (通常 1-16)
-   **效果**: 改善倾斜表面的纹理清晰度

## 像素处理参数

### 14. `premultiplyAlpha` (可选)

-   **类型**: `boolean`
-   **默认值**: `false`
-   **作用**: 是否预乘 alpha 通道
-   **说明**:
    -   `true`: RGB 值预先乘以 alpha 值
    -   `false`: 保持原始 RGB 和 alpha 值分离

### 15. `unpackAlignment` (可选)

-   **类型**: `number`
-   **默认值**: `4`
-   **作用**: 像素数据的字节对齐方式
-   **可能的值**: 1, 2, 4, 8
-   **说明**:
    -   4 字节对齐适用于 RGBA 格式
    -   1 字节对齐适用于单通道格式

### 16. `flipY` (可选)

-   **类型**: `boolean`
-   **默认值**: `target == (gl.TEXTURE_2D || gl.TEXTURE_3D) ? true : false`
-   **作用**: 是否翻转 Y 轴
-   **说明**:
    -   WebGL 坐标系 Y 轴向上
    -   图像坐标系 Y 轴向下
    -   通常需要翻转以匹配预期效果

## 尺寸参数

### 17. `level` (可选)

-   **类型**: `number`
-   **默认值**: `0`
-   **作用**: 指定要操作的 mipmap 级别，决定纹理数据上传到哪个细节层级
-   **详细说明**:
    -   **Mipmap 层级系统**:
        -   Level 0: 原始尺寸 (基础级别)
        -   Level 1: 尺寸减半 (宽度/2, 高度/2)
        -   Level 2: 尺寸再减半 (宽度/4, 高度/4)
        -   Level n: 尺寸为原始的 1/(2^n)
    -   **级别计算**: 最大级别 = floor(log2(max(width, height)))
    -   **实际示例** (512x512 纹理):
        ```
        Level 0: 512x512 (原始)    ████████
        Level 1: 256x256 (1/2)     ████
        Level 2: 128x128 (1/4)     ██
        Level 3: 64x64   (1/8)     █
        Level 4: 32x32   (1/16)    ▓
        Level 5: 16x16   (1/32)    ▒
        Level 6: 8x8     (1/64)    ░
        Level 7: 4x4     (1/128)   ·
        Level 8: 2x2     (1/256)   ·
        Level 9: 1x1     (1/512)   ·
        ```
-   **可能的值**:
    -   `0`: 基础级别 (最高分辨率)
        -   这是最常用的值
        -   包含完整的纹理细节
    -   `1-n`: 更高的 mipmap 级别
        -   分辨率逐级降低
        -   用于远距离渲染优化
-   **使用场景**:
    -   **普通纹理创建**: 始终使用 `level: 0`
    -   **手动 mipmap**: 为每个级别单独上传数据
    -   **纹理更新**: 更新特定级别的数据
    -   **压缩纹理**: 某些格式需要指定级别
-   **示例**:

    ```javascript
    // 基础级别 (最常用)
    const texture = new Texture(gl, {
        image: fullResImage,
        level: 0, // 默认值，通常省略
    });

    // 手动创建mipmap链 - 高级用法
    const manualMipmap = new Texture(gl, {
        width: 256,
        height: 256,
        generateMipmaps: false,
    });

    // 为每个级别单独上传数据
    for (let level = 0; level <= 8; level++) {
        const size = 256 >> level; // 256, 128, 64, 32, 16, 8, 4, 2, 1
        const levelTexture = new Texture(gl, {
            image: generateMipmapLevel(originalImage, level),
            level: level,
            width: size,
            height: size,
        });
    }

    // 或者更新现有纹理的特定级别
    texture.level = 2; // 设置为第2级 (64x64)
    texture.needsUpdate = true;
    ```

-   **实际应用场景**:
    -   **游戏开发**:
        -   远景物体使用高级别 (低分辨率)
        -   近景物体使用低级别 (高分辨率)
        -   根据 LOD (细节层次) 系统动态选择
    -   **移动设备优化**:
        -   低端设备强制使用较高级别
        -   减少内存占用和带宽消耗
    -   **压缩纹理**:
        -   DDS/KTX 格式包含预计算的 mipmap
        -   需要为每个级别指定正确的 level 值
-   **重要注意事项**:
    -   **99%的情况**: 保持默认值 `0`
    -   **手动 mipmap**: 只有在需要精确控制时使用
    -   **自动生成**: `generateMipmaps: true` 时此参数被忽略
    -   **级别验证**: 错误的级别值会导致 WebGL 错误
    -   **内存计算**: 完整 mipmap 链占用原始纹理 1.33 倍内存
-   **性能影响**:
    -   **Level 0**: 最高质量，最大内存占用，最高带宽需求
    -   **更高级别**: 质量降低，内存占用减少，渲染更快
    -   **GPU 优化**: 硬件会根据屏幕距离自动选择最佳级别
    -   **缓存友好**: 较小的 mipmap 级别有更好的缓存局部性

### 18. `width` (可选)

-   **类型**: `number`
-   **默认值**: `undefined`
-   **作用**: 纹理宽度
-   **用途**:
    -   渲染目标纹理
    -   数据纹理 (无图像源时)

### 19. `height` (可选)

-   **类型**: `number`
-   **默认值**: `width`
-   **作用**: 纹理高度
-   **说明**: 如果未指定，默认与 width 相同 (正方形纹理)

### 20. `length` (可选)

-   **类型**: `number`
-   **默认值**: `1`
-   **作用**: 3D 纹理的深度或纹理数组的层数
-   **用途**: 仅用于 3D 纹理和纹理数组

## 回调参数

### 21. `onUpdate` (可选)

-   **类型**: `Function`
-   **默认值**: `undefined`
-   **作用**: 纹理更新完成后的回调函数
-   **用途**:
    -   更新统计信息
    -   触发重新渲染
    -   记录日志等自定义逻辑

## 使用示例

```javascript
// 基本2D纹理
const texture2D = new Texture(gl, {
    image: imageElement,
    wrapS: gl.REPEAT,
    wrapT: gl.REPEAT,
    minFilter: gl.LINEAR_MIPMAP_LINEAR,
    magFilter: gl.LINEAR,
});

// 立方体贴图
const cubeTexture = new Texture(gl, {
    image: [px, nx, py, ny, pz, nz], // 6个面的图像
    target: gl.TEXTURE_CUBE_MAP,
});

// 渲染目标纹理
const renderTarget = new Texture(gl, {
    width: 512,
    height: 512,
    format: gl.RGBA,
    type: gl.UNSIGNED_BYTE,
});

// 数据纹理
const dataTexture = new Texture(gl, {
    image: new Float32Array(width * height * 4),
    width: width,
    height: height,
    format: gl.RGBA,
    type: gl.FLOAT,
});
```

## 参数快速对照表

| 参数               | 类型         | 默认值        | 主要用途        |
| ------------------ | ------------ | ------------- | --------------- |
| `gl`               | WebGLContext | 必需          | WebGL 上下文    |
| `image`            | 多种类型     | undefined     | 纹理数据源      |
| `target`           | number       | TEXTURE_2D    | 纹理类型        |
| `type`             | number       | UNSIGNED_BYTE | 数据类型        |
| `format`           | number       | RGBA          | 像素格式        |
| `internalFormat`   | number       | format 值     | GPU 存储格式    |
| `wrapS/T/R`        | number       | CLAMP_TO_EDGE | 包裹模式        |
| `generateMipmaps`  | boolean      | 自动判断      | 是否生成 mipmap |
| `minFilter`        | number       | 自动判断      | 缩小过滤器      |
| `magFilter`        | number       | LINEAR        | 放大过滤器      |
| `anisotropy`       | number       | 0             | 各向异性过滤    |
| `premultiplyAlpha` | boolean      | false         | 预乘 alpha      |
| `unpackAlignment`  | number       | 4             | 字节对齐        |
| `flipY`            | boolean      | 自动判断      | Y 轴翻转        |
| `level`            | number       | 0             | mipmap 级别     |
| `width/height`     | number       | undefined     | 纹理尺寸        |
| `length`           | number       | 1             | 3D 纹理深度     |
| `onUpdate`         | Function     | undefined     | 更新回调        |

## 高级使用示例

### 1. 像素艺术纹理

```javascript
const pixelTexture = new Texture(gl, {
    image: pixelArtImage,
    wrapS: gl.CLAMP_TO_EDGE,
    wrapT: gl.CLAMP_TO_EDGE,
    minFilter: gl.NEAREST,
    magFilter: gl.NEAREST,
    generateMipmaps: false,
});
```

### 2. HDR 环境贴图

```javascript
const hdrTexture = new Texture(gl, {
    image: hdrImageData,
    format: gl.RGB,
    internalFormat: gl.RGB16F,
    type: gl.HALF_FLOAT,
    minFilter: gl.LINEAR,
    magFilter: gl.LINEAR,
    wrapS: gl.CLAMP_TO_EDGE,
    wrapT: gl.CLAMP_TO_EDGE,
});
```

### 3. 深度纹理

```javascript
const depthTexture = new Texture(gl, {
    width: 1024,
    height: 1024,
    format: gl.DEPTH_COMPONENT,
    internalFormat: gl.DEPTH_COMPONENT24,
    type: gl.UNSIGNED_INT,
    minFilter: gl.NEAREST,
    magFilter: gl.NEAREST,
    wrapS: gl.CLAMP_TO_EDGE,
    wrapT: gl.CLAMP_TO_EDGE,
});
```

### 4. 法线贴图 (优化版)

```javascript
const normalTexture = new Texture(gl, {
    image: normalMapImage,
    format: gl.RG, // 只存储XY分量
    internalFormat: gl.RG8,
    type: gl.UNSIGNED_BYTE,
    minFilter: gl.LINEAR_MIPMAP_LINEAR,
    magFilter: gl.LINEAR,
    wrapS: gl.REPEAT,
    wrapT: gl.REPEAT,
    generateMipmaps: true,
});
```

# WebGL 纹理相关函数详解

## 目录

-   [createTexture()](#createtexture) - 创建纹理对象
-   [bindTexture()](#bindtexture) - 绑定纹理
-   [texImage2D()](#teximage2d) - 设置 2D 纹理图像
-   [texImage3D()](#teximage3d) - 设置 3D 纹理图像 (WebGL 2.0)
-   [texSubImage2D()](#texsubimage2d) - 更新 2D 纹理子图像
-   [texSubImage3D()](#texsubimage3d) - 更新 3D 纹理子图像 (WebGL 2.0)
-   [texStorage2D()](#texstorage2d) - 分配 2D 纹理存储 (WebGL 2.0)
-   [texStorage3D()](#texstorage3d) - 分配 3D 纹理存储 (WebGL 2.0)
-   [texParameteri()](#texparameteri) - 设置纹理整数参数
-   [texParameterf()](#texparameterf) - 设置纹理浮点参数
-   [generateMipmap()](#generatemipmap) - 生成多级渐远纹理
-   [activeTexture()](#activetexture) - 激活纹理单元
-   [copyTexImage2D()](#copyteximage2d) - 从帧缓冲复制到 2D 纹理
-   [copyTexSubImage2D()](#copytexsubimage2d) - 从帧缓冲复制到 2D 纹理子区域
-   [copyTexSubImage3D()](#copytexsubimage3d) - 从帧缓冲复制到 3D 纹理子区域 (WebGL 2.0)
-   [compressedTexImage2D()](#compressedteximage2d) - 设置压缩 2D 纹理
-   [compressedTexImage3D()](#compressedteximage3d) - 设置压缩 3D 纹理 (WebGL 2.0)
-   [compressedTexSubImage2D()](#compressedtexsubimage2d) - 更新压缩 2D 纹理子区域
-   [compressedTexSubImage3D()](#compressedtexsubimage3d) - 更新压缩 3D 纹理子区域 (WebGL 2.0)
-   [getTexParameter()](#gettexparameter) - 获取纹理参数
-   [isTexture()](#istexture) - 检查对象是否为纹理
-   [deleteTexture()](#deletetexture) - 删除纹理对象

---

## createTexture()

创建一个新的纹理对象。

### 语法

```javascript
WebGLTexture gl.createTexture()
```

### 参数

-   **无参数**

### 返回值

-   **类型**: `WebGLTexture | null`
-   **说明**: 返回新创建的纹理对象，如果创建失败则返回 `null`

### 详细说明

-   **作用**: 在 GPU 内存中分配一个新的纹理对象
-   **初始状态**: 创建的纹理对象是空的，需要后续调用其他函数来设置数据
-   **内存管理**: 创建的纹理会占用 GPU 内存，使用完毕后应调用 `deleteTexture()` 释放

### 使用示例

```javascript
// 创建纹理对象
const texture = gl.createTexture();
if (!texture) {
    console.error('Failed to create texture');
    return;
}

// 绑定纹理以进行后续操作
gl.bindTexture(gl.TEXTURE_2D, texture);
```

### 错误处理

-   **GL_OUT_OF_MEMORY**: GPU 内存不足时返回 `null`
-   **上下文丢失**: WebGL 上下文丢失时返回 `null`

---

## bindTexture()

将纹理对象绑定到指定的纹理目标。

### 语法

```javascript
void gl.bindTexture(target, texture);
```

### 参数

#### `target` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理绑定目标
-   **WebGL 1.0 可能的值**:
    -   `gl.TEXTURE_2D`: 2D 纹理目标
        -   用于普通的平面纹理
        -   最常用的纹理类型
    -   `gl.TEXTURE_CUBE_MAP`: 立方体贴图目标
        -   用于环境映射、反射效果
        -   由 6 个面组成的立方体纹理
-   **WebGL 2.0 额外的值**:
    -   `gl.TEXTURE_3D`: 3D 纹理目标
        -   用于体积渲染、3D 噪声
    -   `gl.TEXTURE_2D_ARRAY`: 2D 纹理数组目标
        -   多层 2D 纹理的集合

#### `texture` (可选)

-   **类型**: `WebGLTexture | null`
-   **作用**: 要绑定的纹理对象
-   **特殊值**:
    -   `null`: 解除当前绑定，恢复到默认状态
    -   有效的纹理对象: 绑定指定纹理

### 详细说明

-   **绑定状态**: WebGL 使用状态机，同一时间每个目标只能绑定一个纹理
-   **后续操作**: 绑定后的所有纹理操作都会作用于当前绑定的纹理
-   **性能考虑**: 频繁切换纹理绑定会影响性能，应尽量减少绑定切换

### 使用示例

```javascript
// 创建并绑定 2D 纹理
const texture2D = gl.createTexture();
gl.bindTexture(gl.TEXTURE_2D, texture2D);

// 创建并绑定立方体贴图
const cubeTexture = gl.createTexture();
gl.bindTexture(gl.TEXTURE_CUBE_MAP, cubeTexture);

// 解除绑定
gl.bindTexture(gl.TEXTURE_2D, null);
```

### 错误处理

-   **GL_INVALID_ENUM**: `target` 不是有效的纹理目标
-   **GL_INVALID_OPERATION**: `texture` 不是有效的纹理对象或已被删除

---

## texImage2D()

为 2D 纹理或立方体贴图的一个面设置图像数据。

### 语法

```javascript
// 从图像源设置纹理
void gl.texImage2D(target, level, internalformat, format, type, source);

// 从像素数据设置纹理
void gl.texImage2D(target, level, internalformat, width, height, border, format, type, pixels);
```

### 参数

#### `target` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理目标
-   **2D 纹理值**:
    -   `gl.TEXTURE_2D`: 标准 2D 纹理
-   **立方体贴图值**:
    -   `gl.TEXTURE_CUBE_MAP_POSITIVE_X`: +X 面 (右)
    -   `gl.TEXTURE_CUBE_MAP_NEGATIVE_X`: -X 面 (左)
    -   `gl.TEXTURE_CUBE_MAP_POSITIVE_Y`: +Y 面 (上)
    -   `gl.TEXTURE_CUBE_MAP_NEGATIVE_Y`: -Y 面 (下)
    -   `gl.TEXTURE_CUBE_MAP_POSITIVE_Z`: +Z 面 (前)
    -   `gl.TEXTURE_CUBE_MAP_NEGATIVE_Z`: -Z 面 (后)

#### `level` (必需)

-   **类型**: `GLint`
-   **作用**: 指定 mipmap 级别
-   **范围**: 0 到 log2(max(width, height))
-   **常用值**:
    -   `0`: 基础级别 (原始分辨率)
    -   `1-n`: 更高级别 (分辨率递减)

#### `internalformat` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理的内部存储格式
-   **WebGL 1.0 值**:
    -   `gl.ALPHA`: 仅 alpha 通道
    -   `gl.LUMINANCE`: 灰度
    -   `gl.LUMINANCE_ALPHA`: 灰度 + alpha
    -   `gl.RGB`: 红绿蓝三通道
    -   `gl.RGBA`: 红绿蓝 + alpha 四通道
-   **WebGL 2.0 额外值**:
    -   `gl.R8`, `gl.RG8`, `gl.RGB8`, `gl.RGBA8`: 8 位整数格式
    -   `gl.R16F`, `gl.RG16F`, `gl.RGB16F`, `gl.RGBA16F`: 16 位浮点格式
    -   `gl.R32F`, `gl.RG32F`, `gl.RGB32F`, `gl.RGBA32F`: 32 位浮点格式

#### `width` (必需，像素数据版本)

-   **类型**: `GLsizei`
-   **作用**: 纹理宽度（像素）
-   **限制**: 必须为 2 的幂（WebGL 1.0 中使用 mipmap 时）

#### `height` (必需，像素数据版本)

-   **类型**: `GLsizei`
-   **作用**: 纹理高度（像素）
-   **限制**: 必须为 2 的幂（WebGL 1.0 中使用 mipmap 时）

#### `border` (必需，像素数据版本)

-   **类型**: `GLint`
-   **作用**: 边框宽度
-   **值**: 必须为 `0`（WebGL 不支持纹理边框）

#### `format` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定像素数据的格式
-   **必须与 internalformat 兼容**
-   **WebGL 1.0 值**:
    -   `gl.ALPHA`: 仅 alpha 数据
    -   `gl.LUMINANCE`: 灰度数据
    -   `gl.LUMINANCE_ALPHA`: 灰度 + alpha 数据
    -   `gl.RGB`: RGB 数据
    -   `gl.RGBA`: RGBA 数据
-   **WebGL 2.0 额外值**:
    -   `gl.RED`: 单红色通道
    -   `gl.RG`: 红绿双通道
    -   `gl.RGB_INTEGER`: 整数 RGB
    -   `gl.RGBA_INTEGER`: 整数 RGBA

#### `type` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定像素数据的数据类型
-   **常用值**:
    -   `gl.UNSIGNED_BYTE`: 8 位无符号整数 (0-255)
    -   `gl.UNSIGNED_SHORT_5_6_5`: 16 位 RGB (5-6-5 位分配)
    -   `gl.UNSIGNED_SHORT_4_4_4_4`: 16 位 RGBA (4-4-4-4 位分配)
    -   `gl.UNSIGNED_SHORT_5_5_5_1`: 16 位 RGBA (5-5-5-1 位分配)
    -   `gl.FLOAT`: 32 位浮点数（需要扩展支持）
-   **WebGL 2.0 额外值**:
    -   `gl.HALF_FLOAT`: 16 位浮点数
    -   `gl.UNSIGNED_SHORT`: 16 位无符号整数
    -   `gl.UNSIGNED_INT`: 32 位无符号整数

#### `source` (必需，图像源版本)

-   **类型**: `TexImageSource`
-   **可能的值**:
    -   `HTMLImageElement`: `<img>` 元素
    -   `HTMLCanvasElement`: `<canvas>` 元素
    -   `HTMLVideoElement`: `<video>` 元素
    -   `ImageBitmap`: 图像位图对象
    -   `ImageData`: 图像数据对象

#### `pixels` (可选，像素数据版本)

-   **类型**: `ArrayBufferView | null`
-   **作用**: 包含像素数据的类型化数组
-   **可能的类型**:
    -   `Uint8Array`: 对应 `UNSIGNED_BYTE`
    -   `Uint16Array`: 对应 `UNSIGNED_SHORT`
    -   `Float32Array`: 对应 `FLOAT`
-   **特殊值**: `null` 表示分配存储空间但不填充数据

### 使用示例

#### 从图像元素创建纹理

```javascript
const image = new Image();
image.onload = function () {
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(
        gl.TEXTURE_2D, // target
        0, // level
        gl.RGBA, // internalformat
        gl.RGBA, // format
        gl.UNSIGNED_BYTE, // type
        image // source
    );
    gl.generateMipmap(gl.TEXTURE_2D);
};
image.src = 'texture.jpg';
```

#### 从像素数据创建纹理

```javascript
const width = 256;
const height = 256;
const pixels = new Uint8Array(width * height * 4); // RGBA

// 填充像素数据（例如：红色渐变）
for (let i = 0; i < pixels.length; i += 4) {
    pixels[i] = (i / 4) % width; // Red
    pixels[i + 1] = 0; // Green
    pixels[i + 2] = 0; // Blue
    pixels[i + 3] = 255; // Alpha
}

gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texImage2D(
    gl.TEXTURE_2D, // target
    0, // level
    gl.RGBA, // internalformat
    width, // width
    height, // height
    0, // border
    gl.RGBA, // format
    gl.UNSIGNED_BYTE, // type
    pixels // pixels
);
```

#### 创建立方体贴图

```javascript
const faces = [
    { target: gl.TEXTURE_CUBE_MAP_POSITIVE_X, image: rightImage },
    { target: gl.TEXTURE_CUBE_MAP_NEGATIVE_X, image: leftImage },
    { target: gl.TEXTURE_CUBE_MAP_POSITIVE_Y, image: topImage },
    { target: gl.TEXTURE_CUBE_MAP_NEGATIVE_Y, image: bottomImage },
    { target: gl.TEXTURE_CUBE_MAP_POSITIVE_Z, image: frontImage },
    { target: gl.TEXTURE_CUBE_MAP_NEGATIVE_Z, image: backImage },
];

gl.bindTexture(gl.TEXTURE_CUBE_MAP, cubeTexture);
faces.forEach((face) => {
    gl.texImage2D(
        face.target, // target
        0, // level
        gl.RGBA, // internalformat
        gl.RGBA, // format
        gl.UNSIGNED_BYTE, // type
        face.image // source
    );
});
```

### 错误处理

-   **GL_INVALID_ENUM**: 无效的 target、format 或 type
-   **GL_INVALID_VALUE**: 无效的 level、width、height 或 border
-   **GL_INVALID_OPERATION**: format 和 type 组合无效
-   **GL_OUT_OF_MEMORY**: GPU 内存不足

---

## texImage3D()

为 3D 纹理或 2D 纹理数组设置图像数据（仅 WebGL 2.0）。

### 语法

```javascript
// 从图像源设置纹理
void gl.texImage3D(target, level, internalformat, width, height, depth, border, format, type, source);

// 从像素数据设置纹理
void gl.texImage3D(target, level, internalformat, width, height, depth, border, format, type, pixels);
```

### 参数

#### `target` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定 3D 纹理目标
-   **可能的值**:
    -   `gl.TEXTURE_3D`: 3D 纹理
        -   用于体积渲染、3D 噪声
        -   真正的三维纹理数据
    -   `gl.TEXTURE_2D_ARRAY`: 2D 纹理数组
        -   多层 2D 纹理的集合
        -   每层具有相同的尺寸和格式

#### `level` (必需)

-   **类型**: `GLint`
-   **作用**: 指定 mipmap 级别
-   **范围**: 0 到 log2(max(width, height, depth))

#### `internalformat` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理的内部存储格式
-   **支持的格式**: 与 texImage2D() 相同，但支持更多 3D 特定格式

#### `width`, `height`, `depth` (必需)

-   **类型**: `GLsizei`
-   **作用**: 纹理的三维尺寸
-   **说明**:
    -   `width`: 纹理宽度
    -   `height`: 纹理高度
    -   `depth`: 纹理深度（3D 纹理）或层数（2D 数组）

#### `border` (必需)

-   **类型**: `GLint`
-   **值**: 必须为 `0`

#### `format`, `type` (必需)

-   **说明**: 与 texImage2D() 相同

#### `source` / `pixels`

-   **说明**: 与 texImage2D() 相同，但数据量为 width × height × depth

### 使用示例

```javascript
// 创建 3D 纹理
const width = 64,
    height = 64,
    depth = 64;
const data = new Uint8Array(width * height * depth * 4);

// 填充 3D 数据
for (let z = 0; z < depth; z++) {
    for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
            const index = (z * height * width + y * width + x) * 4;
            data[index] = x * 4; // Red
            data[index + 1] = y * 4; // Green
            data[index + 2] = z * 4; // Blue
            data[index + 3] = 255; // Alpha
        }
    }
}

gl.bindTexture(gl.TEXTURE_3D, texture3D);
gl.texImage3D(
    gl.TEXTURE_3D, // target
    0, // level
    gl.RGBA8, // internalformat
    width, // width
    height, // height
    depth, // depth
    0, // border
    gl.RGBA, // format
    gl.UNSIGNED_BYTE, // type
    data // pixels
);
```

---

## texSubImage2D()

更新 2D 纹理或立方体贴图的一个子区域。

### 语法

```javascript
// 从图像源更新纹理
void gl.texSubImage2D(target, level, xoffset, yoffset, format, type, source);

// 从像素数据更新纹理
void gl.texSubImage2D(target, level, xoffset, yoffset, width, height, format, type, pixels);
```

### 参数

#### `target` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理目标
-   **值**: 与 texImage2D() 相同

#### `level` (必需)

-   **类型**: `GLint`
-   **作用**: 指定要更新的 mipmap 级别

#### `xoffset`, `yoffset` (必需)

-   **类型**: `GLint`
-   **作用**: 指定更新区域的左下角偏移量
-   **说明**:
    -   `xoffset`: 水平偏移（像素）
    -   `yoffset`: 垂直偏移（像素）
    -   偏移量必须在纹理边界内

#### `width`, `height` (必需，像素数据版本)

-   **类型**: `GLsizei`
-   **作用**: 指定更新区域的尺寸
-   **限制**: 更新区域不能超出纹理边界

#### `format`, `type` (必需)

-   **说明**: 与 texImage2D() 相同
-   **注意**: 必须与原纹理的格式兼容

#### `source` / `pixels`

-   **说明**: 与 texImage2D() 相同

### 使用示例

#### 更新纹理的一部分

```javascript
// 假设已有一个 512x512 的纹理
const updateData = new Uint8Array(128 * 128 * 4); // 128x128 的更新数据

// 填充更新数据
for (let i = 0; i < updateData.length; i += 4) {
    updateData[i] = 255; // Red
    updateData[i + 1] = 0; // Green
    updateData[i + 2] = 0; // Blue
    updateData[i + 3] = 255; // Alpha
}

gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texSubImage2D(
    gl.TEXTURE_2D, // target
    0, // level
    192, // xoffset (从 x=192 开始)
    192, // yoffset (从 y=192 开始)
    128, // width (更新 128 像素宽)
    128, // height (更新 128 像素高)
    gl.RGBA, // format
    gl.UNSIGNED_BYTE, // type
    updateData // pixels
);
```

#### 从画布更新纹理

```javascript
// 假设有一个画布元素
const canvas = document.getElementById('dynamicCanvas');
const ctx = canvas.getContext('2d');

// 在画布上绘制一些内容
ctx.fillStyle = 'blue';
ctx.fillRect(0, 0, 100, 100);

// 更新纹理的一部分
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texSubImage2D(
    gl.TEXTURE_2D, // target
    0, // level
    50, // xoffset
    50, // yoffset
    gl.RGBA, // format
    gl.UNSIGNED_BYTE, // type
    canvas // source
);
```

### 性能优势

-   **部分更新**: 只更新需要改变的区域，而不是整个纹理
-   **减少带宽**: 传输的数据量更少
-   **保持状态**: 不影响纹理的其他参数设置
-   **动态内容**: 适合频繁更新的动态纹理

### 错误处理

-   **GL_INVALID_ENUM**: 无效的 target、format 或 type
-   **GL_INVALID_VALUE**: 偏移量或尺寸超出纹理边界
-   **GL_INVALID_OPERATION**: 格式不兼容或纹理未初始化

---

## texParameteri()

设置纹理的整数参数。

### 语法

```javascript
void gl.texParameteri(target, pname, param);
```

### 参数

#### `target` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理目标
-   **可能的值**:
    -   `gl.TEXTURE_2D`: 2D 纹理
    -   `gl.TEXTURE_CUBE_MAP`: 立方体贴图
    -   `gl.TEXTURE_3D`: 3D 纹理 (WebGL 2.0)
    -   `gl.TEXTURE_2D_ARRAY`: 2D 纹理数组 (WebGL 2.0)

#### `pname` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定要设置的参数名称
-   **可能的值**:
    -   **包裹模式**:
        -   `gl.TEXTURE_WRAP_S`: S 方向包裹模式
        -   `gl.TEXTURE_WRAP_T`: T 方向包裹模式
        -   `gl.TEXTURE_WRAP_R`: R 方向包裹模式 (3D 纹理)
    -   **过滤模式**:
        -   `gl.TEXTURE_MIN_FILTER`: 缩小过滤器
        -   `gl.TEXTURE_MAG_FILTER`: 放大过滤器
    -   **WebGL 2.0 额外参数**:
        -   `gl.TEXTURE_BASE_LEVEL`: 基础 mipmap 级别
        -   `gl.TEXTURE_MAX_LEVEL`: 最大 mipmap 级别
        -   `gl.TEXTURE_COMPARE_MODE`: 比较模式
        -   `gl.TEXTURE_COMPARE_FUNC`: 比较函数

#### `param` (必需)

-   **类型**: `GLint`
-   **作用**: 参数值
-   **包裹模式值**:
    -   `gl.CLAMP_TO_EDGE`: 边缘拉伸
    -   `gl.REPEAT`: 重复平铺
    -   `gl.MIRRORED_REPEAT`: 镜像重复 (WebGL 2.0)
-   **过滤模式值**:
    -   **不使用 mipmap**:
        -   `gl.NEAREST`: 最近邻过滤
        -   `gl.LINEAR`: 线性过滤
    -   **使用 mipmap** (仅 MIN_FILTER):
        -   `gl.NEAREST_MIPMAP_NEAREST`
        -   `gl.LINEAR_MIPMAP_NEAREST`
        -   `gl.NEAREST_MIPMAP_LINEAR`
        -   `gl.LINEAR_MIPMAP_LINEAR`

### 使用示例

```javascript
// 设置纹理包裹模式
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.REPEAT);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.REPEAT);

// 设置过滤模式
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR_MIPMAP_LINEAR);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

// WebGL 2.0: 设置 mipmap 级别范围
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_BASE_LEVEL, 0);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAX_LEVEL, 4);
```

---

## texParameterf()

设置纹理的浮点参数。

### 语法

```javascript
void gl.texParameterf(target, pname, param);
```

### 参数

#### `target` (必需)

-   **说明**: 与 texParameteri() 相同

#### `pname` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定要设置的参数名称
-   **浮点参数**:
    -   `gl.TEXTURE_MAX_ANISOTROPY_EXT`: 各向异性过滤级别 (需要扩展)
    -   **WebGL 2.0 额外参数**:
        -   `gl.TEXTURE_MIN_LOD`: 最小 LOD 值
        -   `gl.TEXTURE_MAX_LOD`: 最大 LOD 值

#### `param` (必需)

-   **类型**: `GLfloat`
-   **作用**: 浮点参数值
-   **各向异性过滤**: 1.0 到硬件支持的最大值
-   **LOD 值**: 可以是任意浮点数

### 使用示例

```javascript
// 设置各向异性过滤 (需要扩展支持)
const ext = gl.getExtension('EXT_texture_filter_anisotropic');
if (ext) {
    const maxAnisotropy = gl.getParameter(ext.MAX_TEXTURE_MAX_ANISOTROPY_EXT);
    gl.texParameterf(gl.TEXTURE_2D, ext.TEXTURE_MAX_ANISOTROPY_EXT, maxAnisotropy);
}

// WebGL 2.0: 设置 LOD 范围
gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_MIN_LOD, -1000);
gl.texParameterf(gl.TEXTURE_2D, gl.TEXTURE_MAX_LOD, 1000);
```

---

## generateMipmap()

为当前绑定的纹理生成 mipmap 链。

### 语法

```javascript
void gl.generateMipmap(target);
```

### 参数

#### `target` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理目标
-   **可能的值**:
    -   `gl.TEXTURE_2D`: 2D 纹理
    -   `gl.TEXTURE_CUBE_MAP`: 立方体贴图
    -   `gl.TEXTURE_3D`: 3D 纹理 (WebGL 2.0)
    -   `gl.TEXTURE_2D_ARRAY`: 2D 纹理数组 (WebGL 2.0)

### 详细说明

-   **作用**: 自动生成从基础级别 (level 0) 开始的完整 mipmap 链
-   **算法**: 使用盒式过滤器对上一级别进行下采样
-   **级别数**: 生成到 1x1 像素为止
-   **WebGL 1.0 限制**: 纹理尺寸必须是 2 的幂
-   **WebGL 2.0**: 支持任意尺寸的纹理

### Mipmap 级别计算

```javascript
// 最大 mipmap 级别计算
const maxLevel = Math.floor(Math.log2(Math.max(width, height)));

// 例如 512x512 纹理:
// Level 0: 512x512
// Level 1: 256x256
// Level 2: 128x128
// Level 3: 64x64
// Level 4: 32x32
// Level 5: 16x16
// Level 6: 8x8
// Level 7: 4x4
// Level 8: 2x2
// Level 9: 1x1
```

### 使用示例

```javascript
// 基本用法
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);
gl.generateMipmap(gl.TEXTURE_2D);

// 立方体贴图
gl.bindTexture(gl.TEXTURE_CUBE_MAP, cubeTexture);
// ... 设置 6 个面的纹理数据
gl.generateMipmap(gl.TEXTURE_CUBE_MAP);

// 检查是否可以生成 mipmap
function canGenerateMipmap(width, height) {
    // WebGL 1.0: 必须是 2 的幂
    return (width & (width - 1)) === 0 && (height & (height - 1)) === 0;
}

if (canGenerateMipmap(image.width, image.height)) {
    gl.generateMipmap(gl.TEXTURE_2D);
} else {
    // 设置不使用 mipmap 的过滤器
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
}
```

### 性能考虑

-   **内存占用**: 完整 mipmap 链占用原纹理 1.33 倍内存
-   **生成时间**: 大纹理生成 mipmap 可能耗时较长
-   **质量**: 自动生成的质量可能不如手动优化的 mipmap
-   **时机**: 建议在纹理数据设置完成后立即生成

### 错误处理

-   **GL_INVALID_ENUM**: 无效的 target
-   **GL_INVALID_OPERATION**: 纹理未完整定义或尺寸不符合要求
-   **WebGL 1.0**: 非 2 的幂纹理会产生错误

---

## activeTexture()

激活指定的纹理单元。

### 语法

```javascript
void gl.activeTexture(texture);
```

### 参数

#### `texture` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定要激活的纹理单元
-   **可能的值**:
    -   `gl.TEXTURE0` 到 `gl.TEXTURE31`: 纹理单元 0-31
    -   **实际可用数量**: 由硬件决定，可通过 `gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS)` 查询
    -   **默认值**: `gl.TEXTURE0`

### 详细说明

-   **纹理单元**: WebGL 支持多个纹理单元，每个单元可以绑定不同的纹理
-   **着色器采样**: 着色器中的 sampler uniform 对应特定的纹理单元
-   **状态管理**: 每个纹理单元维护独立的绑定状态
-   **性能**: 切换纹理单元比重新绑定纹理更高效

### 纹理单元编号

```javascript
// 纹理单元常量
gl.TEXTURE0; // 纹理单元 0
gl.TEXTURE1; // 纹理单元 1
gl.TEXTURE2; // 纹理单元 2
// ...
gl.TEXTURE31; // 纹理单元 31

// 也可以通过计算获得
const textureUnit = gl.TEXTURE0 + unitIndex; // unitIndex: 0-31
```

### 使用示例

#### 基本用法

```javascript
// 激活纹理单元 0 并绑定纹理
gl.activeTexture(gl.TEXTURE0);
gl.bindTexture(gl.TEXTURE_2D, diffuseTexture);

// 激活纹理单元 1 并绑定另一个纹理
gl.activeTexture(gl.TEXTURE1);
gl.bindTexture(gl.TEXTURE_2D, normalTexture);

// 在着色器中设置对应的 uniform
gl.uniform1i(gl.getUniformLocation(program, 'u_diffuse'), 0); // 对应 TEXTURE0
gl.uniform1i(gl.getUniformLocation(program, 'u_normal'), 1); // 对应 TEXTURE1
```

#### 多纹理渲染

```javascript
// 设置多个纹理
const textures = [diffuseTexture, normalTexture, specularTexture];
const uniformNames = ['u_diffuse', 'u_normal', 'u_specular'];

textures.forEach((texture, index) => {
    gl.activeTexture(gl.TEXTURE0 + index);
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.uniform1i(gl.getUniformLocation(program, uniformNames[index]), index);
});
```

### 错误处理

-   **GL_INVALID_ENUM**: texture 不是有效的纹理单元常量

---

## getTexParameter()

获取纹理参数的当前值。

### 语法

```javascript
any gl.getTexParameter(target, pname)
```

### 参数

#### `target` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定纹理目标
-   **值**: 与其他纹理函数相同

#### `pname` (必需)

-   **类型**: `GLenum`
-   **作用**: 指定要查询的参数名称
-   **可能的值**:
    -   `gl.TEXTURE_WRAP_S`: S 方向包裹模式
    -   `gl.TEXTURE_WRAP_T`: T 方向包裹模式
    -   `gl.TEXTURE_WRAP_R`: R 方向包裹模式 (3D 纹理)
    -   `gl.TEXTURE_MIN_FILTER`: 缩小过滤器
    -   `gl.TEXTURE_MAG_FILTER`: 放大过滤器
    -   **WebGL 2.0 额外参数**:
        -   `gl.TEXTURE_BASE_LEVEL`: 基础 mipmap 级别
        -   `gl.TEXTURE_MAX_LEVEL`: 最大 mipmap 级别
        -   `gl.TEXTURE_COMPARE_MODE`: 比较模式
        -   `gl.TEXTURE_COMPARE_FUNC`: 比较函数
        -   `gl.TEXTURE_MIN_LOD`: 最小 LOD 值
        -   `gl.TEXTURE_MAX_LOD`: 最大 LOD 值

### 返回值

-   **类型**: 根据参数类型返回 `GLint` 或 `GLfloat`
-   **包裹模式**: 返回整数常量
-   **过滤模式**: 返回整数常量
-   **LOD 值**: 返回浮点数

### 使用示例

```javascript
gl.bindTexture(gl.TEXTURE_2D, texture);

// 查询包裹模式
const wrapS = gl.getTexParameter(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S);
const wrapT = gl.getTexParameter(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T);

// 查询过滤模式
const minFilter = gl.getTexParameter(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER);
const magFilter = gl.getTexParameter(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER);

console.log('Wrap S:', wrapS === gl.REPEAT ? 'REPEAT' : 'CLAMP_TO_EDGE');
console.log('Min Filter:', minFilter);
```

---

## isTexture()

检查给定对象是否为有效的纹理对象。

### 语法

```javascript
GLboolean gl.isTexture(texture)
```

### 参数

#### `texture` (必需)

-   **类型**: `WebGLTexture | null`
-   **作用**: 要检查的对象

### 返回值

-   **类型**: `boolean`
-   **true**: 对象是有效的纹理对象且已被 `bindTexture()` 绑定过
-   **false**: 对象不是纹理对象、为 null、或已被删除

### 详细说明

-   **绑定要求**: 纹理对象必须至少被绑定过一次才会返回 true
-   **删除检测**: 已删除的纹理对象会返回 false
-   **null 检查**: 传入 null 会返回 false

### 使用示例

```javascript
const texture = gl.createTexture();
console.log(gl.isTexture(texture)); // false (未绑定)

gl.bindTexture(gl.TEXTURE_2D, texture);
console.log(gl.isTexture(texture)); // true (已绑定)

gl.deleteTexture(texture);
console.log(gl.isTexture(texture)); // false (已删除)

console.log(gl.isTexture(null)); // false
```

---

## deleteTexture()

删除纹理对象并释放其占用的 GPU 内存。

### 语法

```javascript
void gl.deleteTexture(texture);
```

### 参数

#### `texture` (必需)

-   **类型**: `WebGLTexture | null`
-   **作用**: 要删除的纹理对象
-   **特殊值**: 传入 null 不会产生错误，但也不会执行任何操作

### 详细说明

-   **内存释放**: 立即释放纹理占用的 GPU 内存
-   **绑定状态**: 如果纹理当前被绑定，绑定状态会被重置为默认值
-   **引用失效**: 删除后的纹理对象引用变为无效
-   **重复删除**: 重复删除同一纹理不会产生错误

### 使用示例

#### 基本用法

```javascript
// 创建和使用纹理
const texture = gl.createTexture();
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

// 使用完毕后删除
gl.deleteTexture(texture);
```

#### 批量删除

```javascript
const textures = [texture1, texture2, texture3];

// 删除所有纹理
textures.forEach((texture) => {
    if (gl.isTexture(texture)) {
        gl.deleteTexture(texture);
    }
});

// 清空数组引用
textures.length = 0;
```

#### 内存管理最佳实践

```javascript
class TextureManager {
    constructor(gl) {
        this.gl = gl;
        this.textures = new Set();
    }

    createTexture() {
        const texture = this.gl.createTexture();
        this.textures.add(texture);
        return texture;
    }

    deleteTexture(texture) {
        if (this.textures.has(texture)) {
            this.gl.deleteTexture(texture);
            this.textures.delete(texture);
        }
    }

    cleanup() {
        // 清理所有纹理
        this.textures.forEach((texture) => {
            this.gl.deleteTexture(texture);
        });
        this.textures.clear();
    }
}
```

### 性能考虑

-   **及时删除**: 不再使用的纹理应及时删除以释放内存
-   **批量操作**: 避免频繁创建和删除纹理
-   **内存监控**: 大型应用应监控纹理内存使用情况

### 错误处理

-   **无错误**: 此函数不会产生 WebGL 错误
-   **null 安全**: 传入 null 是安全的

---

## 函数使用流程总结

### 典型的纹理创建和使用流程

```javascript
// 1. 创建纹理对象
const texture = gl.createTexture();

// 2. 激活纹理单元
gl.activeTexture(gl.TEXTURE0);

// 3. 绑定纹理
gl.bindTexture(gl.TEXTURE_2D, texture);

// 4. 设置纹理参数
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.REPEAT);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.REPEAT);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR_MIPMAP_LINEAR);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

// 5. 上传纹理数据
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

// 6. 生成 mipmap
gl.generateMipmap(gl.TEXTURE_2D);

// 7. 在着色器中使用
gl.uniform1i(gl.getUniformLocation(program, 'u_texture'), 0);

// 8. 渲染...

// 9. 清理资源
gl.deleteTexture(texture);
```

### 动态纹理更新流程

```javascript
// 初始化
gl.bindTexture(gl.TEXTURE_2D, texture);
gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);

// 动态更新
function updateTexture(x, y, width, height, data) {
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texSubImage2D(gl.TEXTURE_2D, 0, x, y, width, height, gl.RGBA, gl.UNSIGNED_BYTE, data);
}
```

---

## WebGL 纹理函数快速参考表

| 函数名                      | 作用                     | 主要参数                                                      | WebGL 版本 | 使用频率   |
| --------------------------- | ------------------------ | ------------------------------------------------------------- | ---------- | ---------- |
| `createTexture()`           | 创建纹理对象             | 无                                                            | 1.0        | ⭐⭐⭐⭐⭐ |
| `bindTexture()`             | 绑定纹理到目标           | target, texture                                               | 1.0        | ⭐⭐⭐⭐⭐ |
| `texImage2D()`              | 设置 2D 纹理数据         | target, level, format, type, source                           | 1.0        | ⭐⭐⭐⭐⭐ |
| `texImage3D()`              | 设置 3D 纹理数据         | target, level, format, width, height, depth                   | 2.0        | ⭐⭐⭐     |
| `texSubImage2D()`           | 更新 2D 纹理子区域       | target, level, x, y, format, type, source                     | 1.0        | ⭐⭐⭐⭐   |
| `texSubImage3D()`           | 更新 3D 纹理子区域       | target, level, x, y, z, format, type, source                  | 2.0        | ⭐⭐       |
| `texStorage2D()`            | 预分配 2D 纹理存储       | target, levels, format, width, height                         | 2.0        | ⭐⭐⭐     |
| `texStorage3D()`            | 预分配 3D 纹理存储       | target, levels, format, width, height, depth                  | 2.0        | ⭐⭐       |
| `texParameteri()`           | 设置整数纹理参数         | target, pname, param                                          | 1.0        | ⭐⭐⭐⭐⭐ |
| `texParameterf()`           | 设置浮点纹理参数         | target, pname, param                                          | 1.0        | ⭐⭐⭐     |
| `generateMipmap()`          | 生成 mipmap 链           | target                                                        | 1.0        | ⭐⭐⭐⭐   |
| `activeTexture()`           | 激活纹理单元             | texture                                                       | 1.0        | ⭐⭐⭐⭐   |
| `copyTexImage2D()`          | 从帧缓冲复制到纹理       | target, level, format, x, y, width, height                    | 1.0        | ⭐⭐       |
| `copyTexSubImage2D()`       | 从帧缓冲复制到纹理子区域 | target, level, xoffset, yoffset, x, y, width, height          | 1.0        | ⭐⭐       |
| `copyTexSubImage3D()`       | 从帧缓冲复制到 3D 纹理   | target, level, xoffset, yoffset, zoffset, x, y, width, height | 2.0        | ⭐         |
| `compressedTexImage2D()`    | 设置压缩 2D 纹理         | target, level, format, width, height, data                    | 1.0        | ⭐⭐       |
| `compressedTexImage3D()`    | 设置压缩 3D 纹理         | target, level, format, width, height, depth, data             | 2.0        | ⭐         |
| `compressedTexSubImage2D()` | 更新压缩 2D 纹理子区域   | target, level, x, y, width, height, format, data              | 1.0        | ⭐⭐       |
| `compressedTexSubImage3D()` | 更新压缩 3D 纹理子区域   | target, level, x, y, z, width, height, depth, format, data    | 2.0        | ⭐         |
| `getTexParameter()`         | 获取纹理参数             | target, pname                                                 | 1.0        | ⭐⭐       |
| `isTexture()`               | 检查是否为纹理对象       | texture                                                       | 1.0        | ⭐⭐       |
| `deleteTexture()`           | 删除纹理对象             | texture                                                       | 1.0        | ⭐⭐⭐⭐   |

## 常用参数值速查

### 纹理目标 (target)

```javascript
// WebGL 1.0
gl.TEXTURE_2D; // 2D纹理
gl.TEXTURE_CUBE_MAP; // 立方体贴图

// 立方体贴图面
gl.TEXTURE_CUBE_MAP_POSITIVE_X; // +X面 (右)
gl.TEXTURE_CUBE_MAP_NEGATIVE_X; // -X面 (左)
gl.TEXTURE_CUBE_MAP_POSITIVE_Y; // +Y面 (上)
gl.TEXTURE_CUBE_MAP_NEGATIVE_Y; // -Y面 (下)
gl.TEXTURE_CUBE_MAP_POSITIVE_Z; // +Z面 (前)
gl.TEXTURE_CUBE_MAP_NEGATIVE_Z; // -Z面 (后)

// WebGL 2.0
gl.TEXTURE_3D; // 3D纹理
gl.TEXTURE_2D_ARRAY; // 2D纹理数组
```

### 内部格式 (internalFormat)

```javascript
// WebGL 1.0
gl.ALPHA; // 仅alpha通道
gl.LUMINANCE; // 灰度
gl.LUMINANCE_ALPHA; // 灰度+alpha
gl.RGB; // RGB三通道
gl.RGBA; // RGBA四通道

// WebGL 2.0 - 8位整数
gl.R8, gl.RG8, gl.RGB8, gl.RGBA8;

// WebGL 2.0 - 16位浮点
gl.R16F, gl.RG16F, gl.RGB16F, gl.RGBA16F;

// WebGL 2.0 - 32位浮点
gl.R32F, gl.RG32F, gl.RGB32F, gl.RGBA32F;
```

### 数据类型 (type)

```javascript
// 整数类型
gl.UNSIGNED_BYTE; // 8位无符号整数 (0-255)
gl.UNSIGNED_SHORT; // 16位无符号整数
gl.UNSIGNED_INT; // 32位无符号整数
gl.BYTE; // 8位有符号整数
gl.SHORT; // 16位有符号整数
gl.INT; // 32位有符号整数

// 浮点类型
gl.FLOAT; // 32位浮点数
gl.HALF_FLOAT; // 16位浮点数 (WebGL 2.0)

// 打包格式
gl.UNSIGNED_SHORT_5_6_5; // 16位RGB (5-6-5)
gl.UNSIGNED_SHORT_4_4_4_4; // 16位RGBA (4-4-4-4)
gl.UNSIGNED_SHORT_5_5_5_1; // 16位RGBA (5-5-5-1)
```

### 包裹模式 (wrap)

```javascript
gl.CLAMP_TO_EDGE; // 边缘拉伸 (默认)
gl.REPEAT; // 重复平铺
gl.MIRRORED_REPEAT; // 镜像重复 (WebGL 2.0)
```

### 过滤模式 (filter)

```javascript
// 不使用mipmap
gl.NEAREST; // 最近邻过滤
gl.LINEAR; // 线性过滤

// 使用mipmap (仅MIN_FILTER)
gl.NEAREST_MIPMAP_NEAREST; // 最近mipmap + 最近邻
gl.LINEAR_MIPMAP_NEAREST; // 最近mipmap + 线性
gl.NEAREST_MIPMAP_LINEAR; // mipmap间线性 + 最近邻
gl.LINEAR_MIPMAP_LINEAR; // 三线性过滤 (最高质量)
```

### 纹理单元

```javascript
gl.TEXTURE0; // 纹理单元0 (默认)
gl.TEXTURE1; // 纹理单元1
// ...
gl.TEXTURE31; // 纹理单元31

// 动态计算
gl.TEXTURE0 + index; // index: 0-31
```

## 最佳实践总结

### 1. 纹理创建最佳实践

```javascript
// ✅ 推荐的纹理创建流程
function createTexture(gl, image) {
    const texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, texture);

    // 设置参数 (在上传数据前)
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

    // 上传数据
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

    return texture;
}
```

### 2. 内存管理最佳实践

```javascript
// ✅ 及时清理纹理
class TexturePool {
    constructor(gl) {
        this.gl = gl;
        this.textures = new Map();
    }

    get(key) {
        return this.textures.get(key);
    }

    set(key, texture) {
        // 删除旧纹理
        if (this.textures.has(key)) {
            this.gl.deleteTexture(this.textures.get(key));
        }
        this.textures.set(key, texture);
    }

    clear() {
        this.textures.forEach((texture) => {
            this.gl.deleteTexture(texture);
        });
        this.textures.clear();
    }
}
```

### 3. 性能优化最佳实践

```javascript
// ✅ 批量设置纹理
function setupMultipleTextures(gl, textures, program) {
    textures.forEach((texture, index) => {
        gl.activeTexture(gl.TEXTURE0 + index);
        gl.bindTexture(gl.TEXTURE_2D, texture.object);
        gl.uniform1i(gl.getUniformLocation(program, texture.uniform), index);
    });
}

// ✅ 检查2的幂纹理
function isPowerOfTwo(value) {
    return (value & (value - 1)) === 0;
}

function setupTextureFiltering(gl, width, height) {
    if (isPowerOfTwo(width) && isPowerOfTwo(height)) {
        // 可以使用mipmap
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR_MIPMAP_LINEAR);
        gl.generateMipmap(gl.TEXTURE_2D);
    } else {
        // 不能使用mipmap
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    }
}
```

### 4. 错误处理最佳实践

```javascript
// ✅ 完整的错误检查
function createTextureWithErrorHandling(gl, image) {
    const texture = gl.createTexture();
    if (!texture) {
        throw new Error('Failed to create texture');
    }

    gl.bindTexture(gl.TEXTURE_2D, texture);

    try {
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

        // 检查WebGL错误
        const error = gl.getError();
        if (error !== gl.NO_ERROR) {
            gl.deleteTexture(texture);
            throw new Error(`WebGL error: ${error}`);
        }

        return texture;
    } catch (e) {
        gl.deleteTexture(texture);
        throw e;
    }
}
```

---

## 总结

这份详细的 WebGL 纹理函数参考文档涵盖了：

1. **22 个核心纹理函数**的完整参数说明
2. **实用代码示例**展示各种使用场景
3. **性能优化建议**和最佳实践
4. **错误处理方法**和调试技巧
5. **快速参考表**便于日常查阅

通过掌握这些函数和参数，您可以：

-   高效地创建和管理纹理资源
-   实现各种纹理效果和优化
-   避免常见的纹理使用错误
-   构建高性能的 WebGL 应用程序

建议将此文档作为 WebGL 纹理开发的参考手册，结合实际项目需求选择合适的函数和参数组合。
