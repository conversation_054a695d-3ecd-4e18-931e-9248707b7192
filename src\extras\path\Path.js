import { Vec3 } from '../../math/Vec3.js';
import { Mat4 } from '../../math/Mat4.js';
import CubicBezierSegment from './CubicBezierSegment.js';
import QuadraticBezierSegment from './QuadraticBezierSegment.js';
import LineSegment from './LineSegment.js';
import { clamp, toDegrees, toRadian, mat4fromRotationSinCos, rotateNormalBinormal } from './utils.js';

// 预先创建临时变量以避免重复创建对象
const tempVec3 = /* @__PURE__ */ new Vec3();
const tempMat4 = /* @__PURE__ */ new Mat4();

/**
 * 检查属性是否为null，如果是则抛出错误
 * @param {string} property - 要检查的属性名
 * @param {string} message - 错误消息
 * @throws {Error} 如果属性为null则抛出错误
 */
function throwIfNullProperty(property, message) {
    if (this[property] == null) throw new Error(message);
}

/**
 * 路径类
 * 用于创建和管理由多个路径段组成的复杂路径
 */
export class Path {
    /**
     * 创建一个新的路径
     */
    constructor() {
        this._segments = []; // 路径段数组
        this._lengthOffsets = null; // 每个段的长度偏移量
        this._totalLength = -1; // 路径总长度
        this._lastPoint = null; // 最后一个点
        this._lastTilt = 0; // 最后一个点的倾斜角度

        // 绑定断言函数，用于检查是否已经设置了起始点
        this._assertLastPoint = throwIfNullProperty.bind(this, '_lastPoint', 'Can`t get previous point of curve. Did you forget moveTo command?');

        this.tiltFunction = null; // 自定义倾斜角度计算函数
    }

    /**
     * 移动到新的起始点
     * @param {Vec3} p - 新的起始点
     * @param {number} [tilt=0] - 起始点的倾斜角度
     */
    moveTo(p, tilt = 0) {
        this._totalLength = -1; // 重置路径长度
        this._lastPoint = p; // 设置新的起始点
        this._lastTilt = tilt; // 设置新的倾斜角度
    }

    /**
     * 添加三次贝塞尔曲线段
     * @param {Vec3} cp1 - 第一个控制点
     * @param {Vec3} cp2 - 第二个控制点
     * @param {Vec3} p - 终点
     * @param {number} [tilt=0] - 终点的倾斜角度
     * @returns {Path} 返回this以支持链式调用
     */
    bezierCurveTo(cp1, cp2, p, tilt = 0) {
        this._assertLastPoint(); // 确保已经设置了起始点
        // 创建三次贝塞尔曲线段
        const seg = new CubicBezierSegment(this._lastPoint, cp1, cp2, p, this._lastTilt, tilt);
        this.addSegment(seg);
        return this;
    }

    /**
     * 添加二次贝塞尔曲线段
     * @param {Vec3} cp - 控制点
     * @param {Vec3} p - 终点
     * @param {number} [tilt=0] - 终点的倾斜角度
     * @returns {Path} 返回this以支持链式调用
     */
    quadraticCurveTo(cp, p, tilt = 0) {
        this._assertLastPoint(); // 确保已经设置了起始点
        // 创建二次贝塞尔曲线段
        const seg = new QuadraticBezierSegment(this._lastPoint, cp, p, this._lastTilt, tilt);
        this.addSegment(seg);
        return this;
    }

    /**
     * 添加直线段
     * @param {Vec3} p - 终点
     * @param {number} [tilt=0] - 终点的倾斜角度
     * @returns {Path} 返回this以支持链式调用
     */
    lineTo(p, tilt = 0) {
        this._assertLastPoint(); // 确保已经设置了起始点
        // 创建直线段
        const seg = new LineSegment(this._lastPoint, p, this._lastTilt, tilt);
        this.addSegment(seg);
        return this;
    }

    /**
     * 添加路径段
     * @param {BaseSegment} segment - 要添加的路径段
     * @returns {Path} 返回this以支持链式调用
     */
    addSegment(segment) {
        this._totalLength = -1; // 重置路径长度
        this._lastPoint = segment.lastPoint(); // 更新最后一个点
        this._lastTilt = segment.tiltEnd; // 更新最后一个点的倾斜角度
        this._segments.push(segment); // 添加段到数组
        return this;
    }

    /**
     * 获取所有路径段
     * @returns {Array} 路径段数组
     */
    getSegments() {
        return this._segments;
    }

    /**
     * 更新路径长度和长度偏移量
     */
    updateLength() {
        const n = this._segments.length;
        this._lengthOffsets = new Array(n);

        // 计算每个段的长度偏移量
        let offset = 0;
        for (let i = 0; i < n; i++) {
            this._lengthOffsets[i] = offset;
            offset += this._segments[i].getLength();
        }

        this._totalLength = offset; // 设置总长度
    }

    /**
     * 获取路径总长度
     * @returns {number} 路径总长度
     */
    getLength() {
        // 如果长度无效，则更新长度
        if (this._totalLength < 0) {
            this.updateLength();
        }

        return this._totalLength;
    }

    /**
     * 在给定的绝对长度位置查找路径段
     * @param {number} len - 绝对长度距离
     * @returns {[number, number]} [段索引, 相对段距离]
     */
    findSegmentIndexAtLength(len) {
        const totalLength = this.getLength();

        // 处理边界情况
        if (len <= 0) {
            return [0, 0]; // 路径起点
        }

        if (len >= totalLength) {
            return [this._segments.length - 1, 1]; // 路径终点
        }

        // 使用二分查找找到对应的段
        let start = 0;
        let end = this._lengthOffsets.length - 1;
        let index = -1;
        let mid;

        while (start <= end) {
            mid = Math.ceil((start + end) / 2);

            // 找到包含目标长度的段
            if (mid === 0 || mid === this._lengthOffsets.length - 1 || (len >= this._lengthOffsets[mid] && len < this._lengthOffsets[mid + 1])) {
                index = mid;
                break;
            } else if (len < this._lengthOffsets[mid]) {
                end = mid - 1; // 在左半部分查找
            } else {
                start = mid + 1; // 在右半部分查找
            }
        }

        // 计算段内的相对位置（参数t）
        const seg = this._segments[index];
        const segLen = seg.getLength();
        const t = (len - this._lengthOffsets[index]) / segLen;

        return [index, t];
    }

    /**
     * 获取路径上指定长度位置的点
     * @param {number} len - 从路径起点的长度
     * @param {Vec3} [out=new Vec3()] - 输出向量
     * @returns {Vec3} 路径上的点
     */
    getPointAtLength(len, out = new Vec3()) {
        const [i, t] = this.findSegmentIndexAtLength(len);
        return this._segments[i].getPointAt(t, out);
    }

    /**
     * 获取路径上指定参数t位置的点
     * @param {number} t - 参数值，范围[0,1]
     * @param {Vec3} [out=new Vec3()] - 输出向量
     * @returns {Vec3} 路径上的点
     */
    getPointAt(t, out = new Vec3()) {
        const totalLength = this.getLength();
        return this.getPointAtLength(t * totalLength, out);
    }

    /**
     * 获取路径上指定长度位置的切线
     * @param {number} len - 从路径起点的长度
     * @param {Vec3} [out=new Vec3()] - 输出向量
     * @returns {Vec3} 路径上的切线
     */
    getTangentAtLength(len, out = new Vec3()) {
        const [i, t] = this.findSegmentIndexAtLength(len);
        return this._segments[i].getTangentAt(t, out);
    }

    /**
     * 获取路径上指定参数t位置的切线
     * @param {number} t - 参数值，范围[0,1]
     * @param {Vec3} [out=new Vec3()] - 输出向量
     * @returns {Vec3} 路径上的切线
     */
    getTangentAt(t, out = new Vec3()) {
        const totalLength = this.getLength();
        return this.getTangentAtLength(t * totalLength, out);
    }

    /**
     * 获取路径上指定长度位置的倾斜角度
     * @param {number} len - 从路径起点的长度
     * @returns {number} 倾斜角度
     */
    getTiltAtLength(len) {
        const [i, t] = this.findSegmentIndexAtLength(len);
        return this._segments[i].getTiltAt(t);
    }

    /**
     * 获取路径上指定参数t位置的倾斜角度
     * @param {number} t - 参数值，范围[0,1]
     * @returns {number} 倾斜角度
     */
    getTiltAt(t) {
        const totalLength = this.getLength();
        return this.getTiltAtLength(t * totalLength);
    }

    /**
     * 获取路径上的一系列点
     * @param {number} [divisions=64] - 细分数量
     * @returns {Vec3[]} 点数组
     */
    getPoints(divisions = 64) {
        const points = new Array(divisions + 1);
        // 均匀采样路径上的点
        for (let i = 0; i <= divisions; i++) {
            points[i] = this.getPointAt(i / divisions);
        }
        return points;
    }

    /**
     * 生成Frenet坐标系（切线、法线和副法线）
     * 参考文献：http://www.cs.indiana.edu/pub/techreports/TR425.pdf
     * @param {number} [divisions=64] - 细分数量
     * @param {boolean} [closed=false] - 路径是否闭合
     * @returns {{tangents: Vec3[], normals: Vec3[], binormals: Vec3[], tilts: number[]}} 包含切线、法线、副法线和倾斜角度数组的对象
     */
    computeFrenetFrames(divisions = 64, closed = false) {
        // 创建存储切线和倾斜角度的数组
        const tangents = new Array(divisions + 1);
        const tilts = new Array(divisions + 1);

        // 使用自定义倾斜函数或默认函数
        const tiltFunction = this.tiltFunction ?? ((a) => a);

        // 计算路径上每个点的切线向量和倾斜角度
        const totalLength = this.getLength();
        for (let i = 0; i <= divisions; i++) {
            // 找到对应长度位置的段和参数
            const [si, st] = this.findSegmentIndexAtLength((totalLength * i) / divisions);
            const segment = this._segments[si];
            // 获取切线和倾斜角度
            tangents[i] = segment.getTangentAt(st);
            tilts[i] = tiltFunction(segment.getTiltAt(st), i / divisions, this);
        }

        // 找到第一个切线向量的最小分量方向
        const tx = Math.abs(tangents[0].x);
        const ty = Math.abs(tangents[0].y);
        const tz = Math.abs(tangents[0].z);

        // 创建一个与第一个切线垂直的法线向量
        const normal = new Vec3();
        if (tx < ty && tx < tz) {
            normal.set(1, 0, 0); // 如果x分量最小，使用x轴方向
        } else if (ty < tx && ty < tz) {
            normal.set(0, 1, 0); // 如果y分量最小，使用y轴方向
        } else {
            normal.set(0, 0, 1); // 如果z分量最小，使用z轴方向
        }

        // 创建法线和副法线数组
        const normals = new Array(divisions + 1);
        const binormals = new Array(divisions + 1);
        normals[0] = new Vec3();
        binormals[0] = new Vec3();

        // 计算第一个点的法线和副法线
        tempVec3.cross(tangents[0], normal).normalize();
        normals[0].cross(tangents[0], tempVec3);
        binormals[0].cross(tangents[0], normals[0]);

        // 计算路径上每个点的法线和副法线
        for (let i = 1; i < tangents.length; i++) {
            // 从前一个法线开始
            normals[i] = normals[i - 1].clone();
            binormals[i] = new Vec3();

            // 计算相邻切线之间的旋转
            tempVec3.cross(tangents[i - 1], tangents[i]);
            const crossLen = tempVec3.len();

            // 如果旋转量足够大，应用旋转
            if (crossLen > Number.EPSILON) {
                tempVec3.scale(1 / crossLen); // 归一化旋转轴
                // 计算旋转角度的正弦和余弦值，并限制在[-1,1]范围内
                const cosTheta = clamp(tangents[i - 1].dot(tangents[i]), -1, 1);
                const sinTheta = clamp(crossLen, -1, 1);

                // 创建旋转矩阵并应用到法线
                mat4fromRotationSinCos(tempMat4, tempVec3, sinTheta, cosTheta);
                normals[i].applyMatrix4(tempMat4);
            }

            // 计算副法线
            binormals[i].cross(tangents[i], normals[i]);
        }

        // 应用倾斜角度旋转
        for (let i = 0; i < tilts.length; i++) {
            rotateNormalBinormal(toRadian(tilts[i]), normals[i], binormals[i]);
        }

        // 如果路径是闭合的，处理首尾法线向量使其一致
        if (closed === true) {
            const normalLast = normals[normals.length - 1];
            // 计算首尾法线之间的角度差，并平均分配到中间点
            let step = Math.acos(clamp(normals[0].dot(normalLast), -1, 1)) / (normals.length - 1);

            // 确定旋转方向
            if (tangents[0].dot(tempVec3.cross(normals[0], normalLast)) > 0) {
                step = -step;
            }

            // 应用渐进旋转到中间的法线和副法线
            for (let i = 1; i < normals.length - 1; i++) {
                const angle = step * i;
                rotateNormalBinormal(angle, normals[i], binormals[i]);
                tilts[i] += toDegrees(angle); // 更新倾斜角度
            }

            // 确保最后一个法线和副法线与第一个相同
            normals[normals.length - 1] = normals[0].clone();
            binormals[binormals.length - 1] = binormals[0].clone();
        }

        // 返回计算结果
        return { tangents, normals, binormals, tilts };
    }
}
