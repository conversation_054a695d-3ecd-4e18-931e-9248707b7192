# WebGL Location 生成机制详解

## 🎯 概述

WebGL 中的 location 是 GPU 内存位置的标识符，用于定位 uniform 变量和 attribute 属性在 GPU 中的存储位置。理解 location 的生成机制对于优化 WebGL 性能至关重要。

## 🤔 为什么需要 Location 槽位系统？

### 核心问题：GPU 不能直接通过数据获取的原因

很多人会疑惑：为什么 GPU 不能直接通过变量名或数据本身来获取数据，而需要一个中间的槽位系统？

### 1. GPU 硬件架构的限制

#### 并行处理的需求

```javascript
// GPU需要同时处理成千上万个顶点
// 每个顶点都需要快速访问相同的属性布局

// 如果用变量名查找（字符串比较）：
for (let vertex = 0; vertex < 100000; vertex++) {
    // 每个顶点都要做字符串比较 - 太慢！
    if (attributeName === 'position') {
        /* 获取position数据 */
    }
    if (attributeName === 'normal') {
        /* 获取normal数据 */
    }
    if (attributeName === 'uv') {
        /* 获取uv数据 */
    }
}

// 使用location槽位（数字索引）：
for (let vertex = 0; vertex < 100000; vertex++) {
    // 直接数组访问 - 极快！
    positionData = attributes[0]; // location 0
    normalData = attributes[1]; // location 1
    uvData = attributes[2]; // location 2
}
```

#### 硬件寄存器的固定性

```javascript
// GPU硬件有固定数量的寄存器
const GPU_HARDWARE = {
    attributeRegisters: [
        "寄存器0", "寄存器1", "寄存器2", ..., "寄存器15"
    ],
    // 这些寄存器只能通过数字索引访问，不支持字符串名称
};

// 硬件层面的访问方式
function readAttribute(locationIndex) {
    return GPU_HARDWARE.attributeRegisters[locationIndex]; // 直接索引
}

// 不可能的访问方式（硬件不支持）
function readAttributeByName(name) {
    // GPU硬件没有字符串处理能力！
    return GPU_HARDWARE.findByName(name); // ❌ 硬件不支持
}
```

### 2. 编译时优化的需要

#### 着色器编译优化

```javascript
// 着色器源码（编译前）
const vertexShader = `
    attribute vec3 position;
    attribute vec3 normal;
    uniform mat4 modelMatrix;

    void main() {
        // 编译器需要知道如何访问这些变量
        gl_Position = modelMatrix * vec4(position, 1.0);
    }
`;

// 编译后的伪代码（GPU指令）
const compiledInstructions = [
    'LOAD_ATTRIBUTE 0 -> position', // 从槽位0加载position
    'LOAD_UNIFORM 5 -> modelMatrix', // 从槽位5加载modelMatrix
    'MULTIPLY position, modelMatrix', // 执行矩阵乘法
    'STORE_OUTPUT gl_Position', // 存储结果
];
```

#### 预分配内存布局

```javascript
// 链接时确定内存布局
const memoryLayout = {
    attributes: {
        0: { name: 'position', size: 12, offset: 0 }, // 3 * 4字节
        1: { name: 'normal', size: 12, offset: 12 }, // 3 * 4字节
        2: { name: 'uv', size: 8, offset: 24 }, // 2 * 4字节
    },
    uniforms: {
        5: { name: 'modelMatrix', size: 64, type: 'mat4' },
    },
};
```

### 3. 性能和效率考虑

#### 字符串查找 vs 数字索引

```javascript
// 字符串查找的开销（假设的低效实现）
function getDataByName(name) {
    // 每次都要遍历所有属性 - O(n)复杂度
    for (let attr of allAttributes) {
        if (attr.name === name) {
            // 字符串比较很慢
            return attr.data;
        }
    }
}

// 数字索引的效率（实际的高效实现）
function getDataByLocation(location) {
    // 直接数组访问 - O(1)复杂度
    return attributeArray[location]; // 极快的内存访问
}
```

#### 内存访问模式

```javascript
// GPU的内存访问特点
const GPU_MEMORY_ACCESS = {
    // 连续内存访问很快
    fastAccess: 'array[0], array[1], array[2]',

    // 随机内存访问很慢
    slowAccess: "hashMap['position'], hashMap['normal']",

    // 字符串处理在GPU上很困难
    impossibleOnGPU: 'string comparison and hashing',
};
```

### 4. 跨平台兼容性

#### 不同 GPU 的统一接口

```javascript
// 不同厂商的GPU都支持相同的location系统
const NVIDIA_GPU = {
    getAttributeData: (location) => nvidiaRegisters[location],
};

const AMD_GPU = {
    getAttributeData: (location) => amdRegisters[location],
};

const INTEL_GPU = {
    getAttributeData: (location) => intelRegisters[location],
};

// 统一的访问方式
function universalAccess(location) {
    return currentGPU.getAttributeData(location); // 所有GPU都支持
}
```

### 5. 编译器和驱动的实现简化

#### 驱动程序的职责

```javascript
// WebGL驱动需要将高级概念转换为硬件指令
class WebGLDriver {
    compileShader(source) {
        // 1. 解析变量名
        const variables = parseVariables(source);

        // 2. 分配硬件槽位
        const locationMap = allocateLocations(variables);

        // 3. 生成硬件指令
        const instructions = generateInstructions(source, locationMap);

        return { instructions, locationMap };
    }

    // 如果没有location系统，驱动会非常复杂
    // 需要在运行时动态解析变量名 - 性能灾难！
}
```

## 🔍 Location 的类型

### 1. Uniform Location

-   **用途**: 定位着色器中的 uniform 变量
-   **获取方式**: `gl.getUniformLocation(program, name)`
-   **返回类型**: `WebGLUniformLocation` 对象

### 2. Attribute Location

-   **用途**: 定位顶点着色器中的 attribute 属性
-   **获取方式**: `gl.getAttribLocation(program, name)`
-   **返回类型**: `GLint` 数字（0-15 范围内）

## 🏗️ Location 生成的时机

### 1. 编译阶段（Compile）

```javascript
// 着色器编译时，WebGL 解析变量声明
const vertexShader = `
    attribute vec3 position;    // 将被分配 attribute location
    uniform mat4 modelMatrix;   // 将被分配 uniform location
    uniform vec3 color;         // 将被分配 uniform location
`;

const fragmentShader = `
    uniform float time;         // 将被分配 uniform location
    uniform sampler2D texture;  // 将被分配 uniform location
`;
```

### 2. 链接阶段（Link）

```javascript
// linkProgram 时进行最终的 location 分配
gl.linkProgram(program);

// 链接成功后，所有 location 已确定
if (gl.getProgramParameter(program, gl.LINK_STATUS)) {
    // 现在可以查询 location
    const positionLoc = gl.getAttribLocation(program, 'position');
    const modelMatrixLoc = gl.getUniformLocation(program, 'modelMatrix');
}
```

## 🧮 Uniform Location 生成算法

### 1. 基本分配策略

```javascript
// WebGL 内部的 uniform location 分配过程（概念模型）
class UniformLocationAllocator {
    constructor(program) {
        this.program = program;
        this.uniformSlots = new Map();
        this.nextSlotIndex = 0;
    }

    allocateLocation(uniformName, uniformType, arraySize = 1) {
        // 1. 计算所需的槽位数量
        const slotsNeeded = this.calculateSlotsNeeded(uniformType, arraySize);

        // 2. 分配连续的槽位
        const startSlot = this.nextSlotIndex;
        this.nextSlotIndex += slotsNeeded;

        // 3. 创建 location 对象
        const location = new WebGLUniformLocation(
            this.program, // 参数1: WebGLProgram - 关联的着色器程序对象
            //   作用: 建立location与特定程序的绑定关系
            //   重要性: 确保location只能在对应程序中使用
            //   类型: WebGLProgram 对象引用
            //   验证: 后续uniform操作会检查此程序是否为当前激活程序

            uniformName, // 参数2: string - uniform变量的完整名称
            //   作用: 标识具体的uniform变量，用于调试和验证
            //   格式: "variableName" 或 "arrayName[0]" 或 "structName.member"
            //   示例: "uTime", "uColors[0]", "uLight.position"
            //   用途: 错误报告、调试信息、location验证
            //   注意: 数组和结构体有特殊的命名规则

            startSlot, // 参数3: number - 起始槽位索引
            //   作用: 指定该uniform在GPU内存中的起始位置
            //   范围: 0 到 maxUniformSlots-1 (通常0-1023或更多)
            //   计算: 基于之前分配的uniform累计槽位数
            //   重要性: 决定了数据在GPU内存中的具体存储位置
            //   内存地址: 绝对地址 = 程序基地址 + startSlot × 16字节

            uniformType, // 参数4: GLenum - WebGL uniform类型常量
            //   作用: 指定uniform的数据类型，影响内存布局和数据传输方式
            //   常见值及其含义:
            //     gl.FLOAT (5126)        - float (4字节)
            //     gl.FLOAT_VEC2 (35664)  - vec2 (8字节)
            //     gl.FLOAT_VEC3 (35665)  - vec3 (12字节，按16字节对齐)
            //     gl.FLOAT_VEC4 (35666)  - vec4 (16字节)
            //     gl.INT (5124)          - int (4字节)
            //     gl.INT_VEC2 (35667)    - ivec2 (8字节)
            //     gl.INT_VEC3 (35668)    - ivec3 (12字节，按16字节对齐)
            //     gl.INT_VEC4 (35669)    - ivec4 (16字节)
            //     gl.BOOL (35670)        - bool (4字节，存储为int)
            //     gl.BOOL_VEC2 (35671)   - bvec2 (8字节)
            //     gl.BOOL_VEC3 (35672)   - bvec3 (12字节，按16字节对齐)
            //     gl.BOOL_VEC4 (35673)   - bvec4 (16字节)
            //     gl.FLOAT_MAT2 (35674)  - mat2 (占用2个槽位，32字节)
            //     gl.FLOAT_MAT3 (35675)  - mat3 (占用3个槽位，48字节)
            //     gl.FLOAT_MAT4 (35676)  - mat4 (占用4个槽位，64字节)
            //     gl.SAMPLER_2D (35678)  - sampler2D (纹理单元索引)
            //     gl.SAMPLER_CUBE (35680) - samplerCube (立方体纹理索引)
            //   影响: 决定使用哪个gl.uniform*()函数进行数据传输

            arraySize // 参数5: number - 数组大小(可选，默认1)
            //   作用: 指定uniform数组的元素数量
            //   值: 1表示单个变量，>1表示数组
            //   影响: 总槽位数 = 单个类型槽位数 × arraySize
            //   示例:
            //     float[5] 占用5个槽位 (5×1=5)
            //     vec3[4] 占用4个槽位 (4×1=4)
            //     mat4[2] 占用8个槽位 (2×4=8)
            //   注意: 数组的每个元素都需要单独的location查询
            //         如: uArray[0], uArray[1], uArray[2]...
            //   内存布局: 数组元素在内存中连续存储
        );

        // 4. 记录分配信息
        this.uniformSlots.set(uniformName, {
            location,
            startSlot,
            slotsUsed: slotsNeeded,
        });

        return location;
    }

    /**
     * 计算特定类型的uniform变量需要占用的GPU内存槽位数量
     *
     * @param {GLenum} type - WebGL uniform类型常量
     *   作用: 确定uniform的数据类型，影响内存占用计算
     *   范围: 所有WebGL支持的uniform类型常量
     *   示例: gl.FLOAT, gl.FLOAT_VEC3, gl.FLOAT_MAT4, gl.SAMPLER_2D
     *
     * @param {number} arraySize - 数组大小
     *   作用: 指定uniform数组的元素数量
     *   值: 1表示单个变量，>1表示数组
     *   影响: 总槽位数 = 单个类型槽位数 × arraySize
     *   示例: float[5] → arraySize=5, mat4[3] → arraySize=3
     *
     * @returns {number} 需要占用的槽位总数
     *   用途: GPU内存分配、地址计算、性能优化
     *   计算公式: 基础槽位数 × 数组大小
     *
     * 核心作用:
     *   1. 内存预分配: 为uniform分配器提供精确的内存需求
     *   2. 连续分配: 确保数组和矩阵元素在内存中连续存储
     *   3. 对齐优化: 满足GPU的vec4对齐要求(每槽位16字节)
     *   4. 空间规划: 避免内存碎片，提高访问效率
     *
     * 应用场景:
     *   - Location分配器计算内存需求
     *   - 程序链接时的内存布局规划
     *   - 性能分析中的内存使用统计
     *   - 调试工具中的内存可视化
     */
    calculateSlotsNeeded(type, arraySize) {
        /**
         * GPU内存槽位分配表
         *
         * 设计原则:
         *   - 所有类型按16字节(vec4)边界对齐
         *   - 标量和向量类型占用1个槽位
         *   - 矩阵类型按列数分配槽位
         *   - 纹理采样器占用1个槽位(存储纹理单元索引)
         *
         * 内存布局详解:
         *   每个槽位 = 16字节 = 4个float = 1个vec4
         *   GPU硬件要求所有uniform按vec4边界对齐
         */
        const slotSizes = {
            // ========================================
            // 浮点数类型 - 按vec4对齐(16字节=1槽位)
            // ========================================
            [gl.FLOAT]: 1, // float: 4字节 → 1槽位(浪费12字节，但满足对齐)
            // 内存布局: [float, padding, padding, padding]

            [gl.FLOAT_VEC2]: 1, // vec2: 8字节 → 1槽位(浪费8字节)
            // 内存布局: [float, float, padding, padding]

            [gl.FLOAT_VEC3]: 1, // vec3: 12字节 → 1槽位(浪费4字节)
            // 内存布局: [float, float, float, padding]

            [gl.FLOAT_VEC4]: 1, // vec4: 16字节 → 1槽位(完全利用)
            // 内存布局: [float, float, float, float]

            // ========================================
            // 矩阵类型 - 按列存储，每列占用1个槽位
            // ========================================
            [gl.FLOAT_MAT2]: 2, // mat2: 2列×2行 → 2槽位(32字节)
            // 内存布局: [col0: vec2+padding][col1: vec2+padding]
            // 实际使用: 16字节，浪费16字节

            [gl.FLOAT_MAT3]: 3, // mat3: 3列×3行 → 3槽位(48字节)
            // 内存布局: [col0: vec3+pad][col1: vec3+pad][col2: vec3+pad]
            // 实际使用: 36字节，浪费12字节

            [gl.FLOAT_MAT4]: 4, // mat4: 4列×4行 → 4槽位(64字节)
            // 内存布局: [col0: vec4][col1: vec4][col2: vec4][col3: vec4]
            // 实际使用: 64字节，完全利用

            // ========================================
            // 纹理采样器类型 - 存储纹理单元索引
            // ========================================
            [gl.SAMPLER_2D]: 1, // sampler2D: 纹理单元索引(int) → 1槽位
            // 内存布局: [textureUnit, padding, padding, padding]
            // 值范围: 0 到 gl.MAX_TEXTURE_IMAGE_UNITS-1

            [gl.SAMPLER_CUBE]: 1, // samplerCube: 立方体纹理单元索引 → 1槽位
            // 内存布局: [textureUnit, padding, padding, padding]
            // 用途: 环境映射、反射、天空盒等
        };

        /**
         * 槽位数量计算
         *
         * 公式: 总槽位数 = 基础槽位数 × 数组大小
         *
         * 计算示例:
         *   uniform float uTime;           → 1 × 1 = 1槽位
         *   uniform vec3 uColor;           → 1 × 1 = 1槽位
         *   uniform mat4 uMatrix;          → 4 × 1 = 4槽位
         *   uniform float uValues[5];      → 1 × 5 = 5槽位
         *   uniform mat4 uMatrices[3];     → 4 × 3 = 12槽位
         *   uniform sampler2D uTextures[8]; → 1 × 8 = 8槽位
         *
         * 特殊处理:
         *   - 如果类型未知，默认分配1个槽位(向前兼容)
         *   - 数组元素在内存中连续存储
         *   - 矩阵数组按列主序连续排列
         */
        return (slotSizes[type] || 1) * arraySize;
    }
}
```

### 2. UniformLocationAllocator 使用示例

```javascript
// ========================================
// 完整的 UniformLocationAllocator 使用示例
// ========================================

/**
 * 示例场景：创建一个复杂的着色器程序
 * 包含各种类型的uniform变量：标量、向量、矩阵、数组、纹理
 */

// 1. 创建着色器程序
const vertexShader = `
    attribute vec3 position;
    attribute vec2 uv;

    uniform mat4 uProjectionMatrix;  // 投影矩阵
    uniform mat4 uViewMatrix;        // 视图矩阵
    uniform mat4 uModelMatrix;       // 模型矩阵

    varying vec2 vUv;

    void main() {
        vUv = uv;
        gl_Position = uProjectionMatrix * uViewMatrix * uModelMatrix * vec4(position, 1.0);
    }
`;

const fragmentShader = `
    precision mediump float;

    // 基础uniform
    uniform float uTime;             // 时间
    uniform vec3 uColor;             // 颜色
    uniform vec2 uResolution;        // 分辨率

    // 光照相关
    uniform vec3 uLightPosition;     // 光源位置
    uniform vec3 uLightColor;        // 光源颜色
    uniform float uLightIntensity;   // 光照强度

    // 材质属性
    uniform float uMetallic;         // 金属度
    uniform float uRoughness;        // 粗糙度
    uniform vec3 uEmissive;          // 自发光

    // 纹理
    uniform sampler2D uDiffuseMap;   // 漫反射贴图
    uniform sampler2D uNormalMap;    // 法线贴图
    uniform samplerCube uEnvMap;     // 环境贴图

    // 数组uniform
    uniform vec3 uColors[4];         // 颜色数组
    uniform float uWeights[8];       // 权重数组
    uniform mat4 uBoneMatrices[16];  // 骨骼矩阵数组

    varying vec2 vUv;

    void main() {
        // 着色器逻辑...
        gl_FragColor = vec4(uColor, 1.0);
    }
`;

// 2. 编译和链接程序
const program = createProgram(gl, vertexShader, fragmentShader);

// 3. 创建 UniformLocationAllocator 实例
const allocator = new UniformLocationAllocator(program);

console.log('=== UniformLocationAllocator 使用示例 ===');

// 4. 模拟分配过程 - 按照着色器中出现的顺序分配
console.log('\n🔄 开始分配uniform locations...\n');

// 矩阵类型 uniform
const projectionLocation = allocator.allocateLocation('uProjectionMatrix', gl.FLOAT_MAT4, 1);
console.log('✅ uProjectionMatrix:', {
    location: projectionLocation,
    slotIndex: projectionLocation.__slotIndex,
    slotCount: 4,
    memoryRange: `槽位 ${projectionLocation.__slotIndex}-${projectionLocation.__slotIndex + 3}`,
});

const viewLocation = allocator.allocateLocation('uViewMatrix', gl.FLOAT_MAT4, 1);
console.log('✅ uViewMatrix:', {
    location: viewLocation,
    slotIndex: viewLocation.__slotIndex,
    slotCount: 4,
    memoryRange: `槽位 ${viewLocation.__slotIndex}-${viewLocation.__slotIndex + 3}`,
});

const modelLocation = allocator.allocateLocation('uModelMatrix', gl.FLOAT_MAT4, 1);
console.log('✅ uModelMatrix:', {
    location: modelLocation,
    slotIndex: modelLocation.__slotIndex,
    slotCount: 4,
    memoryRange: `槽位 ${modelLocation.__slotIndex}-${modelLocation.__slotIndex + 3}`,
});

// 标量和向量类型 uniform
const timeLocation = allocator.allocateLocation('uTime', gl.FLOAT, 1);
console.log('✅ uTime:', {
    location: timeLocation,
    slotIndex: timeLocation.__slotIndex,
    slotCount: 1,
    memoryRange: `槽位 ${timeLocation.__slotIndex}`,
});

const colorLocation = allocator.allocateLocation('uColor', gl.FLOAT_VEC3, 1);
console.log('✅ uColor:', {
    location: colorLocation,
    slotIndex: colorLocation.__slotIndex,
    slotCount: 1,
    memoryRange: `槽位 ${colorLocation.__slotIndex}`,
});

const resolutionLocation = allocator.allocateLocation('uResolution', gl.FLOAT_VEC2, 1);
console.log('✅ uResolution:', {
    location: resolutionLocation,
    slotIndex: resolutionLocation.__slotIndex,
    slotCount: 1,
    memoryRange: `槽位 ${resolutionLocation.__slotIndex}`,
});

// 光照相关 uniform
const lightPosLocation = allocator.allocateLocation('uLightPosition', gl.FLOAT_VEC3, 1);
const lightColorLocation = allocator.allocateLocation('uLightColor', gl.FLOAT_VEC3, 1);
const lightIntensityLocation = allocator.allocateLocation('uLightIntensity', gl.FLOAT, 1);

console.log('✅ 光照相关uniform:', {
    uLightPosition: `槽位 ${lightPosLocation.__slotIndex}`,
    uLightColor: `槽位 ${lightColorLocation.__slotIndex}`,
    uLightIntensity: `槽位 ${lightIntensityLocation.__slotIndex}`,
});

// 材质属性 uniform
const metallicLocation = allocator.allocateLocation('uMetallic', gl.FLOAT, 1);
const roughnessLocation = allocator.allocateLocation('uRoughness', gl.FLOAT, 1);
const emissiveLocation = allocator.allocateLocation('uEmissive', gl.FLOAT_VEC3, 1);

console.log('✅ 材质属性uniform:', {
    uMetallic: `槽位 ${metallicLocation.__slotIndex}`,
    uRoughness: `槽位 ${roughnessLocation.__slotIndex}`,
    uEmissive: `槽位 ${emissiveLocation.__slotIndex}`,
});

// 纹理 uniform
const diffuseLocation = allocator.allocateLocation('uDiffuseMap', gl.SAMPLER_2D, 1);
const normalLocation = allocator.allocateLocation('uNormalMap', gl.SAMPLER_2D, 1);
const envLocation = allocator.allocateLocation('uEnvMap', gl.SAMPLER_CUBE, 1);

console.log('✅ 纹理uniform:', {
    uDiffuseMap: `槽位 ${diffuseLocation.__slotIndex}`,
    uNormalMap: `槽位 ${normalLocation.__slotIndex}`,
    uEnvMap: `槽位 ${envLocation.__slotIndex}`,
});

// 数组 uniform
const colorsLocation = allocator.allocateLocation('uColors', gl.FLOAT_VEC3, 4);
console.log('✅ uColors[4]:', {
    location: colorsLocation,
    slotIndex: colorsLocation.__slotIndex,
    slotCount: 4,
    memoryRange: `槽位 ${colorsLocation.__slotIndex}-${colorsLocation.__slotIndex + 3}`,
    note: '4个vec3，每个占用1个槽位',
});

const weightsLocation = allocator.allocateLocation('uWeights', gl.FLOAT, 8);
console.log('✅ uWeights[8]:', {
    location: weightsLocation,
    slotIndex: weightsLocation.__slotIndex,
    slotCount: 8,
    memoryRange: `槽位 ${weightsLocation.__slotIndex}-${weightsLocation.__slotIndex + 7}`,
    note: '8个float，每个占用1个槽位',
});

const boneMatricesLocation = allocator.allocateLocation('uBoneMatrices', gl.FLOAT_MAT4, 16);
console.log('✅ uBoneMatrices[16]:', {
    location: boneMatricesLocation,
    slotIndex: boneMatricesLocation.__slotIndex,
    slotCount: 64, // 16 * 4 = 64
    memoryRange: `槽位 ${boneMatricesLocation.__slotIndex}-${boneMatricesLocation.__slotIndex + 63}`,
    note: '16个mat4，每个占用4个槽位，总共64个槽位',
});

// 5. 显示分配统计
console.log('\n📊 分配统计信息:');
console.log('总共分配的uniform数量:', allocator.uniformSlots.size);
console.log('下一个可用槽位索引:', allocator.nextSlotIndex);
console.log('总共占用的槽位数:', allocator.nextSlotIndex);
console.log('占用的GPU内存:', allocator.nextSlotIndex * 16, '字节');

// 6. 显示详细的内存布局
console.log('\n🗺️ GPU内存布局图:');
let currentSlot = 0;
allocator.uniformSlots.forEach((info, uniformName) => {
    const location = info.location;
    const slotCount = info.slotsUsed;
    const endSlot = info.startSlot + slotCount - 1;

    console.log(`槽位 ${info.startSlot.toString().padStart(2, '0')}-${endSlot.toString().padStart(2, '0')}: ${uniformName.padEnd(20)} (${slotCount}槽位, ${slotCount * 16}字节)`);
});

// 7. 验证分配结果
console.log('\n🔍 验证分配结果:');

// 检查是否有槽位重叠
const usedSlots = new Set();
let hasOverlap = false;

allocator.uniformSlots.forEach((info, uniformName) => {
    for (let i = 0; i < info.slotsUsed; i++) {
        const slot = info.startSlot + i;
        if (usedSlots.has(slot)) {
            console.error(`❌ 槽位冲突: ${uniformName} 使用的槽位 ${slot} 已被占用`);
            hasOverlap = true;
        }
        usedSlots.add(slot);
    }
});

if (!hasOverlap) {
    console.log('✅ 所有uniform的槽位分配正确，无重叠');
}

// 8. 性能分析
console.log('\n⚡ 性能分析:');
console.log('内存利用率:', ((usedSlots.size / allocator.nextSlotIndex) * 100).toFixed(1) + '%');
console.log('最大支持的uniform槽位数:', 1024, '(典型GPU)');
console.log('当前使用率:', ((allocator.nextSlotIndex / 1024) * 100).toFixed(1) + '%');

// 9. 实际使用示例
console.log('\n🎮 实际使用示例:');
console.log('// 激活程序并设置uniform值');
console.log('gl.useProgram(program);');
console.log('');
console.log('// 设置矩阵uniform');
console.log('gl.uniformMatrix4fv(projectionLocation, false, projectionMatrix);');
console.log('gl.uniformMatrix4fv(viewLocation, false, viewMatrix);');
console.log('gl.uniformMatrix4fv(modelLocation, false, modelMatrix);');
console.log('');
console.log('// 设置标量和向量uniform');
console.log('gl.uniform1f(timeLocation, performance.now() * 0.001);');
console.log('gl.uniform3fv(colorLocation, [1.0, 0.5, 0.2]);');
console.log('gl.uniform2fv(resolutionLocation, [canvas.width, canvas.height]);');
console.log('');
console.log('// 设置纹理uniform');
console.log('gl.uniform1i(diffuseLocation, 0); // 纹理单元0');
console.log('gl.uniform1i(normalLocation, 1);  // 纹理单元1');
console.log('gl.uniform1i(envLocation, 2);     // 纹理单元2');
console.log('');
console.log('// 设置数组uniform (需要逐个设置)');
console.log('for (let i = 0; i < 4; i++) {');
console.log('    const loc = gl.getUniformLocation(program, `uColors[${i}]`);');
console.log('    gl.uniform3fv(loc, colors[i]);');
console.log('}');
```

### 3. 实际分配示例

```javascript
// 着色器中的 uniform 声明
const fragmentShader = `
    uniform float uTime;        // 分配到槽位 0
    uniform vec3 uColor;        // 分配到槽位 1  
    uniform mat4 uMatrix;       // 分配到槽位 2-5 (4个槽位)
    uniform sampler2D uTexture; // 分配到槽位 6
    uniform float uArray[3];    // 分配到槽位 7-9 (3个槽位)
`;

// 查询分配结果
const timeLocation = gl.getUniformLocation(program, 'uTime');
const colorLocation = gl.getUniformLocation(program, 'uColor');
const matrixLocation = gl.getUniformLocation(program, 'uMatrix');
const textureLocation = gl.getUniformLocation(program, 'uTexture');
const arrayLocation = gl.getUniformLocation(program, 'uArray[0]');
```

## 🎯 Attribute Location 生成算法

### 1. 自动分配策略

```javascript
// WebGL 内部的 attribute location 分配过程
class AttributeLocationAllocator {
    constructor() {
        this.usedLocations = new Set();
        this.maxLocations = 16; // WebGL 最多支持 16 个 attribute
    }

    allocateLocation(attributeName, attributeType) {
        // 1. 寻找可用的位置
        for (let location = 0; location < this.maxLocations; location++) {
            if (!this.usedLocations.has(location)) {
                // 2. 检查是否需要多个位置（矩阵类型）
                const locationsNeeded = this.getLocationsNeeded(attributeType);

                // 3. 确保有足够的连续位置
                if (this.hasConsecutiveLocations(location, locationsNeeded)) {
                    // 4. 标记位置为已使用
                    for (let i = 0; i < locationsNeeded; i++) {
                        this.usedLocations.add(location + i);
                    }

                    return location;
                }
            }
        }

        throw new Error('No available attribute locations');
    }

    getLocationsNeeded(type) {
        // 矩阵类型需要多个位置
        if (type === gl.FLOAT_MAT2) return 2;
        if (type === gl.FLOAT_MAT3) return 3;
        if (type === gl.FLOAT_MAT4) return 4;
        return 1; // 其他类型只需要 1 个位置
    }

    hasConsecutiveLocations(start, count) {
        for (let i = 0; i < count; i++) {
            if (this.usedLocations.has(start + i) || start + i >= this.maxLocations) {
                return false;
            }
        }
        return true;
    }
}
```

### 2. 手动绑定位置

```javascript
// 在链接前手动指定 attribute 位置
gl.bindAttribLocation(program, 0, 'position');
gl.bindAttribLocation(program, 1, 'normal');
gl.bindAttribLocation(program, 2, 'uv');
gl.bindAttribLocation(program, 3, 'color');

// 然后进行链接
gl.linkProgram(program);

// 验证分配结果
console.log('position location:', gl.getAttribLocation(program, 'position')); // 0
console.log('normal location:', gl.getAttribLocation(program, 'normal')); // 1
console.log('uv location:', gl.getAttribLocation(program, 'uv')); // 2
console.log('color location:', gl.getAttribLocation(program, 'color')); // 3
```

## 🔄 Location 重复和冲突

### 1. 不同程序间的 Location 重复

```javascript
// 两个不同的程序可能有相同的 location 值
const program1 = createProgram(vs1, fs1);
const program2 = createProgram(vs2, fs2);

// 获取 location
const loc1 = gl.getUniformLocation(program1, 'uTime'); // 可能返回槽位 0
const loc2 = gl.getUniformLocation(program2, 'uColor'); // 也可能返回槽位 0

// 虽然槽位索引相同，但它们指向不同程序的不同内存区域
console.log('Program1 uTime 槽位:', loc1.__slotIndex); // 0
console.log('Program2 uColor 槽位:', loc2.__slotIndex); // 0
console.log('是否相同对象:', loc1 === loc2); // false
```

### 2. WebGLUniformLocation 的内部结构

```javascript
// WebGLUniformLocation 的概念模型
class WebGLUniformLocation {
    constructor(program, name, slotIndex, type, size) {
        this.__program = program; // 关联的程序对象
        this.__name = name; // uniform 名称
        this.__slotIndex = slotIndex; // 在该程序中的槽位索引
        this.__type = type; // 数据类型
        this.__size = size; // 数组大小
        this.__id = generateUniqueId(); // 全局唯一标识符
    }

    // 获取在 GPU 中的绝对地址
    getAbsoluteAddress() {
        return this.__program.uniformBaseAddress + this.__slotIndex * 16;
    }

    // 验证是否属于当前活跃程序
    isValidForCurrentProgram(gl) {
        return gl.currentProgram === this.__program;
    }
}
```

## 🚀 优化策略

### 1. Location 缓存

```javascript
class ProgramManager {
    constructor(gl) {
        this.gl = gl;
        this.locationCache = new Map();
    }

    getUniformLocation(program, name) {
        const key = `${program.__id}_${name}`;

        if (!this.locationCache.has(key)) {
            const location = this.gl.getUniformLocation(program, name);
            this.locationCache.set(key, location);
        }

        return this.locationCache.get(key);
    }

    getAttributeLocation(program, name) {
        const key = `${program.__id}_attr_${name}`;

        if (!this.locationCache.has(key)) {
            const location = this.gl.getAttribLocation(program, name);
            this.locationCache.set(key, location);
        }

        return this.locationCache.get(key);
    }
}
```

### 2. 批量查询优化

```javascript
class LocationBatcher {
    static cacheAllLocations(gl, program) {
        const locations = {
            uniforms: new Map(),
            attributes: new Map(),
        };

        // 批量缓存所有 uniform locations
        const uniformCount = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
        for (let i = 0; i < uniformCount; i++) {
            const info = gl.getActiveUniform(program, i);
            const location = gl.getUniformLocation(program, info.name);
            if (location !== null) {
                locations.uniforms.set(info.name, {
                    location,
                    type: info.type,
                    size: info.size,
                });
            }
        }

        // 批量缓存所有 attribute locations
        const attributeCount = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);
        for (let i = 0; i < attributeCount; i++) {
            const info = gl.getActiveAttrib(program, i);
            const location = gl.getAttribLocation(program, info.name);
            if (location !== -1) {
                locations.attributes.set(info.name, {
                    location,
                    type: info.type,
                    size: info.size,
                });
            }
        }

        return locations;
    }
}
```

## 📊 Location 分配的限制

### 1. Uniform Location 限制

-   **数量限制**: 取决于 GPU 硬件，通常为 1024-4096 个槽位
-   **类型限制**: 不同类型占用不同数量的槽位
-   **数组限制**: 数组元素占用连续的槽位

### 2. Attribute Location 限制

-   **数量限制**: WebGL 标准规定最多 16 个 attribute location
-   **矩阵限制**: mat4 矩阵占用 4 个连续的 location
-   **实例化限制**: 实例化属性需要额外考虑除数设置

## 🎯 最佳实践

### 1. 合理规划 Location 使用

```javascript
// 推荐的 attribute location 分配策略
const ATTRIBUTE_LOCATIONS = {
    POSITION: 0,
    NORMAL: 1,
    UV: 2,
    COLOR: 3,
    TANGENT: 4,
    INSTANCE_MATRIX: 5, // mat4 占用 5-8
    INSTANCE_COLOR: 9,
};

// 在链接前绑定位置
Object.entries(ATTRIBUTE_LOCATIONS).forEach(([name, location]) => {
    gl.bindAttribLocation(program, location, name.toLowerCase());
});
```

### 2. 统一的 Location 管理

```javascript
class LocationManager {
    static createLocationMap(gl, program) {
        return {
            uniforms: this.getAllUniformLocations(gl, program),
            attributes: this.getAllAttributeLocations(gl, program),
        };
    }

    static getAllUniformLocations(gl, program) {
        const locations = new Map();
        const count = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);

        for (let i = 0; i < count; i++) {
            const info = gl.getActiveUniform(program, i);
            const location = gl.getUniformLocation(program, info.name);

            if (location) {
                locations.set(info.name, {
                    location,
                    type: info.type,
                    size: info.size,
                    slotIndex: location.__slotIndex, // 调试用
                });
            }
        }

        return locations;
    }

    static getAllAttributeLocations(gl, program) {
        const locations = new Map();
        const count = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);

        for (let i = 0; i < count; i++) {
            const info = gl.getActiveAttrib(program, i);
            const location = gl.getAttribLocation(program, info.name);

            if (location !== -1) {
                locations.set(info.name, {
                    location,
                    type: info.type,
                    size: info.size,
                });
            }
        }

        return locations;
    }
}
```

## 🔍 调试和诊断

### 1. Location 信息查看

```javascript
function debugProgramLocations(gl, program) {
    console.group('Program Location Debug Info');

    // 显示所有 uniform locations
    console.group('Uniform Locations:');
    const uniformCount = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    for (let i = 0; i < uniformCount; i++) {
        const info = gl.getActiveUniform(program, i);
        const location = gl.getUniformLocation(program, info.name);
        console.log(`${info.name}: location=${location}, type=${info.type}, size=${info.size}`);
    }
    console.groupEnd();

    // 显示所有 attribute locations
    console.group('Attribute Locations:');
    const attributeCount = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);
    for (let i = 0; i < attributeCount; i++) {
        const info = gl.getActiveAttrib(program, i);
        const location = gl.getAttribLocation(program, info.name);
        console.log(`${info.name}: location=${location}, type=${info.type}, size=${info.size}`);
    }
    console.groupEnd();

    console.groupEnd();
}
```

### 2. Location 冲突检测

```javascript
class LocationConflictDetector {
    static checkAttributeConflicts(programs) {
        const conflicts = [];

        programs.forEach((program, index) => {
            const locations = this.getAttributeLocationMap(program);

            // 检查位置冲突
            const usedLocations = new Set();
            locations.forEach((info, name) => {
                if (usedLocations.has(info.location)) {
                    conflicts.push({
                        program: index,
                        attribute: name,
                        location: info.location,
                        type: 'duplicate_location',
                    });
                }
                usedLocations.add(info.location);
            });
        });

        return conflicts;
    }
}
```

## 🔬 深入理解：Location 的内存映射

### 1. GPU 内存布局

```javascript
// GPU 内存中的 uniform 布局（概念模型）
class GPUUniformMemory {
    constructor(program) {
        this.program = program;
        this.baseAddress = this.allocateUniformBlock(); // 分配基础内存块
        this.slotSize = 16; // 每个槽位 16 字节（vec4 对齐）
        this.slotMap = new Map(); // 槽位映射表
    }

    allocateUniformBlock() {
        // 为程序分配独立的 uniform 内存块
        return GPUMemoryManager.allocate(this.calculateRequiredSize());
    }

    calculateRequiredSize() {
        // 计算程序所需的总内存大小
        let totalSlots = 0;
        this.program.uniforms.forEach((uniform) => {
            totalSlots += this.getUniformSlotCount(uniform.type, uniform.size);
        });
        return totalSlots * this.slotSize;
    }

    getUniformSlotCount(type, arraySize = 1) {
        const slotCounts = {
            [gl.FLOAT]: 1,
            [gl.FLOAT_VEC2]: 1,
            [gl.FLOAT_VEC3]: 1,
            [gl.FLOAT_VEC4]: 1,
            [gl.INT]: 1,
            [gl.INT_VEC2]: 1,
            [gl.INT_VEC3]: 1,
            [gl.INT_VEC4]: 1,
            [gl.BOOL]: 1,
            [gl.BOOL_VEC2]: 1,
            [gl.BOOL_VEC3]: 1,
            [gl.BOOL_VEC4]: 1,
            [gl.FLOAT_MAT2]: 2,
            [gl.FLOAT_MAT3]: 3,
            [gl.FLOAT_MAT4]: 4,
            [gl.SAMPLER_2D]: 1,
            [gl.SAMPLER_CUBE]: 1,
        };

        return (slotCounts[type] || 1) * arraySize;
    }

    // 获取 uniform 的绝对内存地址
    getUniformAddress(slotIndex) {
        return this.baseAddress + slotIndex * this.slotSize;
    }
}
```

### 2. Location 对象的完整结构

```javascript
// WebGLUniformLocation 的完整实现模型
class WebGLUniformLocation {
    /**
     * WebGLUniformLocation 构造函数
     *
     * @param {WebGLProgram} program - 关联的着色器程序对象
     *   作用: 建立location与特定程序的绑定关系，确保数据安全性
     *   重要性: 防止跨程序的uniform访问错误
     *   验证: 每次uniform操作都会检查此程序是否为当前激活程序
     *   生命周期: location的有效性与program的生命周期绑定
     *   内存管理: 当program被删除时，相关的location也会失效
     *
     * @param {Object} uniformInfo - uniform变量的详细信息对象
     *   结构: {
     *     name: string,    // uniform变量名称
     *     type: GLenum,    // WebGL类型常量
     *     size: number     // 数组大小(非数组为1)
     *   }
     *   来源: gl.getActiveUniform() 返回的 WebGLActiveInfo 对象
     *   用途: 提供location创建所需的所有元数据
     *   验证: 确保类型匹配和名称正确性
     *
     * @param {number} slotIndex - GPU内存中的槽位索引
     *   作用: 指定uniform在GPU内存中的起始位置
     *   范围: 0 到 maxUniformSlots-1 (硬件相关，通常0-1023+)
     *   计算: 由UniformLocationAllocator根据类型和大小计算
     *   内存映射: 绝对地址 = program.baseAddress + slotIndex × 16
     *   对齐: 所有uniform按16字节边界对齐(vec4对齐)
     *   连续性: 数组和矩阵类型占用连续的槽位
     */
    constructor(program, uniformInfo, slotIndex) {
        // ========================================
        // 第一部分: 基本标识信息
        // ========================================

        /**
         * 关联的着色器程序对象引用
         * 作用: 建立location与program的强绑定关系
         * 安全性: 防止location被用于错误的程序
         * 验证: uniform操作时检查 gl.currentProgram === this.__program
         */
        this.__program = program;

        /**
         * uniform变量的完整名称
         * 格式:
         *   - 普通变量: "uTime", "uColor"
         *   - 数组元素: "uValues[0]", "uColors[1]"
         *   - 结构体成员: "uLight.position", "uMaterial.diffuse"
         *   - 结构体数组: "uLights[0].position", "uBones[5].matrix"
         * 用途: 调试信息、错误报告、location验证
         */
        this.__name = uniformInfo.name;

        /**
         * WebGL uniform类型常量
         * 决定因素:
         *   - 使用哪个gl.uniform*()函数传输数据
         *   - 占用多少个GPU内存槽位
         *   - 数据的内存布局和对齐方式
         * 常见类型及其特征:
         *   gl.FLOAT(5126): 4字节，使用gl.uniform1f()
         *   gl.FLOAT_VEC3(35665): 12字节(按16字节对齐)，使用gl.uniform3fv()
         *   gl.FLOAT_MAT4(35676): 64字节(4个槽位)，使用gl.uniformMatrix4fv()
         *   gl.SAMPLER_2D(35678): 纹理单元索引，使用gl.uniform1i()
         */
        this.__type = uniformInfo.type;

        /**
         * 数组大小
         * 值含义:
         *   1: 单个uniform变量
         *   >1: uniform数组，表示数组元素个数
         * 影响:
         *   - 总槽位数 = 单个类型槽位数 × size
         *   - 内存占用 = 槽位数 × 16字节
         * 注意: 即使是数组，每个元素仍需单独的location
         */
        this.__size = uniformInfo.size;

        // ========================================
        // 第二部分: GPU内存定位信息
        // ========================================

        /**
         * 在GPU内存中的起始槽位索引
         * 计算: 由UniformLocationAllocator顺序分配
         * 范围: 0 到 maxUniformSlots-1 (硬件限制)
         * 用途: 计算uniform在GPU内存中的绝对地址
         * 重要性: 这是GPU硬件直接访问的内存位置标识
         */
        this.__slotIndex = slotIndex;

        /**
         * 该uniform占用的总槽位数量
         * 计算: 基于类型和数组大小动态计算
         * 示例:
         *   float: 1个槽位
         *   vec3: 1个槽位 (虽然只用12字节，但按16字节对齐)
         *   mat4: 4个槽位 (4×4×4=64字节)
         *   float[5]: 5个槽位
         *   mat4[2]: 8个槽位 (2×4=8)
         */
        this.__slotCount = this.calculateSlotCount();

        /**
         * 相对于程序基地址的内存偏移量(字节)
         * 计算: slotIndex × 16 (每个槽位16字节)
         * 用途: 配合程序基地址计算绝对内存地址
         * 公式: 绝对地址 = program.uniformBaseAddress + this.__memoryOffset
         * 对齐: 所有uniform按16字节(vec4)边界对齐
         */
        this.__memoryOffset = slotIndex * 16;

        // ========================================
        // 第三部分: 调试和统计信息
        // ========================================

        /**
         * 全局唯一标识符
         * 格式: "programId_uniformName_timestamp_random"
         * 用途: 调试、性能分析、错误追踪
         * 唯一性: 即使同名uniform在不同程序中也有不同ID
         */
        this.__id = this.generateUniqueId();

        /**
         * location创建时间戳
         * 用途: 性能分析、生命周期追踪
         * 精度: 毫秒级，基于performance.now()
         */
        this.__creationTime = performance.now();

        /**
         * uniform访问次数统计
         * 用途: 性能优化、热点分析
         * 更新: 每次调用gl.uniform*()时递增
         */
        this.__accessCount = 0;
    }

    /**
     * 计算该uniform占用的GPU内存槽位数量
     *
     * @returns {number} 槽位数量
     *   计算公式: 单个类型槽位数 × 数组大小
     *   用途: 内存分配、地址计算、性能优化
     *
     * 槽位分配规则:
     *   - 标量和向量类型: 1个槽位(16字节对齐)
     *     float, vec2, vec3, vec4, int, ivec2-4, bool, bvec2-4 = 1槽位
     *   - 矩阵类型: 按列数分配槽位
     *     mat2 = 2槽位(32字节), mat3 = 3槽位(48字节), mat4 = 4槽位(64字节)
     *   - 纹理采样器: 1个槽位(存储纹理单元索引)
     *     sampler2D, samplerCube = 1槽位
     *   - 数组类型: 基础槽位数 × 数组长度
     *     float[5] = 5槽位, vec3[3] = 3槽位, mat4[2] = 8槽位
     */
    calculateSlotCount() {
        // GPU内存槽位分配表 - 基于WebGL规范和硬件对齐要求
        const baseSlots = {
            // 浮点数类型 - 所有向量类型都按vec4对齐(16字节=1槽位)
            [gl.FLOAT]: 1, // float: 4字节 → 1槽位
            [gl.FLOAT_VEC2]: 1, // vec2: 8字节 → 1槽位(浪费8字节)
            [gl.FLOAT_VEC3]: 1, // vec3: 12字节 → 1槽位(浪费4字节)
            [gl.FLOAT_VEC4]: 1, // vec4: 16字节 → 1槽位(完全利用)

            // 整数类型 - 与浮点数相同的对齐规则
            [gl.INT]: 1, // int: 4字节 → 1槽位
            [gl.INT_VEC2]: 1, // ivec2: 8字节 → 1槽位
            [gl.INT_VEC3]: 1, // ivec3: 12字节 → 1槽位
            [gl.INT_VEC4]: 1, // ivec4: 16字节 → 1槽位

            // 布尔类型 - 在GPU中作为整数存储
            [gl.BOOL]: 1, // bool: 4字节(存储为int) → 1槽位
            [gl.BOOL_VEC2]: 1, // bvec2: 8字节 → 1槽位
            [gl.BOOL_VEC3]: 1, // bvec3: 12字节 → 1槽位
            [gl.BOOL_VEC4]: 1, // bvec4: 16字节 → 1槽位

            // 矩阵类型 - 按列存储，每列占用1个槽位
            [gl.FLOAT_MAT2]: 2, // mat2: 2列×2行×4字节 = 16字节×2 = 2槽位
            [gl.FLOAT_MAT3]: 3, // mat3: 3列×3行×4字节 = 16字节×3 = 3槽位(每列按vec4对齐)
            [gl.FLOAT_MAT4]: 4, // mat4: 4列×4行×4字节 = 16字节×4 = 4槽位

            // 纹理采样器 - 存储纹理单元索引(整数)
            [gl.SAMPLER_2D]: 1, // sampler2D: 纹理单元索引 → 1槽位
            [gl.SAMPLER_CUBE]: 1, // samplerCube: 立方体纹理单元索引 → 1槽位
        };

        // 计算总槽位数: 基础槽位数 × 数组大小
        // 如果类型未知，默认分配1个槽位(向前兼容)
        return (baseSlots[this.__type] || 1) * this.__size;
    }

    /**
     * 生成全局唯一的location标识符
     *
     * @returns {string} 唯一ID字符串
     *   格式: "programId_uniformName_timestamp_randomValue"
     *   用途: 调试追踪、性能分析、错误报告
     *   唯一性: 即使相同名称的uniform在不同程序中也有不同ID
     *
     * ID组成部分:
     *   - programId: 程序的唯一标识
     *   - uniformName: uniform变量名称
     *   - timestamp: 创建时间戳(毫秒)
     *   - randomValue: 随机数(防止时间戳冲突)
     */
    generateUniqueId() {
        return `${this.__program.__id}_${this.__name}_${Date.now()}_${Math.random()}`;
    }

    /**
     * 获取uniform在GPU内存中的绝对地址
     *
     * @returns {number} GPU内存绝对地址
     *   计算公式: 程序基地址 + 槽位偏移量
     *   用途: GPU硬件直接访问、内存调试
     *
     * 地址计算详解:
     *   - 程序基地址: 每个程序在GPU中分配的独立内存区域起始地址
     *   - 槽位偏移量: slotIndex × 16字节(vec4对齐)
     *   - 绝对地址: GPU可以直接访问的物理内存位置
     *
     * 示例:
     *   程序基地址: 0x10000000
     *   槽位索引: 5
     *   绝对地址: 0x10000000 + 5×16 = 0x10000050
     */
    getAbsoluteAddress() {
        return this.__program.uniformBaseAddress + this.__memoryOffset;
    }

    /**
     * 验证location对象的有效性
     *
     * @returns {boolean} 是否有效
     *   true: location可以安全使用
     *   false: location已失效，使用会导致错误
     *
     * 验证条件:
     *   1. 关联的程序对象存在且未被删除
     *   2. 程序已成功链接(gl.linkProgram成功)
     *   3. 槽位索引有效(>=0)
     *
     * 失效情况:
     *   - 程序被gl.deleteProgram()删除
     *   - 程序链接失败
     *   - location创建时出现错误
     */
    isValid() {
        return this.__program && this.__program.isLinked && this.__slotIndex >= 0;
    }

    /**
     * 验证location是否属于当前激活的程序
     *
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @returns {boolean} 是否属于当前程序
     *   true: 可以安全设置uniform值
     *   false: 会导致WebGL错误，需要先调用gl.useProgram()
     *
     * 安全检查:
     *   - 防止跨程序的uniform访问错误
     *   - 确保uniform数据设置到正确的程序
     *   - 避免GPU状态混乱
     *
     * 使用场景:
     *   - uniform设置前的安全验证
     *   - 调试工具中的状态检查
     *   - 性能优化中的批处理验证
     */
    belongsToCurrentProgram(gl) {
        return gl.currentProgram === this.__program;
    }

    /**
     * 获取location的完整调试信息
     *
     * @returns {Object} 调试信息对象
     *   包含location的所有关键属性和计算值
     *   用途: 开发调试、性能分析、错误诊断
     *
     * 返回对象结构:
     *   {
     *     name: string,           // uniform变量名称
     *     type: string,           // 人类可读的类型名称
     *     size: number,           // 数组大小
     *     slotIndex: number,      // 起始槽位索引
     *     slotCount: number,      // 占用槽位数量
     *     memoryOffset: number,   // 内存偏移量(字节)
     *     absoluteAddress: number,// GPU绝对内存地址
     *     accessCount: number,    // 访问次数统计
     *     programId: string       // 关联程序ID
     *   }
     */
    getDebugInfo() {
        return {
            // 基本标识信息
            name: this.__name, // uniform变量完整名称
            type: this.getTypeName(this.__type), // 转换为可读的类型名称
            size: this.__size, // 数组大小(非数组为1)

            // 内存布局信息
            slotIndex: this.__slotIndex, // GPU内存起始槽位
            slotCount: this.__slotCount, // 占用的槽位总数
            memoryOffset: this.__memoryOffset, // 相对程序基地址的偏移
            absoluteAddress: this.getAbsoluteAddress(), // GPU绝对内存地址

            // 统计和关联信息
            accessCount: this.__accessCount, // uniform被访问的次数
            programId: this.__program.__id, // 关联的程序唯一ID
        };
    }

    /**
     * 将WebGL类型常量转换为人类可读的类型名称
     *
     * @param {GLenum} type - WebGL类型常量
     * @returns {string} 可读的类型名称
     *   用途: 调试输出、错误报告、开发工具显示
     *   格式: GLSL着色器语言中的类型名称
     *
     * 支持的类型映射:
     *   - 浮点数类型: float, vec2, vec3, vec4
     *   - 整数类型: int, ivec2, ivec3, ivec4
     *   - 布尔类型: bool, bvec2, bvec3, bvec4
     *   - 矩阵类型: mat2, mat3, mat4
     *   - 纹理采样器: sampler2D, samplerCube
     *   - 未知类型: "unknown(常量值)"
     */
    getTypeName(type) {
        // WebGL类型常量到GLSL类型名称的映射表
        const typeNames = {
            // 浮点数标量和向量类型
            [gl.FLOAT]: 'float', // 5126 → float
            [gl.FLOAT_VEC2]: 'vec2', // 35664 → vec2
            [gl.FLOAT_VEC3]: 'vec3', // 35665 → vec3
            [gl.FLOAT_VEC4]: 'vec4', // 35666 → vec4

            // 整数标量和向量类型
            [gl.INT]: 'int', // 5124 → int
            [gl.INT_VEC2]: 'ivec2', // 35667 → ivec2
            [gl.INT_VEC3]: 'ivec3', // 35668 → ivec3
            [gl.INT_VEC4]: 'ivec4', // 35669 → ivec4

            // 布尔标量和向量类型(在GPU中存储为整数)
            [gl.BOOL]: 'bool', // 35670 → bool
            [gl.BOOL_VEC2]: 'bvec2', // 35671 → bvec2
            [gl.BOOL_VEC3]: 'bvec3', // 35672 → bvec3
            [gl.BOOL_VEC4]: 'bvec4', // 35673 → bvec4

            // 浮点数矩阵类型(按列主序存储)
            [gl.FLOAT_MAT2]: 'mat2', // 35674 → mat2 (2×2矩阵)
            [gl.FLOAT_MAT3]: 'mat3', // 35675 → mat3 (3×3矩阵)
            [gl.FLOAT_MAT4]: 'mat4', // 35676 → mat4 (4×4矩阵)

            // 纹理采样器类型
            [gl.SAMPLER_2D]: 'sampler2D', // 35678 → sampler2D (2D纹理)
            [gl.SAMPLER_CUBE]: 'samplerCube', // 35680 → samplerCube (立方体纹理)
        };

        // 返回对应的类型名称，如果未知则显示原始常量值
        // 这有助于调试新的或不支持的WebGL类型
        return typeNames[type] || `unknown(${type})`;
    }
}
```

### 3. 特殊情况处理

#### 数组 Uniform 的 Location 分配

```javascript
// 数组 uniform 的特殊处理
const fragmentShader = `
    uniform float uValues[5];     // 数组 uniform
    uniform vec3 uColors[3];      // 向量数组
    uniform mat4 uMatrices[2];    // 矩阵数组
`;

// 数组 uniform 的 location 查询
function getArrayUniformLocations(gl, program, arrayName, arraySize) {
    const locations = [];

    for (let i = 0; i < arraySize; i++) {
        // 数组元素的命名规则：arrayName[index]
        const elementName = `${arrayName}[${i}]`;
        const location = gl.getUniformLocation(program, elementName);

        if (location) {
            locations.push({
                index: i,
                name: elementName,
                location: location,
            });
        }
    }

    return locations;
}

// 使用示例
const valueLocations = getArrayUniformLocations(gl, program, 'uValues', 5);
const colorLocations = getArrayUniformLocations(gl, program, 'uColors', 3);
const matrixLocations = getArrayUniformLocations(gl, program, 'uMatrices', 2);

// 设置数组 uniform 值
valueLocations.forEach((item, index) => {
    gl.uniform1f(item.location, values[index]);
});

colorLocations.forEach((item, index) => {
    gl.uniform3fv(item.location, colors[index]);
});

matrixLocations.forEach((item, index) => {
    gl.uniformMatrix4fv(item.location, false, matrices[index]);
});
```

#### 结构体 Uniform 的 Location 分配

```javascript
// 结构体 uniform 的处理
const fragmentShader = `
    struct Light {
        vec3 position;
        vec3 color;
        float intensity;
    };

    uniform Light uLight;           // 单个结构体
    uniform Light uLights[3];       // 结构体数组
`;

// 结构体成员的 location 查询
function getStructUniformLocations(gl, program, structName, structMembers) {
    const locations = {};

    structMembers.forEach((member) => {
        const memberName = `${structName}.${member}`;
        const location = gl.getUniformLocation(program, memberName);

        if (location) {
            locations[member] = location;
        }
    });

    return locations;
}

// 结构体数组的 location 查询
function getStructArrayUniformLocations(gl, program, arrayName, arraySize, structMembers) {
    const locations = [];

    for (let i = 0; i < arraySize; i++) {
        const structLocations = {};

        structMembers.forEach((member) => {
            const memberName = `${arrayName}[${i}].${member}`;
            const location = gl.getUniformLocation(program, memberName);

            if (location) {
                structLocations[member] = location;
            }
        });

        locations.push(structLocations);
    }

    return locations;
}

// 使用示例
const lightMembers = ['position', 'color', 'intensity'];
const lightLocation = getStructUniformLocations(gl, program, 'uLight', lightMembers);
const lightsLocations = getStructArrayUniformLocations(gl, program, 'uLights', 3, lightMembers);

// 设置结构体 uniform 值
gl.uniform3fv(lightLocation.position, [1.0, 2.0, 3.0]);
gl.uniform3fv(lightLocation.color, [1.0, 1.0, 1.0]);
gl.uniform1f(lightLocation.intensity, 0.8);

// 设置结构体数组 uniform 值
lightsLocations.forEach((light, index) => {
    gl.uniform3fv(light.position, lightData[index].position);
    gl.uniform3fv(light.color, lightData[index].color);
    gl.uniform1f(light.intensity, lightData[index].intensity);
});
```

## 🎛️ 高级 Location 管理策略

### 1. 智能 Location 缓存系统

```javascript
class SmartLocationCache {
    constructor() {
        this.cache = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            invalidations: 0,
        };
    }

    getUniformLocation(gl, program, name) {
        const key = this.generateKey(program, name, 'uniform');

        if (this.cache.has(key)) {
            this.stats.hits++;
            return this.cache.get(key);
        }

        this.stats.misses++;
        const location = gl.getUniformLocation(program, name);

        if (location) {
            this.cache.set(key, location);
        }

        return location;
    }

    getAttributeLocation(gl, program, name) {
        const key = this.generateKey(program, name, 'attribute');

        if (this.cache.has(key)) {
            this.stats.hits++;
            return this.cache.get(key);
        }

        this.stats.misses++;
        const location = gl.getAttribLocation(program, name);

        if (location !== -1) {
            this.cache.set(key, location);
        }

        return location;
    }

    generateKey(program, name, type) {
        return `${program.__id || program}_${type}_${name}`;
    }

    invalidateProgram(program) {
        const programId = program.__id || program;
        const keysToDelete = [];

        for (const key of this.cache.keys()) {
            if (key.startsWith(programId)) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach((key) => this.cache.delete(key));
        this.stats.invalidations += keysToDelete.length;
    }

    getCacheStats() {
        const total = this.stats.hits + this.stats.misses;
        return {
            ...this.stats,
            hitRate: total > 0 ? ((this.stats.hits / total) * 100).toFixed(2) + '%' : '0%',
            cacheSize: this.cache.size,
        };
    }

    clear() {
        this.cache.clear();
        this.stats = { hits: 0, misses: 0, invalidations: 0 };
    }
}

// 全局缓存实例
const locationCache = new SmartLocationCache();
```

### 2. Location 预分配和批量管理

```javascript
class LocationPreallocator {
    constructor(gl) {
        this.gl = gl;
        this.programLocations = new Map();
    }

    preallocateProgram(program, uniformNames = [], attributeNames = []) {
        const programId = program.__id || this.generateProgramId(program);

        const locations = {
            uniforms: new Map(),
            attributes: new Map(),
            metadata: {
                programId,
                createdAt: Date.now(),
                uniformCount: 0,
                attributeCount: 0,
            },
        };

        // 预分配指定的 uniform locations
        uniformNames.forEach((name) => {
            const location = this.gl.getUniformLocation(program, name);
            if (location) {
                locations.uniforms.set(name, location);
                locations.metadata.uniformCount++;
            }
        });

        // 预分配指定的 attribute locations
        attributeNames.forEach((name) => {
            const location = this.gl.getAttribLocation(program, name);
            if (location !== -1) {
                locations.attributes.set(name, location);
                locations.metadata.attributeCount++;
            }
        });

        // 如果没有指定名称，则自动发现所有 locations
        if (uniformNames.length === 0) {
            this.discoverAllUniforms(program, locations);
        }

        if (attributeNames.length === 0) {
            this.discoverAllAttributes(program, locations);
        }

        this.programLocations.set(programId, locations);
        return locations;
    }

    discoverAllUniforms(program, locations) {
        const uniformCount = this.gl.getProgramParameter(program, this.gl.ACTIVE_UNIFORMS);

        for (let i = 0; i < uniformCount; i++) {
            const info = this.gl.getActiveUniform(program, i);
            const location = this.gl.getUniformLocation(program, info.name);

            if (location) {
                locations.uniforms.set(info.name, location);
                locations.metadata.uniformCount++;
            }
        }
    }

    discoverAllAttributes(program, locations) {
        const attributeCount = this.gl.getProgramParameter(program, this.gl.ACTIVE_ATTRIBUTES);

        for (let i = 0; i < attributeCount; i++) {
            const info = this.gl.getActiveAttrib(program, i);
            const location = this.gl.getAttribLocation(program, info.name);

            if (location !== -1) {
                locations.attributes.set(info.name, location);
                locations.metadata.attributeCount++;
            }
        }
    }

    getUniformLocation(program, name) {
        const programId = program.__id || this.generateProgramId(program);
        const locations = this.programLocations.get(programId);

        if (locations && locations.uniforms.has(name)) {
            return locations.uniforms.get(name);
        }

        // 如果没有预分配，则动态查询
        const location = this.gl.getUniformLocation(program, name);

        // 将动态查询的结果添加到缓存
        if (location && locations) {
            locations.uniforms.set(name, location);
            locations.metadata.uniformCount++;
        }

        return location;
    }

    getAttributeLocation(program, name) {
        const programId = program.__id || this.generateProgramId(program);
        const locations = this.programLocations.get(programId);

        if (locations && locations.attributes.has(name)) {
            return locations.attributes.get(name);
        }

        // 如果没有预分配，则动态查询
        const location = this.gl.getAttribLocation(program, name);

        // 将动态查询的结果添加到缓存
        if (location !== -1 && locations) {
            locations.attributes.set(name, location);
            locations.metadata.attributeCount++;
        }

        return location;
    }

    generateProgramId(program) {
        if (!program.__id) {
            program.__id = `program_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        return program.__id;
    }

    getProgramStats(program) {
        const programId = program.__id || this.generateProgramId(program);
        const locations = this.programLocations.get(programId);

        if (!locations) {
            return null;
        }

        return {
            ...locations.metadata,
            uniformNames: Array.from(locations.uniforms.keys()),
            attributeNames: Array.from(locations.attributes.keys()),
        };
    }

    cleanup(program) {
        const programId = program.__id || this.generateProgramId(program);
        this.programLocations.delete(programId);
    }
}
```

## 📝 总结

WebGL 的 location 生成机制是一个复杂但有序的过程：

1. **编译阶段**: 解析着色器中的变量声明
2. **链接阶段**: 分配具体的内存位置
3. **查询阶段**: 通过 API 获取 location 对象
4. **使用阶段**: 通过 location 进行数据传输

理解这个机制有助于：

-   优化 WebGL 应用性能
-   避免 location 相关的错误
-   合理规划 GPU 内存使用
-   实现高效的渲染管道

关键要点：

-   Location 在链接时确定，运行时不变
-   不同程序的 location 可能重复但指向不同内存
-   合理的 location 管理是 WebGL 优化的重要环节
-   数组和结构体 uniform 需要特殊的 location 处理策略
-   智能缓存和预分配可以显著提升性能
