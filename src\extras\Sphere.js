import { Geometry } from '../core/Geometry.js';
import { Vec3 } from '../math/Vec3.js';

/**
 * 球体几何体类
 * 用于创建可配置的球体网格
 */
export class Sphere extends Geometry {
    /**
     * 创建一个球体几何体
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} [options] - 配置选项
     * @param {Number} [options.radius=0.5] - 球体半径
     * @param {Number} [options.widthSegments=16] - 水平方向的分段数
     * @param {Number} [options.heightSegments=Math.ceil(widthSegments*0.5)] - 垂直方向的分段数
     * @param {Number} [options.phiStart=0] - 水平起始角度
     * @param {Number} [options.phiLength=Math.PI*2] - 水平角度跨度
     * @param {Number} [options.thetaStart=0] - 垂直起始角度
     * @param {Number} [options.thetaLength=Math.PI] - 垂直角度跨度
     * @param {Object} [options.attributes={}] - 自定义几何体属性
     */
    constructor(
        gl,
        { radius = 0.5, widthSegments = 16, heightSegments = Math.ceil(widthSegments * 0.5), phiStart = 0, phiLength = Math.PI * 2, thetaStart = 0, thetaLength = Math.PI, attributes = {} } = {}
    ) {
        // 简化变量名
        const wSegs = widthSegments;
        const hSegs = heightSegments;
        const pStart = phiStart;
        const pLength = phiLength;
        const tStart = thetaStart;
        const tLength = thetaLength;

        // 计算顶点和索引数量
        const num = (wSegs + 1) * (hSegs + 1); // 顶点总数
        const numIndices = wSegs * hSegs * 6; // 索引总数（每个四边形由2个三角形组成）

        // 创建数据数组
        const position = new Float32Array(num * 3); // 顶点位置
        const normal = new Float32Array(num * 3); // 顶点法线
        const uv = new Float32Array(num * 2); // 纹理坐标
        // 根据顶点数选择合适的索引类型
        const index = num > 65536 ? new Uint32Array(numIndices) : new Uint16Array(numIndices);

        // 初始化计数器
        let i = 0; // 顶点计数器
        let iv = 0; // 顶点索引计数器
        let ii = 0; // 索引计数器
        let te = tStart + tLength; // 垂直结束角度
        const grid = []; // 存储每个顶点的索引

        // 临时法线向量
        let n = new Vec3();

        // 生成顶点
        for (let iy = 0; iy <= hSegs; iy++) {
            let vRow = []; // 当前行的顶点索引
            let v = iy / hSegs; // 垂直纹理坐标 [0, 1]

            for (let ix = 0; ix <= wSegs; ix++, i++) {
                let u = ix / wSegs; // 水平纹理坐标 [0, 1]

                // 计算球面坐标
                // 球面参数方程：
                // x = -r * cos(φ) * sin(θ)
                // y = r * cos(θ)
                // z = r * sin(φ) * sin(θ)
                // 其中 φ 是水平角度，θ 是垂直角度
                let x = -radius * Math.cos(pStart + u * pLength) * Math.sin(tStart + v * tLength);
                let y = radius * Math.cos(tStart + v * tLength);
                let z = radius * Math.sin(pStart + u * pLength) * Math.sin(tStart + v * tLength);

                // 设置顶点位置
                position[i * 3] = x;
                position[i * 3 + 1] = y;
                position[i * 3 + 2] = z;

                // 计算法线（球体的法线就是从中心到表面的归一化向量）
                n.set(x, y, z).normalize();
                normal[i * 3] = n.x;
                normal[i * 3 + 1] = n.y;
                normal[i * 3 + 2] = n.z;

                // 设置纹理坐标
                uv[i * 2] = u;
                uv[i * 2 + 1] = 1 - v; // 翻转v坐标

                // 存储当前顶点的索引
                vRow.push(iv++);
            }

            // 将当前行添加到网格中
            grid.push(vRow);
        }

        // 生成索引（三角形）
        for (let iy = 0; iy < hSegs; iy++) {
            for (let ix = 0; ix < wSegs; ix++) {
                // 获取当前四边形的四个顶点索引
                let a = grid[iy][ix + 1]; // 右上
                let b = grid[iy][ix]; // 左上
                let c = grid[iy + 1][ix]; // 左下
                let d = grid[iy + 1][ix + 1]; // 右下

                // 创建两个三角形（一个四边形）
                // 跳过北极点的三角形（如果thetaStart > 0）
                if (iy !== 0 || tStart > 0) {
                    // 三角形1：右上-左上-右下
                    index[ii * 3] = a;
                    index[ii * 3 + 1] = b;
                    index[ii * 3 + 2] = d;
                    ii++;
                }
                // 跳过南极点的三角形（如果thetaLength < PI）
                if (iy !== hSegs - 1 || te < Math.PI) {
                    // 三角形2：左上-左下-右下
                    index[ii * 3] = b;
                    index[ii * 3 + 1] = c;
                    index[ii * 3 + 2] = d;
                    ii++;
                }
            }
        }

        // 设置几何体属性
        Object.assign(attributes, {
            position: { size: 3, data: position },
            normal: { size: 3, data: normal },
            uv: { size: 2, data: uv },
            index: { data: index },
        });

        // 调用父类构造函数
        super(gl, attributes);
    }
}
