import BaseSegment from './BaseSegment.js';
import { Vec3 } from '../../math/Vec3.js';
import { T_VALUES, C_VALUES } from './utils.js';

// 预先创建临时向量以避免重复创建对象
const tempVec3 = /* @__PURE__ */ new Vec3();

/**
 * 计算二次贝塞尔曲线上的点
 * @param {number} t - 参数值，范围[0..1]
 * @param {number} p0 - 起点坐标分量
 * @param {number} p1 - 控制点坐标分量
 * @param {number} p2 - 终点坐标分量
 * @returns {number} 曲线上点的坐标分量
 */
function quadraticBezier(t, p0, p1, p2) {
    const k = 1 - t; // 补数
    // 二次贝塞尔曲线公式: B(t) = (1-t)²P₀ + 2(1-t)tP₁ + t²P₂
    return k * k * p0 + 2 * k * t * p1 + t * t * p2;
}

/**
 * 计算二次贝塞尔曲线的导数（切线方向）
 * @param {number} t - 参数值，范围[0..1]
 * @param {number} p0 - 起点坐标分量
 * @param {number} p1 - 控制点坐标分量
 * @param {number} p2 - 终点坐标分量
 * @returns {number} 曲线在t处的导数分量
 */
function quadraticBezierDeriv(t, p0, p1, p2) {
    const k = 1 - t; // 补数
    // 二次贝塞尔曲线导数公式: B'(t) = 2(1-t)(P₁-P₀) + 2t(P₂-P₁)
    return 2 * k * (p1 - p0) + 2 * t * (p2 - p1);
}

/**
 * 二次贝塞尔曲线段类
 * 表示路径中的二次贝塞尔曲线段
 */
export default class QuadraticBezierSegment extends BaseSegment {
    /**
     * 创建一个二次贝塞尔曲线段
     * @param {Vec3} p0 - 起点
     * @param {Vec3} p1 - 控制点
     * @param {Vec3} p2 - 终点
     * @param {number} [tiltStart=0] - 起点的倾斜角度
     * @param {number} [tiltEnd=0] - 终点的倾斜角度
     */
    constructor(p0, p1, p2, tiltStart = 0, tiltEnd = 0) {
        super();
        this.p0 = p0; // 曲线起点
        this.p1 = p1; // 曲线控制点
        this.p2 = p2; // 曲线终点

        this.tiltStart = tiltStart; // 起点的倾斜角度
        this.tiltEnd = tiltEnd; // 终点的倾斜角度

        this._len = -1; // 初始化长度为-1，表示尚未计算
    }

    /**
     * 更新曲线段长度
     * 每次更改曲线的控制点后，必须调用此方法
     * 使用高斯-勒让德积分法计算曲线长度
     */
    updateLength() {
        // 算法来源: https://github.com/Pomax/bezierjs/blob/d19695f3cc3ce383cf38ce4643f467deca7edb92/src/utils.js#L265
        const z = 0.5; // 积分区间变换因子
        const len = T_VALUES.length; // 高斯-勒让德积分点数

        // 使用高斯-勒让德积分计算曲线长度
        let sum = 0;
        for (let i = 0, t; i < len; i++) {
            // 将积分点从[-1,1]映射到[0,1]
            t = z * T_VALUES[i] + z;
            // 累加加权的导数长度
            sum += C_VALUES[i] * this.getDerivativeAt(t, tempVec3).len();
        }

        // 计算最终长度
        this._len = z * sum;
    }

    /**
     * 获取曲线上相对位置的点
     * @param {number} t - 参数值，范围[0..1]
     * @param {Vec3} [out=new Vec3()] - 可选的输出向量
     * @returns {Vec3} 曲线上的点
     */
    getPointAt(t, out = new Vec3()) {
        // 分别计算x、y、z坐标
        out.x = quadraticBezier(t, this.p0.x, this.p1.x, this.p2.x);
        out.y = quadraticBezier(t, this.p0.y, this.p1.y, this.p2.y);
        out.z = quadraticBezier(t, this.p0.z, this.p1.z, this.p2.z);
        return out;
    }

    /**
     * 获取曲线在参数t处的导数向量
     * @param {number} t - 参数值，范围[0..1]
     * @param {Vec3} [out=new Vec3()] - 可选的输出向量
     * @returns {Vec3} 导数向量（非归一化）
     */
    getDerivativeAt(t, out = new Vec3()) {
        // 分别计算x、y、z方向的导数
        out.x = quadraticBezierDeriv(t, this.p0.x, this.p1.x, this.p2.x);
        out.y = quadraticBezierDeriv(t, this.p0.y, this.p1.y, this.p2.y);
        out.z = quadraticBezierDeriv(t, this.p0.z, this.p1.z, this.p2.z);
        return out;
    }

    /**
     * 获取参数t处的单位切向量
     * @param {number} t - 参数值，范围[0..1]
     * @param {Vec3} [out=new Vec3()] - 可选的输出向量
     * @returns {Vec3} 单位切向量
     */
    getTangentAt(t, out = new Vec3()) {
        // 获取导数向量并归一化得到切向量
        return this.getDerivativeAt(t, out).normalize();
    }

    /**
     * 获取曲线的最后一个点（终点）
     * @returns {Vec3} 曲线的终点
     */
    lastPoint() {
        return this.p2;
    }
}
