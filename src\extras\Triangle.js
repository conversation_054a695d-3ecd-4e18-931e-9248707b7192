import { Geometry } from '../core/Geometry.js';

/**
 * 三角形几何体类
 * 创建一个覆盖整个屏幕的大三角形，通常用于全屏渲染
 */
export class Triangle extends Geometry {
    /**
     * 创建一个三角形几何体
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} [options] - 配置选项
     * @param {Object} [options.attributes={}] - 自定义几何体属性
     */
    constructor(gl, { attributes = {} } = {}) {
        // 设置几何体属性
        Object.assign(attributes, {
            // 位置坐标：创建一个大三角形，覆盖整个标准化设备坐标（NDC）空间
            // 三个顶点：(-1,-1), (3,-1), (-1,3)
            // 这个三角形比标准NDC空间（-1到1）大，确保能覆盖整个屏幕
            position: { size: 2, data: new Float32Array([-1, -1, 3, -1, -1, 3]) },

            // 纹理坐标：对应于位置坐标
            // 三个纹理坐标：(0,0), (2,0), (0,2)
            uv: { size: 2, data: new Float32Array([0, 0, 2, 0, 0, 2]) },
        });

        // 调用父类构造函数
        super(gl, attributes);
    }
}
