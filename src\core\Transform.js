/**
 * @file 3D变换与场景图核心实现 - 现代3D图形的空间变换基础
 *
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📚 3D变换学习指南
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 🎯 什么是3D变换？
 * 3D变换是计算机图形学的核心概念，它定义了对象在3D空间中的：
 * • 📍 位置（Position）- 对象在世界坐标系中的位置
 * • 🔄 旋转（Rotation）- 对象的朝向和姿态
 * • 📏 缩放（Scale）- 对象的大小变化
 *
 * 🏗️ 场景图概念：
 * 场景图是一种树形数据结构，用于组织3D场景中的对象：
 * • 🌳 父子关系：子对象继承父对象的变换
 * • 🔗 相对变换：子对象的变换相对于父对象
 * • 📊 层次管理：便于管理复杂的3D场景
 *
 * 🔬 数学基础：
 *
 * 1️⃣ 变换矩阵（Transformation Matrix）
 *    - 4x4矩阵，包含位置、旋转、缩放信息
 *    - 可以将一个坐标系变换到另一个坐标系
 *
 * 2️⃣ 四元数（Quaternion）
 *    - 表示旋转的数学工具，避免万向锁问题
 *    - 比欧拉角更稳定，插值更平滑
 *
 * 3️⃣ 欧拉角（Euler Angles）
 *    - 用X、Y、Z三个角度表示旋转
 *    - 直观易懂，但存在万向锁问题
 *
 * 📖 使用示例：
 * ```javascript
 * // 创建一个3D对象
 * const cube = new Transform();
 *
 * // 设置位置
 * cube.position.set(10, 5, 0);
 *
 * // 设置旋转（欧拉角，度数）
 * cube.rotation.set(45, 0, 90);
 *
 * // 设置缩放
 * cube.scale.set(2, 1, 1); // X轴放大2倍
 *
 * // 创建父子关系
 * const parent = new Transform();
 * parent.addChild(cube);
 *
 * // 更新变换矩阵
 * parent.updateMatrixWorld();
 * ```
 *
 * ⚡ 性能优化要点：
 * • 避免频繁的矩阵计算，使用缓存机制
 * • 合理使用matrixAutoUpdate标志
 * • 批量更新场景图中的变换
 *
 * 🚨 常见问题：
 * • 万向锁：使用四元数而非欧拉角进行旋转插值
 * • 精度损失：避免过深的场景图层次
 * • 性能问题：合理控制updateMatrixWorld的调用频率
 */

import { Vec3 } from '../math/Vec3.js';
import { Quat } from '../math/Quat.js';
import { Mat4 } from '../math/Mat4.js';
import { Euler } from '../math/Euler.js';

/**
 * 🎨 3D变换类 - 现代3D图形的空间变换核心
 *
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📋 类概述
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * Transform类是所有3D对象的基础类，提供：
 * • 🌍 空间变换管理（位置、旋转、缩放）
 * • 🌳 场景图层次结构
 * • 🔄 矩阵自动更新机制
 * • 📐 多种旋转表示方式
 *
 * 🏗️ 架构设计：
 * 采用组合模式实现场景图，支持任意深度的父子关系。
 * 使用观察者模式同步四元数和欧拉角的变化。
 *
 * 📚 学习建议：
 * 1. 理解3D坐标系和变换矩阵
 * 2. 掌握四元数与欧拉角的区别
 * 3. 学习场景图的概念和应用
 * 4. 了解矩阵组合的顺序和意义
 */
export class Transform {
    /**
     * 🚀 创建3D变换对象实例
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 构造函数详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 初始化一个完整的3D变换对象，包含：
     * • 🌳 场景图管理（父子关系）
     * • 📐 变换组件（位置、旋转、缩放）
     * • 🔄 矩阵管理（局部和世界矩阵）
     * • 🎯 自动同步机制（四元数↔欧拉角）
     *
     * 🔧 初始状态：
     * • 位置：(0, 0, 0) - 世界原点
     * • 旋转：无旋转 - 与世界坐标轴对齐
     * • 缩放：(1, 1, 1) - 原始大小
     * • 可见性：true - 默认可见
     *
     * 💡 使用示例：
     * ```javascript
     * // 创建基础变换对象
     * const transform = new Transform();
     *
     * // 创建带初始变换的对象
     * const cube = new Transform();
     * cube.position.set(5, 0, 0);
     * cube.rotation.y = Math.PI / 4; // 45度旋转
     * cube.scale.setScalar(2); // 整体放大2倍
     * ```
     */
    constructor() {
        // ═══════════════════════════════════════════════════════════════════════════════
        // 🌳 场景图层次结构
        // ═══════════════════════════════════════════════════════════════════════════════
        //
        // 💡 场景图是什么？
        // 场景图是一种树形数据结构，用于组织3D场景中对象的层次关系。
        // 子对象的变换相对于父对象，形成相对坐标系。

        this.parent = null; // 🔗 父对象引用，null表示根节点
        this.children = []; // 👶 子对象数组，存储所有直接子节点
        this.visible = true; // 👁️ 可见性标志，控制对象及其子对象的渲染

        // ═══════════════════════════════════════════════════════════════════════════════
        // 📐 变换矩阵系统
        // ═══════════════════════════════════════════════════════════════════════════════
        //
        // 💡 矩阵的作用：
        // • 局部矩阵：描述对象相对于父对象的变换
        // • 世界矩阵：描述对象在世界坐标系中的最终变换
        // • 世界矩阵 = 父世界矩阵 × 局部矩阵

        this.matrix = new Mat4(); // 🏠 局部变换矩阵（相对于父对象）
        this.worldMatrix = new Mat4(); // 🌍 世界变换矩阵（绝对坐标）
        this.matrixAutoUpdate = true; // ⚡ 自动更新标志，true时自动重计算矩阵
        this.worldMatrixNeedsUpdate = false; // 🔄 世界矩阵更新标志

        // ═══════════════════════════════════════════════════════════════════════════════
        // 🎯 变换组件（TRS - Translation, Rotation, Scale）
        // ═══════════════════════════════════════════════════════════════════════════════
        //
        // 💡 为什么分离TRS组件？
        // 分离的组件更直观易用，可以独立修改位置、旋转、缩放，
        // 最终组合成变换矩阵。

        this.position = new Vec3(); // 📍 位置向量 (x, y, z)
        this.quaternion = new Quat(); // 🔄 四元数旋转（避免万向锁）
        this.scale = new Vec3(1); // 📏 缩放向量 (sx, sy, sz)，默认无缩放
        this.rotation = new Euler(); // 📐 欧拉角旋转（直观的角度表示）
        this.up = new Vec3(0, 1, 0); // ⬆️ 上方向向量（用于lookAt计算）

        // ═══════════════════════════════════════════════════════════════════════════════
        // 🔄 旋转表示同步机制
        // ═══════════════════════════════════════════════════════════════════════════════
        //
        // 💡 为什么需要同步？
        // 四元数和欧拉角都可以表示旋转，但各有优缺点：
        // • 四元数：数学稳定，无万向锁，插值平滑
        // • 欧拉角：直观易懂，便于用户输入
        //
        // 🔧 同步机制：
        // 当任一表示方式改变时，自动更新另一种表示，保持一致性

        // 📐 → 🔄 欧拉角改变时，自动更新四元数
        this.rotation._target.onChange = () => this.quaternion.fromEuler(this.rotation, true);

        // 🔄 → 📐 四元数改变时，自动更新欧拉角
        this.quaternion._target.onChange = () => this.rotation.fromQuaternion(this.quaternion, undefined, true);
    }

    /**
     * 🔗 设置父对象关系
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 父子关系管理详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 场景图中的父子关系：
     * • 子对象的变换相对于父对象
     * • 父对象移动时，子对象跟随移动
     * • 父对象旋转时，子对象围绕父对象旋转
     * • 父对象缩放时，子对象也会缩放
     *
     * 🔄 双向通知机制：
     * 为了保持数据一致性，父子关系的建立需要双方都知晓：
     * • 子对象记录父对象引用
     * • 父对象的children数组包含子对象
     *
     * @param {Transform|null} parent - 要设置的父对象，null表示移除父子关系
     * @param {boolean} [notifyParent=true] - 是否通知父对象添加此子对象
     *
     * 💡 使用示例：
     * ```javascript
     * // 创建父子关系
     * const car = new Transform();
     * const wheel = new Transform();
     * wheel.setParent(car); // 车轮成为汽车的子对象
     *
     * // 移除父子关系
     * wheel.setParent(null); // 车轮独立存在
     *
     * // 转移父对象
     * const newCar = new Transform();
     * wheel.setParent(newCar); // 车轮转移到新汽车
     * ```
     */
    setParent(parent, notifyParent = true) {
        // 🔄 处理原有父子关系
        // 如果已有父对象且不是同一个，则从原父对象中移除
        if (this.parent && parent !== this.parent) this.parent.removeChild(this, false);

        // 🔗 建立新的父子关系
        this.parent = parent;

        // 📢 双向通知机制
        // 如果需要通知父对象且父对象存在，则让父对象添加此子对象
        if (notifyParent && parent) parent.addChild(this, false);
    }

    /**
     * 👶 添加子对象
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 子对象管理详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 添加子对象的意义：
     * • 建立层次结构，便于批量操作
     * • 实现相对变换，子对象跟随父对象
     * • 简化复杂模型的管理（如人体骨骼）
     *
     * 🎯 典型应用场景：
     * • 🚗 汽车模型：车身 → 车轮、车门、引擎盖
     * • 🏠 建筑模型：房屋 → 门、窗、屋顶
     * • 🤖 角色模型：身体 → 头部、手臂、腿部
     * • 🌳 自然场景：树干 → 树枝 → 树叶
     *
     * @param {Transform} child - 要添加的子对象
     * @param {boolean} [notifyChild=true] - 是否通知子对象设置此对象为父对象
     *
     * 💡 使用示例：
     * ```javascript
     * // 创建复杂模型
     * const robot = new Transform();
     * const head = new Transform();
     * const leftArm = new Transform();
     * const rightArm = new Transform();
     *
     * // 建立层次结构
     * robot.addChild(head);
     * robot.addChild(leftArm);
     * robot.addChild(rightArm);
     *
     * // 现在移动robot时，所有部件都会跟随
     * robot.position.x = 10; // 整个机器人向右移动
     * ```
     */
    addChild(child, notifyChild = true) {
        // 🔍 避免重复添加
        // 如果子对象不在子对象数组中，则添加
        if (!this.children.includes(child)) this.children.push(child);

        // 📢 双向通知机制
        // 如果需要通知子对象，则让子对象设置此对象为父对象
        if (notifyChild) child.setParent(this, false);
    }

    /**
     * 🗑️ 移除子对象
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 子对象移除详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 移除子对象的场景：
     * • 🎮 游戏中对象被销毁
     * • 🔄 重新组织场景结构
     * • 🎨 动态修改模型组成
     * • 🧹 清理不需要的对象
     *
     * ⚠️ 注意事项：
     * • 移除后子对象变为独立对象
     * • 子对象的世界变换可能发生变化
     * • 需要手动管理子对象的生命周期
     *
     * @param {Transform} child - 要移除的子对象
     * @param {boolean} [notifyChild=true] - 是否通知子对象移除父对象关系
     *
     * 💡 使用示例：
     * ```javascript
     * // 移除特定子对象
     * robot.removeChild(brokenArm);
     *
     * // 清空所有子对象
     * while(robot.children.length > 0) {
     *     robot.removeChild(robot.children[0]);
     * }
     *
     * // 转移子对象到新父对象
     * robot.removeChild(wheel);
     * newCar.addChild(wheel);
     * ```
     */
    removeChild(child, notifyChild = true) {
        // 🔍 查找并移除子对象
        const index = this.children.indexOf(child);
        if (index !== -1) this.children.splice(index, 1);

        // 📢 双向通知机制
        // 如果需要通知子对象，则让子对象设置父对象为null
        if (notifyChild) child.setParent(null, false);
    }

    /**
     * 🌍 更新世界变换矩阵
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 世界矩阵更新机制详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 什么是世界矩阵？
     * 世界矩阵描述对象在世界坐标系中的最终变换，它是从根节点到当前节点
     * 所有变换的累积结果。
     *
     * 🔄 计算公式：
     * • 根节点：世界矩阵 = 局部矩阵
     * • 子节点：世界矩阵 = 父世界矩阵 × 局部矩阵
     * • 递归传播：所有子节点都需要重新计算
     *
     * ⚡ 性能优化策略：
     * • 🎯 惰性更新：只在需要时才计算
     * • 🔄 级联更新：父节点变化时强制更新所有子节点
     * • 📊 批量处理：一次调用更新整个子树
     *
     * 🎯 调用时机：
     * • 渲染前必须调用，确保变换正确
     * • 碰撞检测前调用，获取准确位置
     * • 物理模拟前调用，同步变换状态
     *
     * @param {boolean} [force] - 是否强制更新子对象的世界矩阵
     *
     * 💡 使用示例：
     * ```javascript
     * // 场景渲染前更新所有变换
     * scene.updateMatrixWorld();
     *
     * // 获取对象在世界坐标系中的位置
     * const worldPosition = new Vec3();
     * object.updateMatrixWorld();
     * object.worldMatrix.getTranslation(worldPosition);
     *
     * // 强制更新（当手动修改矩阵时）
     * object.updateMatrixWorld(true);
     * ```
     */
    updateMatrixWorld(force) {
        // 🔄 自动更新局部矩阵
        // 如果设置了自动更新矩阵，则根据TRS组件重新计算局部矩阵
        if (this.matrixAutoUpdate) this.updateMatrix();

        // 🎯 条件更新机制
        // 只有在需要更新或强制更新时才进行计算，避免不必要的性能开销
        if (this.worldMatrixNeedsUpdate || force) {
            // 🌍 计算世界矩阵
            if (this.parent === null) {
                // 根节点：世界矩阵等于局部矩阵
                this.worldMatrix.copy(this.matrix);
            } else {
                // 子节点：世界矩阵 = 父世界矩阵 × 局部矩阵
                // 注意：矩阵乘法顺序很重要！
                this.worldMatrix.multiply(this.parent.worldMatrix, this.matrix);
            }

            // 🏁 重置更新标志
            this.worldMatrixNeedsUpdate = false;

            // 🔄 级联更新
            // 当前节点更新后，强制更新所有子节点
            force = true;
        }

        // 🌳 递归更新子树
        // 遍历所有子对象，递归更新它们的世界矩阵
        for (let i = 0, l = this.children.length; i < l; i++) {
            this.children[i].updateMatrixWorld(force);
        }
    }

    /**
     * 🔧 更新局部变换矩阵
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 局部矩阵组合详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 TRS组合顺序：
     * 变换矩阵的组合顺序是固定的：缩放 → 旋转 → 平移
     * Matrix = Translation × Rotation × Scale
     *
     * 🔬 数学原理：
     * 1. 首先应用缩放变换
     * 2. 然后应用旋转变换
     * 3. 最后应用平移变换
     *
     * ⚠️ 为什么顺序重要？
     * 矩阵乘法不满足交换律，不同的顺序会产生不同的结果：
     * • 先旋转后平移：对象绕原点旋转后移动
     * • 先平移后旋转：对象移动后绕新位置旋转
     *
     * ⚡ 性能考虑：
     * • 仅在TRS组件改变时调用
     * • 自动标记世界矩阵需要更新
     * • 避免频繁调用，使用matrixAutoUpdate控制
     *
     * 💡 使用示例：
     * ```javascript
     * // 修改变换组件后手动更新
     * object.position.x = 10;
     * object.rotation.y = Math.PI / 2;
     * object.updateMatrix(); // 重新计算局部矩阵
     *
     * // 禁用自动更新以提升性能
     * object.matrixAutoUpdate = false;
     * // 手动控制更新时机
     * object.updateMatrix();
     * ```
     */
    updateMatrix() {
        // 🎯 TRS组合
        // 使用四元数、位置和缩放组合成4x4变换矩阵
        // 内部按照 T × R × S 的顺序进行组合
        this.matrix.compose(this.quaternion, this.position, this.scale);

        // 🔄 标记更新需求
        // 局部矩阵改变后，世界矩阵也需要重新计算
        this.worldMatrixNeedsUpdate = true;
    }

    /**
     * 🌳 遍历场景图节点
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 场景图遍历详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 深度优先遍历：
     * 采用深度优先搜索（DFS）策略遍历整个场景图子树：
     * 1. 首先访问当前节点
     * 2. 然后递归访问所有子节点
     * 3. 支持提前终止机制
     *
     * 🎯 常用场景：
     * • 🔍 查找特定对象
     * • 🎨 批量修改属性
     * • 📊 收集统计信息
     * • 🧹 资源清理
     * • 🎮 游戏逻辑更新
     *
     * ⚡ 性能优化：
     * • 提前终止：回调返回true时停止遍历子树
     * • 避免深层递归：合理控制场景图深度
     * • 缓存结果：避免重复遍历
     *
     * @param {Function} callback - 对每个对象调用的回调函数
     *                              参数：(node) => boolean
     *                              返回true时停止遍历当前节点的子对象
     *
     * 💡 使用示例：
     * ```javascript
     * // 查找特定名称的对象
     * let foundObject = null;
     * scene.traverse((node) => {
     *     if (node.name === 'player') {
     *         foundObject = node;
     *         return true; // 找到后停止遍历
     *     }
     * });
     *
     * // 批量设置可见性
     * scene.traverse((node) => {
     *     if (node.type === 'enemy') {
     *         node.visible = false;
     *     }
     * });
     *
     * // 收集所有网格对象
     * const meshes = [];
     * scene.traverse((node) => {
     *     if (node.isMesh) {
     *         meshes.push(node);
     *     }
     * });
     * ```
     */
    traverse(callback) {
        // 🎯 访问当前节点
        // 如果回调返回true，则停止遍历子对象（提前终止机制）
        if (callback(this)) return;

        // 🌳 递归遍历子树
        // 深度优先遍历所有子对象
        for (let i = 0, l = this.children.length; i < l; i++) {
            this.children[i].traverse(callback);
        }
    }

    /**
     * 🔧 矩阵分解为TRS组件
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 矩阵分解详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 什么是矩阵分解？
     * 将4x4变换矩阵分解为平移（T）、旋转（R）、缩放（S）三个组件，
     * 这是矩阵组合的逆过程。
     *
     * 🔬 分解算法：
     * 1. 平移：直接提取矩阵的第4列（前3个元素）
     * 2. 缩放：计算前3列向量的长度
     * 3. 旋转：移除缩放后的矩阵转换为四元数
     *
     * 🎯 应用场景：
     * • 🔄 动画插值：分解关键帧矩阵进行插值
     * • 📊 数据导入：从外部格式导入变换数据
     * • 🔧 调试工具：检查变换组件的值
     * • 🎮 物理引擎：同步物理和渲染变换
     *
     * ⚠️ 注意事项：
     * • 非均匀缩放可能导致精度损失
     * • 负缩放会影响旋转的提取
     * • 剪切变换无法正确分解
     *
     * 💡 使用示例：
     * ```javascript
     * // 从外部矩阵数据恢复TRS
     * const externalMatrix = new Mat4().fromArray(matrixData);
     * object.matrix.copy(externalMatrix);
     * object.decompose(); // 分解为position, rotation, scale
     *
     * // 检查当前变换状态
     * object.decompose();
     * console.log('Position:', object.position);
     * console.log('Rotation:', object.rotation);
     * console.log('Scale:', object.scale);
     * ```
     */
    decompose() {
        // 🔧 矩阵分解
        // 将当前局部矩阵分解为四元数、位置和缩放
        // 注意：直接操作_target避免触发onChange回调
        this.matrix.decompose(this.quaternion._target, this.position, this.scale);

        // 🔄 同步旋转表示
        // 从四元数更新欧拉角，保持两种旋转表示的一致性
        this.rotation.fromQuaternion(this.quaternion);
    }

    /**
     * 👁️ 朝向目标点（LookAt变换）
     *
     * ═══════════════════════════════════════════════════════════════════════════════
     * 📖 LookAt变换详解
     * ═══════════════════════════════════════════════════════════════════════════════
     *
     * 💡 什么是LookAt？
     * LookAt是一种特殊的旋转变换，使对象的某个轴（通常是-Z轴）指向目标点。
     * 广泛用于相机控制、角色朝向、炮塔瞄准等场景。
     *
     * 🔬 数学原理：
     * 1. 计算从当前位置到目标的方向向量
     * 2. 结合上方向向量构建正交坐标系
     * 3. 将坐标系转换为旋转矩阵
     * 4. 提取旋转信息更新四元数和欧拉角
     *
     * 🎯 应用场景：
     * • 📷 相机控制：相机看向目标对象
     * • 🎮 角色AI：敌人面向玩家
     * • 🚀 导弹追踪：导弹朝向目标
     * • 🎨 广告牌：始终面向观察者
     * • 🔦 聚光灯：灯光照向目标
     *
     * @param {Vec3} target - 目标位置（世界坐标）
     * @param {boolean} [invert=false] - 是否反转朝向
     *                                   false: 对象的-Z轴指向目标（默认）
     *                                   true: 对象的+Z轴指向目标
     *
     * 💡 使用示例：
     * ```javascript
     * // 相机看向目标
     * const target = new Vec3(0, 0, 0);
     * camera.lookAt(target);
     *
     * // 角色面向敌人
     * player.lookAt(enemy.position);
     *
     * // 炮塔瞄准目标（反转朝向）
     * turret.lookAt(target, true);
     *
     * // 动态跟踪
     * function update() {
     *     spotlight.lookAt(player.position);
     * }
     * ```
     */
    lookAt(target, invert = false) {
        // 🎯 构建LookAt矩阵
        if (invert) {
            // 反转方向：对象的+Z轴指向目标
            // 参数顺序：eye(观察点), target(目标点), up(上方向)
            this.matrix.lookAt(this.position, target, this.up);
        } else {
            // 正常方向：对象的-Z轴指向目标（默认）
            // 交换eye和target的位置实现反向
            this.matrix.lookAt(target, this.position, this.up);
        }

        // 🔄 提取旋转信息
        // 从LookAt矩阵中提取旋转部分，更新四元数
        this.matrix.getRotation(this.quaternion._target);

        // 🔄 同步旋转表示
        // 从四元数更新欧拉角，保持一致性
        this.rotation.fromQuaternion(this.quaternion);
    }
}
