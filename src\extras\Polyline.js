// ========== 导入依赖模块 ==========
import { Geometry } from '../core/Geometry.js'; // 几何体类 - 管理顶点数据
import { Program } from '../core/Program.js'; // 着色器程序类 - 管理顶点和片段着色器
import { Mesh } from '../core/Mesh.js'; // 网格类 - 结合几何体和着色器程序
import { Vec2 } from '../math/Vec2.js'; // 2D向量类 - 用于分辨率等2D数据
import { Vec3 } from '../math/Vec3.js'; // 3D向量类 - 用于点位置
import { Color } from '../math/Color.js'; // 颜色类 - 用于线条颜色

// 预先创建临时向量以避免在计算中重复创建对象（性能优化）
const tmp = /* @__PURE__ */ new Vec3();

/**
 * Polyline 多段线类
 *
 * 这个类实现了WebGL中的高质量线条渲染。传统的WebGL只支持1像素宽的线条，
 * 而这个类通过几何体方法创建具有任意宽度的平滑线条。
 *
 * 核心原理：
 * 1. 将每个线条点扩展为两个顶点（线条的两侧）
 * 2. 使用三角形连接相邻的顶点对，形成线条的几何体
 * 3. 在顶点着色器中计算每个顶点的精确位置，实现平滑连接
 * 4. 支持可变宽度、颜色和其他视觉效果
 */
export class Polyline {
    /**
     * 创建一个多段线实例
     *
     * @param {WebGLRenderingContext} gl - WebGL渲染上下文
     * @param {Object} options - 配置选项
     * @param {Array<Vec3>} options.points - 线条的控制点数组，每个点定义线条路径
     * @param {String} [options.vertex=defaultVertex] - 自定义顶点着色器代码
     * @param {String} [options.fragment=defaultFragment] - 自定义片段着色器代码
     * @param {Object} [options.uniforms={}] - 着色器统一变量（如颜色、粗细等）
     * @param {Object} [options.attributes={}] - 自定义几何体属性
     */
    constructor(
        gl,
        {
            points, // Vec3数组 - 线条的控制点
            vertex = defaultVertex, // 顶点着色器 - 默认或自定义
            fragment = defaultFragment, // 片段着色器 - 默认或自定义
            uniforms = {}, // 统一变量 - 着色器参数
            attributes = {}, // 自定义属性 - 额外的顶点数据
        }
    ) {
        this.gl = gl; // 保存WebGL上下文引用
        this.points = points; // 保存控制点数组引用
        this.count = points.length; // 控制点数量

        // ========== 创建顶点数据缓冲区 ==========
        // 线条渲染需要将每个控制点扩展为两个顶点（线条的左右两侧）
        // 这样可以在着色器中通过偏移创建具有宽度的线条

        // 动态缓冲区 - 每帧可能更新的数据
        this.position = new Float32Array(this.count * 3 * 2); // 当前点位置 (x,y,z) * 2侧
        this.prev = new Float32Array(this.count * 3 * 2); // 前一个点位置 (x,y,z) * 2侧
        this.next = new Float32Array(this.count * 3 * 2); // 下一个点位置 (x,y,z) * 2侧

        // 静态缓冲区 - 初始化后不变的数据
        const side = new Float32Array(this.count * 1 * 2); // 线条侧面标识 (-1左侧, 1右侧)
        const uv = new Float32Array(this.count * 2 * 2); // 纹理坐标 (u,v) * 2侧
        const index = new Uint16Array((this.count - 1) * 3 * 2); // 三角形索引（每段线2个三角形）

        // ========== 填充静态缓冲区数据 ==========
        for (let i = 0; i < this.count; i++) {
            // 为每个控制点设置两个顶点的侧面标识
            // -1表示线条左侧，1表示线条右侧
            side.set([-1, 1], i * 2);

            // 计算沿线条方向的纹理坐标v（从0到1）
            const v = i / (this.count - 1);
            // 设置纹理坐标：
            // 左侧顶点: (u=0, v), 右侧顶点: (u=1, v)
            // u坐标区分线条的左右侧，v坐标表示沿线条的位置
            uv.set([0, v, 1, v], i * 4);

            // 最后一个点不需要创建三角形（没有下一个点）
            if (i === this.count - 1) continue;

            // ========== 创建三角形索引 ==========
            // 每段线条由两个三角形组成，连接当前点和下一个点的四个顶点
            const ind = i * 2; // 当前点的顶点索引起始位置

            // 顶点布局：
            // ind+0: 当前点左侧    ind+2: 下一点左侧
            // ind+1: 当前点右侧    ind+3: 下一点右侧

            // 三角形1：当前点左侧 -> 当前点右侧 -> 下一点左侧
            index.set([ind + 0, ind + 1, ind + 2], (ind + 0) * 3);
            // 三角形2：下一点左侧 -> 当前点右侧 -> 下一点右侧
            index.set([ind + 2, ind + 1, ind + 3], (ind + 1) * 3);
        }

        // ========== 创建几何体对象 ==========
        // 将所有顶点属性组合成一个几何体
        const geometry = (this.geometry = new Geometry(
            gl,
            Object.assign(attributes, {
                // 核心顶点属性
                position: { size: 3, data: this.position }, // 当前顶点位置 (x,y,z)
                prev: { size: 3, data: this.prev }, // 前一个点位置 (x,y,z) - 用于计算方向
                next: { size: 3, data: this.next }, // 下一个点位置 (x,y,z) - 用于计算方向
                side: { size: 1, data: side }, // 线条侧面 (-1或1) - 用于偏移
                uv: { size: 2, data: uv }, // 纹理坐标 (u,v) - 用于效果和材质
                index: { size: 1, data: index }, // 三角形索引 - 定义渲染顺序
            })
        ));

        // 根据当前控制点填充动态缓冲区数据
        this.updateGeometry();

        // ========== 设置默认统一变量 ==========
        // 如果用户没有提供这些统一变量，则创建默认值
        if (!uniforms.uResolution) this.resolution = uniforms.uResolution = { value: new Vec2() }; // 画布分辨率
        if (!uniforms.uDPR) this.dpr = uniforms.uDPR = { value: 1 }; // 设备像素比
        if (!uniforms.uThickness) this.thickness = uniforms.uThickness = { value: 1 }; // 线条粗细
        if (!uniforms.uColor) this.color = uniforms.uColor = { value: new Color('#000') }; // 线条颜色
        if (!uniforms.uMiter) this.miter = uniforms.uMiter = { value: 1 }; // 斜接限制（控制尖角处理）

        // 初始化尺寸相关的统一变量值
        this.resize();

        // ========== 创建着色器程序 ==========
        // 编译和链接顶点着色器与片段着色器
        const program = (this.program = new Program(gl, {
            vertex, // 顶点着色器代码
            fragment, // 片段着色器代码
            uniforms, // 统一变量
        }));

        // ========== 创建最终的网格对象 ==========
        // 网格结合了几何体和着色器程序，可以被渲染
        this.mesh = new Mesh(gl, { geometry, program });
    }

    /**
     * 更新几何体数据
     *
     * 这个方法是Polyline的核心，负责将控制点数组转换为可渲染的顶点数据。
     * 它计算每个顶点需要的position、prev、next属性，这些属性在顶点着色器中
     * 用于计算线条的方向和宽度。
     */
    updateGeometry() {
        this.points.forEach((p, i) => {
            // ========== 设置当前点位置 ==========
            // 每个控制点对应两个顶点（线条的左右两侧）
            // 两个顶点使用相同的position，在着色器中通过side属性区分
            p.toArray(this.position, i * 3 * 2); // 左侧顶点位置
            p.toArray(this.position, i * 3 * 2 + 3); // 右侧顶点位置

            // ========== 处理第一个点的前一个点 ==========
            if (!i) {
                // 第一个点没有真正的前一个点，需要人工计算一个虚拟的前一个点
                // 方法：将第一个点向第二个点的反方向延伸
                // 公式：virtualPrev = firstPoint + (firstPoint - secondPoint)
                tmp.copy(p) // 复制第一个点
                    .sub(this.points[i + 1]) // 减去第二个点，得到反向向量
                    .add(p); // 加上第一个点，得到延伸点
                tmp.toArray(this.prev, i * 3 * 2); // 左侧顶点的前一个点
                tmp.toArray(this.prev, i * 3 * 2 + 3); // 右侧顶点的前一个点
            } else {
                // ========== 设置非第一个点的下一个点引用 ==========
                // 当前点成为前一个点的"下一个点"
                p.toArray(this.next, (i - 1) * 3 * 2); // 前一个点左侧顶点的next
                p.toArray(this.next, (i - 1) * 3 * 2 + 3); // 前一个点右侧顶点的next
            }

            // ========== 处理最后一个点的下一个点 ==========
            if (i === this.points.length - 1) {
                // 最后一个点没有真正的下一个点，需要人工计算一个虚拟的下一个点
                // 方法：将最后一个点向倒数第二个点的反方向延伸
                // 公式：virtualNext = lastPoint + (lastPoint - secondLastPoint)
                tmp.copy(p) // 复制最后一个点
                    .sub(this.points[i - 1]) // 减去倒数第二个点，得到反向向量
                    .add(p); // 加上最后一个点，得到延伸点
                tmp.toArray(this.next, i * 3 * 2); // 左侧顶点的下一个点
                tmp.toArray(this.next, i * 3 * 2 + 3); // 右侧顶点的下一个点
            } else {
                // ========== 设置非最后一个点的前一个点引用 ==========
                // 当前点成为下一个点的"前一个点"
                p.toArray(this.prev, (i + 1) * 3 * 2); // 下一个点左侧顶点的prev
                p.toArray(this.prev, (i + 1) * 3 * 2 + 3); // 下一个点右侧顶点的prev
            }
        });

        // ========== 通知WebGL更新缓冲区 ==========
        // 标记这些属性的缓冲区数据已改变，需要重新上传到GPU
        this.geometry.attributes.position.needsUpdate = true;
        this.geometry.attributes.prev.needsUpdate = true;
        this.geometry.attributes.next.needsUpdate = true;
    }

    /**
     * 调整线条大小
     *
     * 当画布大小或设备像素比改变时调用此方法。
     * 它更新着色器中用于计算像素精确线条宽度的统一变量。
     * 只有在使用默认统一变量时才需要调用（如果手动管理则不需要）。
     */
    resize() {
        // 更新分辨率统一变量（如果使用默认的）
        if (this.resolution) {
            this.resolution.value.set(this.gl.canvas.width, this.gl.canvas.height);
        }
        // 更新设备像素比统一变量（如果使用默认的）
        if (this.dpr) {
            this.dpr.value = this.gl.renderer.dpr;
        }
    }
}

/**
 * 默认顶点着色器
 *
 * 这是Polyline类的核心着色器，实现了高质量的线条渲染算法。
 * 它处理线条的宽度、连接处的平滑过渡，以及像素精确的渲染。
 *
 * 主要功能：
 * 1. 将3D线条点转换为屏幕空间的宽线条
 * 2. 处理线条连接处的斜接（miter）效果
 * 3. 确保线条在不同分辨率和DPI下保持一致的像素宽度
 */
const defaultVertex = /* glsl */ `
    precision highp float;

    // ========== 顶点属性 ==========
    // 这些属性由Polyline类的几何体提供
    attribute vec3 position;  // 当前顶点的3D位置
    attribute vec3 next;      // 下一个控制点的3D位置（用于计算线条方向）
    attribute vec3 prev;      // 前一个控制点的3D位置（用于计算线条方向）
    attribute vec2 uv;        // 纹理坐标 (u: 0-1横向, v: 0-1沿线方向)
    attribute float side;     // 线条侧面标识 (-1: 左侧, 1: 右侧)

    // ========== 统一变量 ==========
    // 这些变量对所有顶点都相同
    uniform mat4 modelViewMatrix;     // 模型视图变换矩阵
    uniform mat4 projectionMatrix;    // 投影变换矩阵
    uniform vec2 uResolution;         // 画布分辨率 (宽度, 高度)
    uniform float uDPR;               // 设备像素比（用于高DPI屏幕）
    uniform float uThickness;         // 线条粗细（像素单位）
    uniform float uMiter;             // 斜接限制 (0-1, 控制尖角的锐利程度)

    // ========== 输出变量 ==========
    // 传递给片段着色器的插值变量
    varying vec2 vUv;                 // 纹理坐标

    /**
     * 计算顶点的最终屏幕位置
     *
     * 这个函数实现了线条渲染的核心算法：
     * 1. 将3D点转换到屏幕空间
     * 2. 计算线条的方向和法线
     * 3. 处理连接处的斜接效果
     * 4. 应用像素精确的宽度
     */
    vec4 getPosition() {
        // ========== 坐标变换 ==========
        // 将所有相关点从3D世界空间转换到2D屏幕空间
        mat4 mvp = projectionMatrix * modelViewMatrix;
        vec4 current = mvp * vec4(position, 1);  // 当前点的裁剪空间坐标
        vec4 nextPos = mvp * vec4(next, 1);      // 下一个点的裁剪空间坐标
        vec4 prevPos = mvp * vec4(prev, 1);      // 前一个点的裁剪空间坐标

        // ========== 屏幕空间转换 ==========
        // 考虑屏幕宽高比，将点转换到标准化屏幕空间
        vec2 aspect = vec2(uResolution.x / uResolution.y, 1);
        vec2 currentScreen = current.xy / current.w * aspect;
        vec2 nextScreen = nextPos.xy / nextPos.w * aspect;
        vec2 prevScreen = prevPos.xy / prevPos.w * aspect;

        // ========== 方向计算 ==========
        // 计算线条在当前点的两个方向向量
        vec2 dir1 = normalize(currentScreen - prevScreen);  // 从前一个点到当前点
        vec2 dir2 = normalize(nextScreen - currentScreen);  // 从当前点到下一个点

        // 计算平均方向向量（用于平滑连接）
        vec2 dir = normalize(dir1 + dir2);

        // ========== 法线计算 ==========
        // 计算垂直于线条方向的法线向量（用于创建宽度）
        vec2 normal = vec2(-dir.y, dir.x);

        // ========== 斜接处理 ==========
        // 在尖角处应用斜接限制，防止法线过长导致视觉错误
        // dot(normal, vec2(-dir1.y, dir1.x)) 计算角度的余弦值
        // mix() 在1.0（无限制）和max(0.3, cosAngle)（有限制）之间插值
        normal /= mix(1.0, max(0.3, dot(normal, vec2(-dir1.y, dir1.x))), uMiter);

        // 恢复宽高比（因为之前应用了aspect变换）
        normal /= aspect;

        // ========== 像素精确宽度 ==========
        // 计算像素宽度比例，确保线条在不同分辨率下保持一致的像素宽度
        float pixelWidthRatio = 1.0 / (uResolution.y / uDPR);
        float pixelWidth = current.w * pixelWidthRatio;

        // 将法线向量缩放到最终的线条宽度
        normal *= pixelWidth * uThickness;

        // ========== 顶点偏移 ==========
        // 根据side值（-1或1）将顶点沿法线方向偏移
        // 这创建了线条的左右两侧边缘
        current.xy -= normal * side;

        return current;
    }

    void main() {
        // 传递纹理坐标给片段着色器（用于材质和效果）
        vUv = uv;

        // 计算并设置最终的顶点位置
        gl_Position = getPosition();
    }
`;

/**
 * 默认片段着色器
 *
 * 这是一个简单的片段着色器，为线条提供基本的纯色渲染。
 * 在实际应用中，可以扩展此着色器来实现更复杂的效果，如：
 * - 渐变色
 * - 纹理映射
 * - 透明度变化
 * - 发光效果
 * - 虚线效果
 */
const defaultFragment = /* glsl */ `
    precision highp float;

    // ========== 统一变量 ==========
    uniform vec3 uColor;  // 线条的RGB颜色值

    // ========== 输入变量 ==========
    // 从顶点着色器接收的插值变量
    varying vec2 vUv;     // 纹理坐标 (可用于创建沿线条的效果)

    void main() {
        // ========== 基本颜色渲染 ==========
        // 设置片段的RGB颜色
        gl_FragColor.rgb = uColor;

        // 设置完全不透明
        gl_FragColor.a = 1.0;

        // 注意：vUv变量在这个基本着色器中没有使用，
        // 但可以用于创建更高级的效果，例如：
        // - 沿线条的渐变：gl_FragColor.a = vUv.y;
        // - 虚线效果：if (mod(vUv.y * 10.0, 2.0) > 1.0) discard;
        // - 边缘淡化：gl_FragColor.a = 1.0 - abs(vUv.x - 0.5) * 2.0;
    }
`;
