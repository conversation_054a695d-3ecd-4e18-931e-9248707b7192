# GLSL 数组声明与 JavaScript 数组传递详解

## 核心问题分析

您选中的代码展示了一个重要概念：

```glsl
uniform float u_boneWeights[4];  // 骨骼权重数组
```

为什么 GLSL 中需要声明 `u_boneWeights[4]` 而不是 `u_boneWeights`？

## GLSL vs JavaScript 数组对比

### JavaScript 中的数组（动态）

```javascript
// JavaScript: 运行时动态分配
let boneWeights = [0.8, 0.2, 0.0, 0.0]; // 可以随时改变大小
boneWeights.push(0.1); // 动态添加元素
boneWeights.length; // 运行时查询长度
```

### GLSL 中的数组（静态）

```glsl
// GLSL: 编译时静态分配
uniform float u_boneWeights[4];  // 必须在编译时指定大小
// u_boneWeights.length;  // ❌ GLSL没有.length属性
// 数组大小在编译后无法改变
```

## 详细工作流程示例

### 1. 着色器编译阶段

```glsl
// 顶点着色器源码
attribute vec4 a_position;
attribute vec4 a_boneWeights;
uniform float u_boneWeights[4];  // 🔍 编译器看到这里

void main() {
    // 编译器分析：需要为u_boneWeights分配4个float的内存空间
    float totalWeight = 0.0;

    // 编译器生成循环代码，已知循环次数为4
    for(int i = 0; i < 4; i++) {
        totalWeight += u_boneWeights[i] * a_boneWeights[i];
    }

    gl_Position = a_position * totalWeight;
}
```

**编译器内部处理：**

```
1. 解析 uniform float u_boneWeights[4]
2. 分配内存：4 * sizeof(float) = 16 bytes
3. 生成GPU指令：访问连续的4个内存位置
4. 优化循环：已知循环边界，可以展开优化
```

### 2. JavaScript 数据传递阶段

```javascript
// JavaScript端：准备数据
const boneWeights = [0.8, 0.2, 0.0, 0.0]; // 4个元素的数组

const program = new Program(gl, {
    vertex: vertexShader,
    fragment: fragmentShader,
    uniforms: {
        // 🔍 关键：JavaScript变量名不需要[4]，但数组长度必须匹配
        u_boneWeights: { value: boneWeights }, // 传递4个元素的数组
    },
});

// 内部执行流程：
// 1. 检查 boneWeights.length = 4
// 2. 调用 gl.uniform1fv(location, boneWeights)
// 3. GPU接收4个float值到预分配的内存空间
```

## 实际应用场景对比

### 场景 1：骨骼动画系统

**GLSL 着色器（编译时）：**

```glsl
// 必须明确指定支持的最大骨骼数量
uniform float u_boneWeights[4];    // 最多4个骨骼
uniform mat4 u_boneMatrices[4];    // 对应的4个变换矩阵

attribute vec4 a_boneIndices;      // 顶点关联的骨骼索引
attribute vec4 a_boneWeights;      // 顶点的骨骼权重

void main() {
    vec4 position = vec4(0.0);

    // 编译时已知循环次数，可以优化
    for(int i = 0; i < 4; i++) {
        int boneIndex = int(a_boneIndices[i]);
        float weight = a_boneWeights[i] * u_boneWeights[i];

        if(weight > 0.0) {
            position += u_boneMatrices[boneIndex] * a_position * weight;
        }
    }

    gl_Position = u_projectionMatrix * u_viewMatrix * position;
}
```

**JavaScript 应用（运行时）：**

```javascript
// 骨骼动画管理器
class SkeletalAnimationManager {
    constructor(maxBones = 4) {
        this.maxBones = maxBones;
        this.boneWeights = new Float32Array(maxBones);
        this.boneMatrices = new Float32Array(maxBones * 16); // 4x4矩阵
    }

    updateBoneWeights(weights) {
        // 确保数组长度匹配着色器声明
        if (weights.length > this.maxBones) {
            console.warn(`Too many bone weights: ${weights.length}, max: ${this.maxBones}`);
            weights = weights.slice(0, this.maxBones);
        }

        // 填充到固定大小数组
        this.boneWeights.fill(0.0);
        this.boneWeights.set(weights);

        // 传递给着色器
        program.uniforms.u_boneWeights.value = this.boneWeights;
    }

    // 使用示例
    animateCharacter(frame) {
        // 计算当前帧的骨骼权重
        const currentWeights = this.calculateBoneWeights(frame);

        // 可能只有2个活跃骨骼：[0.7, 0.3, 0.0, 0.0]
        this.updateBoneWeights(currentWeights);
    }
}
```

### 场景 2：多光源照明系统

**GLSL 着色器（编译时限制）：**

```glsl
#define MAX_LIGHTS 8  // 编译时常量

uniform float u_lightIntensities[MAX_LIGHTS];  // 8个光源强度
uniform vec3 u_lightPositions[MAX_LIGHTS];     // 8个光源位置
uniform vec3 u_lightColors[MAX_LIGHTS];        // 8个光源颜色
uniform int u_activeLightCount;                // 实际活跃光源数量

void main() {
    vec3 finalColor = vec3(0.0);

    // 循环所有可能的光源位置
    for(int i = 0; i < MAX_LIGHTS; i++) {
        // 运行时检查是否为活跃光源
        if(i >= u_activeLightCount) break;

        if(u_lightIntensities[i] > 0.0) {
            vec3 lightDir = normalize(u_lightPositions[i] - vWorldPosition);
            float intensity = u_lightIntensities[i];
            vec3 lightColor = u_lightColors[i];

            finalColor += calculateLighting(lightDir, intensity, lightColor);
        }
    }

    gl_FragColor = vec4(finalColor, 1.0);
}
```

**JavaScript 光源管理（运行时灵活）：**

```javascript
class LightManager {
    constructor() {
        this.maxLights = 8; // 匹配着色器中的MAX_LIGHTS
        this.lights = [];

        // 预分配固定大小的数组
        this.intensities = new Float32Array(this.maxLights);
        this.positions = new Float32Array(this.maxLights * 3);
        this.colors = new Float32Array(this.maxLights * 3);
    }

    addLight(light) {
        if (this.lights.length >= this.maxLights) {
            console.warn(`Maximum lights reached: ${this.maxLights}`);
            return false;
        }

        this.lights.push(light);
        this.updateUniforms();
        return true;
    }

    removeLight(index) {
        if (index < this.lights.length) {
            this.lights.splice(index, 1);
            this.updateUniforms();
        }
    }

    updateUniforms() {
        // 清空数组
        this.intensities.fill(0.0);
        this.positions.fill(0.0);
        this.colors.fill(0.0);

        // 填充活跃光源数据
        this.lights.forEach((light, index) => {
            this.intensities[index] = light.intensity;

            // 位置数据（vec3）
            this.positions[index * 3] = light.position.x;
            this.positions[index * 3 + 1] = light.position.y;
            this.positions[index * 3 + 2] = light.position.z;

            // 颜色数据（vec3）
            this.colors[index * 3] = light.color.r;
            this.colors[index * 3 + 1] = light.color.g;
            this.colors[index * 3 + 2] = light.color.b;
        });

        // 更新着色器uniform
        program.uniforms.u_lightIntensities.value = this.intensities;
        program.uniforms.u_lightPositions.value = this.positions;
        program.uniforms.u_lightColors.value = this.colors;
        program.uniforms.u_activeLightCount.value = this.lights.length;
    }
}

// 使用示例
const lightManager = new LightManager();

// 动态添加光源
lightManager.addLight({
    intensity: 1.0,
    position: { x: 10, y: 10, z: 10 },
    color: { r: 1.0, g: 1.0, b: 1.0 },
});

lightManager.addLight({
    intensity: 0.5,
    position: { x: -5, y: 5, z: 0 },
    color: { r: 1.0, g: 0.5, b: 0.2 },
});

// 实际传递给GPU的数组：
// u_lightIntensities = [1.0, 0.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
// u_activeLightCount = 2
```

## 技术原理深入分析

### 1. GPU 内存分配机制

```
编译时内存分配：
┌─────────────────────────────────────┐
│ GLSL 编译器                          │
├─────────────────────────────────────┤
│ uniform float u_boneWeights[4];     │
│ ↓                                   │
│ 分配连续内存：                        │
│ [float][float][float][float]        │
│  16 bytes (4 * 4 bytes)            │
└─────────────────────────────────────┘

运行时数据传输：
┌─────────────────────────────────────┐
│ JavaScript                          │
├─────────────────────────────────────┤
│ [0.8, 0.2, 0.0, 0.0]               │
│ ↓ gl.uniform1fv()                   │
│ GPU 内存：                           │
│ [0.8][0.2][0.0][0.0]               │
└─────────────────────────────────────┘
```

### 2. 编译时 vs 运行时差异

| 方面     | GLSL (编译时)  | JavaScript (运行时) |
| -------- | -------------- | ------------------- |
| 数组大小 | 必须固定 `[4]` | 动态变化 `.length`  |
| 内存分配 | 编译时静态分配 | 运行时动态分配      |
| 性能     | 高效，预分配   | 灵活，但有开销      |
| 类型检查 | 编译时检查     | 运行时检查          |

### 3. 为什么 GLSL 需要固定大小？

#### GPU 架构限制

```
GPU 并行处理特点：
┌─────────────────────────────────────┐
│ 所有着色器核心同时执行相同代码         │
├─────────────────────────────────────┤
│ Core 1: for(i=0; i<4; i++) {...}   │
│ Core 2: for(i=0; i<4; i++) {...}   │
│ Core 3: for(i=0; i<4; i++) {...}   │
│ ...                                 │
│ Core N: for(i=0; i<4; i++) {...}   │
└─────────────────────────────────────┘

如果数组大小不固定：
❌ 不同核心可能执行不同次数的循环
❌ 破坏 GPU 的 SIMD (单指令多数据) 架构
❌ 导致性能严重下降
```

#### 内存访问模式

```glsl
// ✅ 编译时已知内存布局
uniform float u_weights[4];
// GPU 可以预计算内存地址：
// u_weights[0] = base_address + 0 * sizeof(float)
// u_weights[1] = base_address + 1 * sizeof(float)
// u_weights[2] = base_address + 2 * sizeof(float)
// u_weights[3] = base_address + 3 * sizeof(float)

// ❌ 如果大小未知，无法预计算地址
// uniform float u_weights[];  // 编译器不知道如何分配内存
```

## 实际开发中的最佳实践

### 1. 合理设计数组大小

```glsl
// 根据目标硬件和需求设计合理的上限
#define MAX_BONES 4        // 移动设备友好
#define MAX_LIGHTS 8       // 桌面设备可以更多
#define MAX_TEXTURES 16    // 根据硬件限制

uniform float u_boneWeights[MAX_BONES];
uniform vec3 u_lightPositions[MAX_LIGHTS];
uniform sampler2D u_textures[MAX_TEXTURES];
```

### 2. JavaScript 端的适配策略

```javascript
class UniformArrayManager {
    constructor(maxSize) {
        this.maxSize = maxSize;
        this.data = new Float32Array(maxSize);
    }

    setValues(values) {
        if (values.length > this.maxSize) {
            console.warn(`Array too large: ${values.length}, max: ${this.maxSize}`);
            values = values.slice(0, this.maxSize);
        }

        // 清空并填充新值
        this.data.fill(0.0);
        this.data.set(values);

        return this.data;
    }
}

// 使用示例
const boneWeightManager = new UniformArrayManager(4);
const lightIntensityManager = new UniformArrayManager(8);

// 安全地设置数组值
program.uniforms.u_boneWeights.value = boneWeightManager.setValues([0.8, 0.2]);
program.uniforms.u_lightIntensities.value = lightIntensityManager.setValues([1.0, 0.5, 0.3]);
```

### 3. 动态数组大小的替代方案

```glsl
// 方案1：使用计数器
uniform float u_weights[8];
uniform int u_activeCount;  // 实际使用的元素数量

void main() {
    float total = 0.0;
    for(int i = 0; i < 8; i++) {
        if(i >= u_activeCount) break;  // 提前退出
        total += u_weights[i];
    }
}

// 方案2：使用标记值
uniform float u_weights[8];  // 使用 -1.0 表示无效值

void main() {
    float total = 0.0;
    for(int i = 0; i < 8; i++) {
        if(u_weights[i] < 0.0) break;  // 遇到标记值退出
        total += u_weights[i];
    }
}
```

## 总结

GLSL 中声明 `u_boneWeights[4]` 而不是 `u_boneWeights` 的原因：

1. **GPU 架构要求**：并行处理需要固定的内存布局
2. **编译时优化**：预知数组大小可以生成更高效的代码
3. **内存管理**：静态分配避免运行时内存碎片
4. **性能保证**：固定循环边界便于 GPU 优化

这种设计虽然限制了灵活性，但保证了 GPU 渲染的高性能和稳定性。

## 结构体数组 uniform 的声明与使用

### 问题场景：复杂结构化数据

当 JavaScript 中的 uniform 数据是这样的结构化形式时：

```javascript
lightData: {
    0: {
        position: { value: [10, 10, 10] },          // 对应 lightData[0].position
        color: { value: [1, 1, 1] },                // 对应 lightData[0].color
        intensity: { value: 1.0 }                   // 对应 lightData[0].intensity
    },
    1: {
        position: { value: [-10, 10, 10] },         // 对应 lightData[1].position
        color: { value: [0.8, 0.8, 1.0] },          // 对应 lightData[1].color
        intensity: { value: 0.8 }                   // 对应 lightData[1].intensity
    }
}
```

GLSL 中需要使用**结构体数组**来对应这种数据结构。

### GLSL 结构体声明

```glsl
// 1. 首先定义光源数据结构体
struct LightData {
    vec3 position;   // 光源位置
    vec3 color;      // 光源颜色
    float intensity; // 光源强度
};

// 2. 声明结构体数组uniform
uniform LightData lightData[2];  // 2个光源的数组

// 3. 在着色器中使用
void main() {
    vec3 finalColor = vec3(0.0);

    // 遍历所有光源
    for(int i = 0; i < 2; i++) {
        // 访问结构体成员
        vec3 lightPos = lightData[i].position;
        vec3 lightColor = lightData[i].color;
        float lightIntensity = lightData[i].intensity;

        // 计算光照
        vec3 lightDir = normalize(lightPos - vWorldPosition);
        float diff = max(dot(vNormal, lightDir), 0.0);
        finalColor += lightColor * lightIntensity * diff;
    }

    gl_FragColor = vec4(finalColor, 1.0);
}
```

### 完整的实际应用示例

#### GLSL 着色器代码

```glsl
// 顶点着色器
attribute vec3 a_position;
attribute vec3 a_normal;

uniform mat4 u_modelMatrix;
uniform mat4 u_viewMatrix;
uniform mat4 u_projectionMatrix;
uniform mat3 u_normalMatrix;

varying vec3 vWorldPosition;
varying vec3 vNormal;

void main() {
    vec4 worldPosition = u_modelMatrix * vec4(a_position, 1.0);
    vWorldPosition = worldPosition.xyz;
    vNormal = normalize(u_normalMatrix * a_normal);

    gl_Position = u_projectionMatrix * u_viewMatrix * worldPosition;
}

// 片段着色器
precision mediump float;

// 定义光源数据结构
struct LightData {
    vec3 position;
    vec3 color;
    float intensity;
};

// 声明光源数组
uniform LightData lightData[2];
uniform vec3 u_viewPosition;
uniform vec3 u_materialColor;

varying vec3 vWorldPosition;
varying vec3 vNormal;

void main() {
    vec3 normal = normalize(vNormal);
    vec3 viewDir = normalize(u_viewPosition - vWorldPosition);
    vec3 finalColor = vec3(0.0);

    // 遍历所有光源进行光照计算
    for(int i = 0; i < 2; i++) {
        // 获取当前光源数据
        vec3 lightPos = lightData[i].position;
        vec3 lightColor = lightData[i].color;
        float lightIntensity = lightData[i].intensity;

        // 计算光照方向和距离
        vec3 lightDir = lightPos - vWorldPosition;
        float distance = length(lightDir);
        lightDir = normalize(lightDir);

        // 漫反射
        float diff = max(dot(normal, lightDir), 0.0);

        // 镜面反射
        vec3 reflectDir = reflect(-lightDir, normal);
        float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);

        // 距离衰减
        float attenuation = 1.0 / (1.0 + 0.09 * distance + 0.032 * distance * distance);

        // 累加光照贡献
        vec3 diffuse = diff * lightColor * lightIntensity;
        vec3 specular = spec * lightColor * lightIntensity;
        finalColor += (diffuse + specular) * attenuation;
    }

    gl_FragColor = vec4(finalColor * u_materialColor, 1.0);
}
```

#### JavaScript 程序代码

```javascript
// 创建光源数据
const lightData = {
    0: {
        position: { value: [10, 10, 10] },
        color: { value: [1, 1, 1] },
        intensity: { value: 1.0 },
    },
    1: {
        position: { value: [-10, 10, 10] },
        color: { value: [0.8, 0.8, 1.0] },
        intensity: { value: 0.8 },
    },
};

// 创建着色器程序
const program = new Program(gl, {
    vertex: vertexShader,
    fragment: fragmentShader,
    uniforms: {
        // 结构体数组uniform
        lightData: lightData,

        // 其他uniform
        u_modelMatrix: { value: modelMatrix },
        u_viewMatrix: { value: viewMatrix },
        u_projectionMatrix: { value: projectionMatrix },
        u_normalMatrix: { value: normalMatrix },
        u_viewPosition: { value: camera.position },
        u_materialColor: { value: [0.8, 0.6, 0.4] },
    },
});

// 动态更新光源数据
function updateLights(time) {
    // 更新第一个光源位置（绕圆运动）
    const radius = 15;
    lightData[0].position.value = [Math.cos(time) * radius, 10, Math.sin(time) * radius];

    // 更新第二个光源强度（呼吸效果）
    lightData[1].intensity.value = 0.5 + 0.3 * Math.sin(time * 2);

    // 更新第二个光源颜色（色彩变化）
    lightData[1].color.value = [0.8 + 0.2 * Math.sin(time), 0.8 + 0.2 * Math.sin(time + Math.PI / 3), 1.0];
}

// 渲染循环
function render() {
    const time = performance.now() * 0.001;

    updateLights(time);

    // 使用程序进行渲染
    renderer.render({
        scene: mesh,
        camera: camera,
        program: program,
    });

    requestAnimationFrame(render);
}
```

### 内部处理机制详解

#### 1. WebGL uniform 位置映射

当使用结构体数组时，WebGL 会为每个结构体成员创建独立的 uniform 位置：

```javascript
// WebGL内部会创建这些uniform位置：
// lightData[0].position  -> location_1
// lightData[0].color     -> location_2
// lightData[0].intensity -> location_3
// lightData[1].position  -> location_4
// lightData[1].color     -> location_5
// lightData[1].intensity -> location_6

// Program.js 中的处理逻辑示例：
this.uniformLocations.forEach((location, activeUniform) => {
    // activeUniform.name 可能是：
    // "lightData[0].position"
    // "lightData[0].color"
    // "lightData[0].intensity"
    // "lightData[1].position"
    // 等等...

    if (activeUniform.nameComponents) {
        // 解析结构化uniform路径
        // nameComponents = ["0", "position"] 对于 "lightData[0].position"
        let uniform = this.uniforms[activeUniform.uniformName]; // lightData

        // 遍历路径组件
        activeUniform.nameComponents.forEach((component) => {
            uniform = uniform[component]; // uniform[0].position
        });

        // 设置对应的值
        setUniform(this.gl, activeUniform.type, location, uniform.value);
    }
});
```

#### 2. 内存布局分析

```
GPU 内存中的结构体数组布局：

lightData[0]:
├── position:  [float, float, float]     // 12 bytes
├── color:     [float, float, float]     // 12 bytes
└── intensity: [float]                   // 4 bytes
                                         // 总计: 28 bytes (可能有padding)

lightData[1]:
├── position:  [float, float, float]     // 12 bytes
├── color:     [float, float, float]     // 12 bytes
└── intensity: [float]                   // 4 bytes
                                         // 总计: 28 bytes

总内存占用: 56+ bytes (取决于GPU的对齐要求)
```

### 高级应用场景

#### 1. 复杂材质系统

```glsl
// 材质数据结构
struct MaterialData {
    vec3 albedo;        // 基础颜色
    float metallic;     // 金属度
    float roughness;    // 粗糙度
    float ao;           // 环境遮蔽
    vec3 emission;      // 自发光
};

// 多材质支持
uniform MaterialData materials[4];
uniform int u_materialIndex;

void main() {
    // 根据索引选择材质
    MaterialData material = materials[u_materialIndex];

    // 使用PBR计算
    vec3 albedo = material.albedo;
    float metallic = material.metallic;
    float roughness = material.roughness;

    // ... PBR光照计算
}
```

```javascript
// JavaScript材质管理
const materials = {
    0: {
        // 金属材质
        albedo: { value: [0.7, 0.7, 0.7] },
        metallic: { value: 1.0 },
        roughness: { value: 0.1 },
        ao: { value: 1.0 },
        emission: { value: [0, 0, 0] },
    },
    1: {
        // 塑料材质
        albedo: { value: [0.8, 0.2, 0.2] },
        metallic: { value: 0.0 },
        roughness: { value: 0.8 },
        ao: { value: 1.0 },
        emission: { value: [0, 0, 0] },
    },
    2: {
        // 发光材质
        albedo: { value: [0.1, 0.1, 0.1] },
        metallic: { value: 0.0 },
        roughness: { value: 0.9 },
        ao: { value: 1.0 },
        emission: { value: [0, 1, 0.5] },
    },
};
```

#### 2. 骨骼动画系统

```glsl
// 骨骼数据结构
struct BoneData {
    mat4 transform;     // 骨骼变换矩阵
    mat4 inverseBindPose; // 逆绑定姿态矩阵
    float weight;       // 骨骼权重
};

uniform BoneData bones[32];  // 支持32个骨骼

attribute vec4 a_boneIndices;  // 顶点关联的骨骼索引
attribute vec4 a_boneWeights;  // 顶点的骨骼权重

void main() {
    vec4 position = vec4(0.0);
    vec3 normal = vec3(0.0);

    // 骨骼蒙皮计算
    for(int i = 0; i < 4; i++) {
        int boneIndex = int(a_boneIndices[i]);
        float weight = a_boneWeights[i];

        if(weight > 0.0 && boneIndex >= 0) {
            mat4 boneTransform = bones[boneIndex].transform *
                               bones[boneIndex].inverseBindPose;

            position += boneTransform * vec4(a_position, 1.0) * weight;
            normal += mat3(boneTransform) * a_normal * weight;
        }
    }

    gl_Position = u_projectionMatrix * u_viewMatrix * position;
    vNormal = normalize(normal);
}
```

### 性能优化建议

#### 1. 减少结构体大小

```glsl
// ❌ 低效：结构体过大
struct LightData {
    vec3 position;      // 12 bytes
    vec3 color;         // 12 bytes
    vec3 direction;     // 12 bytes
    float intensity;    // 4 bytes
    float range;        // 4 bytes
    float innerCone;    // 4 bytes
    float outerCone;    // 4 bytes
    int type;           // 4 bytes
    // 总计: 56 bytes per light
};

// ✅ 高效：分离常用和不常用数据
struct LightCore {
    vec3 position;      // 12 bytes
    vec3 color;         // 12 bytes
    float intensity;    // 4 bytes
    // 总计: 28 bytes per light
};

struct LightExtended {
    vec3 direction;     // 12 bytes
    float range;        // 4 bytes
    float innerCone;    // 4 bytes
    float outerCone;    // 4 bytes
    // 总计: 24 bytes per light
};

uniform LightCore lights[8];           // 常用数据
uniform LightExtended lightDetails[8]; // 详细数据
```

#### 2. 使用 Uniform Buffer Objects (WebGL 2.0)

```javascript
// WebGL 2.0 的UBO优化
const lightDataUBO = gl.createBuffer();
gl.bindBuffer(gl.UNIFORM_BUFFER, lightDataUBO);

// 打包结构体数据
const lightDataArray = new Float32Array(8 * 7); // 8个光源，每个7个float
lightData.forEach((light, index) => {
    const offset = index * 7;
    lightDataArray.set(light.position.value, offset); // 3 floats
    lightDataArray.set(light.color.value, offset + 3); // 3 floats
    lightDataArray[offset + 6] = light.intensity.value; // 1 float
});

gl.bufferData(gl.UNIFORM_BUFFER, lightDataArray, gl.DYNAMIC_DRAW);

// 绑定到着色器
const blockIndex = gl.getUniformBlockIndex(program, 'LightBlock');
gl.uniformBlockBinding(program, blockIndex, 0);
gl.bindBufferBase(gl.UNIFORM_BUFFER, 0, lightDataUBO);
```

### 常见问题和解决方案

#### 1. 结构体成员对齐问题

```glsl
// ❌ 可能导致对齐问题
struct BadAlignment {
    float a;        // 4 bytes
    vec3 b;         // 12 bytes，但可能需要16字节对齐
    float c;        // 4 bytes
};

// ✅ 良好的对齐
struct GoodAlignment {
    vec3 b;         // 12 bytes
    float a;        // 4 bytes
    float c;        // 4 bytes
    float padding;  // 4 bytes padding，确保16字节对齐
};
```

#### 2. 动态数组大小处理

```glsl
#define MAX_LIGHTS 8

uniform LightData lights[MAX_LIGHTS];
uniform int u_lightCount;  // 实际光源数量

void main() {
    vec3 finalColor = vec3(0.0);

    // 只处理活跃的光源
    for(int i = 0; i < MAX_LIGHTS; i++) {
        if(i >= u_lightCount) break;

        // 处理光源 lights[i]
        // ...
    }
}
```
