# WebGL unpackAlignment 深度解析与内存对齐机制

## 🎯 核心概念

### unpackAlignment 是什么？

`unpackAlignment` 是 WebGL 中控制像素数据内存对齐的关键参数，通过 `gl.pixelStorei(gl.UNPACK_ALIGNMENT, value)` 设置。它决定了从客户端内存（JavaScript）传输到 GPU 显存时，每行像素数据的字节对齐方式。

## 🔬 技术原理

### 1. 内存对齐的必要性

```javascript
// CPU/GPU 内存访问的物理限制
const memoryAccessPrinciple = {
    // CPU 和 GPU 都有内存对齐要求
    cpuAlignment: {
        reason: 'CPU 按字（word）访问内存，通常是 4 或 8 字节',
        benefit: '对齐访问比非对齐访问快 2-10 倍',
        penalty: '非对齐访问可能导致额外的内存读取周期',
    },

    gpuAlignment: {
        reason: 'GPU 内存控制器优化了对齐访问',
        benefit: '对齐数据可以利用内存突发传输',
        penalty: '非对齐数据需要额外的内存事务',
    },
};
```

### 2. unpackAlignment 的工作机制

```javascript
// unpackAlignment 如何影响数据传输
const alignmentMechanism = {
    purpose: '确保每行像素数据的起始地址满足对齐要求',

    // 对齐计算公式
    rowStride: 'ceil(width * bytesPerPixel / alignment) * alignment',

    // 实际内存布局
    memoryLayout: {
        withoutAlignment: '紧密排列，可能导致性能问题',
        withAlignment: '每行填充到对齐边界，优化访问性能',
    },
};
```

## 📊 对齐值详解

### 支持的对齐值

```javascript
const alignmentOptions = {
    1: {
        description: '1字节对齐（无对齐）',
        usage: '任意宽度的纹理，单通道格式',
        performance: '可能较慢，但兼容性最好',
        example: 'RGB 格式，宽度不是4的倍数',
    },

    2: {
        description: '2字节对齐',
        usage: '双通道格式（RG），偶数宽度',
        performance: '中等性能',
        example: 'LUMINANCE_ALPHA 格式',
    },

    4: {
        description: '4字节对齐（默认）',
        usage: 'RGBA 格式，4的倍数宽度',
        performance: '最佳性能，标准配置',
        example: '大多数彩色纹理',
    },

    8: {
        description: '8字节对齐',
        usage: '高精度格式，64位系统优化',
        performance: '在某些硬件上可能更快',
        example: '双精度或特殊格式',
    },
};
```

## 🧮 内存布局计算

### 1. 对齐计算示例

```javascript
// 计算实际内存布局的函数
function calculateMemoryLayout(width, height, format, alignment) {
    // 每像素字节数
    const bytesPerPixel = {
        RGBA: 4,
        RGB: 3,
        LUMINANCE_ALPHA: 2,
        LUMINANCE: 1,
        ALPHA: 1,
    }[format];

    // 每行原始字节数
    const rawBytesPerRow = width * bytesPerPixel;

    // 对齐后的每行字节数
    const alignedBytesPerRow = Math.ceil(rawBytesPerRow / alignment) * alignment;

    // 每行的填充字节数
    const paddingBytes = alignedBytesPerRow - rawBytesPerRow;

    // 总内存使用
    const totalBytes = alignedBytesPerRow * height;

    return {
        rawBytesPerRow,
        alignedBytesPerRow,
        paddingBytes,
        totalBytes,
        wastedBytes: paddingBytes * height,
        efficiency: (rawBytesPerRow * height) / totalBytes,
    };
}

// 实际示例
const examples = {
    // RGBA 纹理，300x200，4字节对齐
    rgbaTexture: calculateMemoryLayout(300, 200, 'RGBA', 4),
    // 结果：每行1200字节，已经是4的倍数，无填充

    // RGB 纹理，301x200，4字节对齐
    rgbTexture: calculateMemoryLayout(301, 200, 'RGB', 4),
    // 结果：每行903字节 -> 对齐到904字节，每行填充1字节

    // RGB 纹理，301x200，1字节对齐
    rgbTextureNoAlign: calculateMemoryLayout(301, 200, 'RGB', 1),
    // 结果：每行903字节，无填充，但可能性能较差
};
```

### 2. 性能影响分析

```javascript
// 不同对齐方式的性能对比
const performanceComparison = {
    scenario: 'RGB纹理，宽度301像素',

    alignment1: {
        bytesPerRow: 903, // 301 * 3
        padding: 0,
        performance: '可能较慢',
        reason: '非对齐访问，可能需要额外内存事务',
    },

    alignment4: {
        bytesPerRow: 904, // ceil(903/4) * 4
        padding: 1,
        performance: '更快',
        reason: '4字节对齐，利用CPU/GPU优化访问',
        tradeoff: '每行浪费1字节，但访问更高效',
    },
};
```

## 🎮 实际应用场景

### 1. 不同格式的最佳对齐策略

```javascript
const alignmentStrategies = {
    // RGBA 纹理（最常见）
    rgba: {
        format: 'gl.RGBA',
        bytesPerPixel: 4,
        recommendedAlignment: 4,
        reason: '每像素4字节，天然4字节对齐',
        code: `
            const texture = new Texture(gl, {
                image: imageElement,
                format: gl.RGBA,
                unpackAlignment: 4, // 默认值，最优选择
            });
        `,
    },

    // RGB 纹理
    rgb: {
        format: 'gl.RGB',
        bytesPerPixel: 3,
        recommendedAlignment: 1,
        reason: '3字节/像素，很难自然对齐到4字节',
        alternative: '考虑转换为RGBA格式',
        code: `
            const texture = new Texture(gl, {
                image: rgbImageData,
                format: gl.RGB,
                unpackAlignment: 1, // 避免对齐问题
            });
        `,
    },

    // 灰度纹理
    luminance: {
        format: 'gl.LUMINANCE',
        bytesPerPixel: 1,
        recommendedAlignment: 1,
        reason: '单字节格式，对齐意义不大',
        code: `
            const texture = new Texture(gl, {
                image: grayscaleData,
                format: gl.LUMINANCE,
                unpackAlignment: 1,
            });
        `,
    },

    // 数据纹理
    dataTexture: {
        format: 'gl.RGBA',
        type: 'gl.FLOAT',
        bytesPerPixel: 16, // 4 * 4字节
        recommendedAlignment: 4,
        reason: '浮点数据，保持4字节对齐',
        code: `
            const texture = new Texture(gl, {
                image: new Float32Array(width * height * 4),
                width, height,
                format: gl.RGBA,
                type: gl.FLOAT,
                unpackAlignment: 4,
            });
        `,
    },
};
```

### 2. 常见问题和解决方案

```javascript
const commonIssues = {
    // 问题1：RGB纹理显示异常
    rgbDisplayIssue: {
        problem: 'RGB纹理显示错位或颜色异常',
        cause: '默认4字节对齐与RGB的3字节不匹配',
        solution: '设置 unpackAlignment 为 1',
        code: `
            // ❌ 错误：使用默认4字节对齐
            const badTexture = new Texture(gl, {
                image: rgbImageData,
                format: gl.RGB,
                // unpackAlignment: 4 (默认)
            });
            
            // ✅ 正确：使用1字节对齐
            const goodTexture = new Texture(gl, {
                image: rgbImageData,
                format: gl.RGB,
                unpackAlignment: 1,
            });
        `,
    },

    // 问题2：自定义宽度的数据纹理
    customWidthIssue: {
        problem: '非标准宽度的数据纹理上传失败',
        cause: '数据长度与对齐要求不匹配',
        solution: '计算正确的数据长度或调整对齐',
        code: `
            // 计算对齐后的数据长度
            function createAlignedData(width, height, format, alignment) {
                const bytesPerPixel = 4; // RGBA
                const rawBytesPerRow = width * bytesPerPixel;
                const alignedBytesPerRow = Math.ceil(rawBytesPerRow / alignment) * alignment;
                const alignedWidth = alignedBytesPerRow / bytesPerPixel;
                
                return new Float32Array(alignedWidth * height);
            }
        `,
    },
};
```

## 🚀 性能优化建议

### 1. 最佳实践

```javascript
const bestPractices = {
    // 1. 根据格式选择对齐
    formatBasedAlignment: {
        principle: '让对齐值匹配像素格式',
        rules: {
            'RGBA (4字节)': 'unpackAlignment: 4',
            'RGB (3字节)': 'unpackAlignment: 1',
            'RG (2字节)': 'unpackAlignment: 2',
            'R (1字节)': 'unpackAlignment: 1',
        },
    },

    // 2. 纹理尺寸优化
    dimensionOptimization: {
        principle: '选择对齐友好的纹理尺寸',
        recommendations: {
            RGBA纹理: '宽度任意（4字节对齐天然满足）',
            RGB纹理: '宽度尽量是4的倍数，或使用1字节对齐',
        },
    },

    // 3. 数据预处理
    dataPreprocessing: {
        principle: '在数据准备阶段考虑对齐',
        example: `
            // 将RGB数据转换为RGBA以获得更好的对齐
            function rgbToRgba(rgbData, width, height) {
                const rgbaData = new Uint8Array(width * height * 4);
                for (let i = 0; i < width * height; i++) {
                    rgbaData[i * 4] = rgbData[i * 3];     // R
                    rgbaData[i * 4 + 1] = rgbData[i * 3 + 1]; // G
                    rgbaData[i * 4 + 2] = rgbData[i * 3 + 2]; // B
                    rgbaData[i * 4 + 3] = 255;            // A
                }
                return rgbaData;
            }
        `,
    },
};
```

### 2. 调试和诊断

```javascript
const debuggingTools = {
    // 对齐问题诊断
    alignmentDiagnostic: `
        function diagnoseAlignment(width, format, alignment) {
            const bytesPerPixel = {
                'RGBA': 4, 'RGB': 3, 'RG': 2, 'R': 1
            }[format];
            
            const rawBytes = width * bytesPerPixel;
            const alignedBytes = Math.ceil(rawBytes / alignment) * alignment;
            const padding = alignedBytes - rawBytes;
            
            console.log('对齐诊断:', {
                width,
                format,
                alignment,
                bytesPerPixel,
                rawBytesPerRow: rawBytes,
                alignedBytesPerRow: alignedBytes,
                paddingBytes: padding,
                isOptimal: padding === 0,
                recommendation: padding > 0 ? 
                    '考虑使用1字节对齐或调整纹理宽度' : 
                    '当前配置已优化'
            });
        }
    `,

    // 性能测试
    performanceTest: `
        function testAlignmentPerformance(imageData, width, height) {
            const alignments = [1, 2, 4, 8];
            const results = {};
            
            alignments.forEach(alignment => {
                const startTime = performance.now();
                
                // 创建并上传纹理
                const texture = new Texture(gl, {
                    image: imageData,
                    width, height,
                    format: gl.RGB,
                    unpackAlignment: alignment,
                });
                
                // 强制上传
                texture.update();
                
                const endTime = performance.now();
                results[alignment] = endTime - startTime;
            });
            
            console.log('对齐性能测试结果:', results);
        }
    `,
};
```

## 🔧 底层实现机制

### 1. WebGL 内部处理流程

```javascript
// WebGL 内部的 unpackAlignment 处理流程
const webglInternalProcess = {
    // 步骤1：设置对齐参数
    step1_setAlignment: `
        gl.pixelStorei(gl.UNPACK_ALIGNMENT, 4);
        // 这个调用会影响后续所有的纹理上传操作
    `,

    // 步骤2：计算内存布局
    step2_calculateLayout: `
        // WebGL 内部计算逻辑（伪代码）
        function calculateRowStride(width, bytesPerPixel, alignment) {
            const rawBytes = width * bytesPerPixel;
            const alignedBytes = Math.ceil(rawBytes / alignment) * alignment;
            return alignedBytes;
        }
    `,

    // 步骤3：数据传输
    step3_dataTransfer: `
        // 对于每一行像素数据
        for (let row = 0; row < height; row++) {
            const srcOffset = row * width * bytesPerPixel;
            const dstOffset = row * alignedBytesPerRow;

            // 复制实际像素数据
            copyPixelData(src + srcOffset, dst + dstOffset, width * bytesPerPixel);

            // 跳过填充字节（如果有）
            // 填充字节不需要复制，GPU会忽略它们
        }
    `,
};
```

### 2. GPU 硬件层面的优化

```javascript
// GPU 硬件如何利用内存对齐
const gpuHardwareOptimization = {
    // 内存控制器优化
    memoryController: {
        burstTransfer: '对齐数据可以使用突发传输模式',
        cacheEfficiency: '对齐访问提高缓存命中率',
        bandwidth: '减少内存带宽浪费',
    },

    // 纹理单元优化
    textureUnit: {
        fetchOptimization: '对齐的纹理数据获取更高效',
        filteringPerformance: '过滤操作在对齐数据上更快',
        mipmapGeneration: 'Mipmap 生成受益于对齐',
    },

    // 实际性能数据（示例）
    performanceGains: {
        alignedAccess: '100% 基准性能',
        unalignedAccess: '60-80% 性能（取决于硬件）',
        worstCase: '某些移动GPU可能降至40%性能',
    },
};
```

## 📱 平台差异与兼容性

### 1. 不同平台的对齐行为

```javascript
const platformDifferences = {
    // 桌面平台
    desktop: {
        windows: {
            nvidia: '严格遵循对齐要求，性能差异明显',
            amd: '对齐优化显著，建议使用正确对齐',
            intel: '集成显卡对对齐更敏感',
        },
        macos: {
            behavior: '苹果GPU对对齐有良好优化',
            recommendation: '遵循标准对齐实践',
        },
        linux: {
            behavior: '取决于具体的GPU驱动',
            note: '开源驱动可能有不同的优化策略',
        },
    },

    // 移动平台
    mobile: {
        ios: {
            behavior: 'A系列芯片对内存对齐要求严格',
            performance: '正确对齐可显著提升性能',
            recommendation: '必须正确设置对齐',
        },
        android: {
            adreno: 'Qualcomm GPU对对齐敏感',
            mali: 'ARM GPU有自己的优化策略',
            powerVR: 'Imagination GPU的对齐行为',
            fragmentation: '设备碎片化导致行为差异',
        },
    },

    // WebGL版本差异
    webglVersions: {
        webgl1: {
            alignment: '支持1, 2, 4, 8字节对齐',
            default: '默认4字节对齐',
            limitations: '某些格式组合有限制',
        },
        webgl2: {
            alignment: '完全支持所有对齐选项',
            additional: '支持更多像素格式',
            improvements: '更好的对齐处理',
        },
    },
};
```

### 2. 兼容性最佳实践

```javascript
const compatibilityBestPractices = {
    // 通用兼容性策略
    universalStrategy: {
        principle: '选择最安全的对齐方式',
        implementation: `
            function getSafeAlignment(format, width) {
                // 根据格式选择最安全的对齐
                const formatAlignment = {
                    'RGBA': 4,    // 天然4字节对齐
                    'RGB': 1,     // 避免对齐问题
                    'RG': 2,      // 2字节对齐
                    'R': 1,       // 单字节
                };

                return formatAlignment[format] || 1; // 默认最安全
            }
        `,
    },

    // 动态检测策略
    dynamicDetection: {
        concept: '运行时检测最佳对齐',
        implementation: `
            class AlignmentOptimizer {
                static detectOptimalAlignment(gl, format, width, height) {
                    const testData = this.generateTestData(width, height, format);
                    const alignments = [1, 2, 4, 8];
                    let bestAlignment = 1;
                    let bestPerformance = 0;

                    alignments.forEach(alignment => {
                        const performance = this.benchmarkAlignment(
                            gl, testData, width, height, format, alignment
                        );

                        if (performance > bestPerformance) {
                            bestPerformance = performance;
                            bestAlignment = alignment;
                        }
                    });

                    return bestAlignment;
                }

                static benchmarkAlignment(gl, data, width, height, format, alignment) {
                    const iterations = 10;
                    const startTime = performance.now();

                    for (let i = 0; i < iterations; i++) {
                        const texture = gl.createTexture();
                        gl.bindTexture(gl.TEXTURE_2D, texture);
                        gl.pixelStorei(gl.UNPACK_ALIGNMENT, alignment);
                        gl.texImage2D(gl.TEXTURE_2D, 0, format, width, height, 0, format, gl.UNSIGNED_BYTE, data);
                        gl.deleteTexture(texture);
                    }

                    const endTime = performance.now();
                    return iterations / (endTime - startTime); // 操作/毫秒
                }
            }
        `,
    },
};
```

## 🎯 高级应用场景

### 1. 大型纹理优化

```javascript
const largeTextureOptimization = {
    // 4K纹理优化
    texture4K: {
        dimensions: '4096x4096',
        format: 'RGBA',
        dataSize: '67MB (4096 * 4096 * 4)',
        alignmentImpact: {
            aligned: '无额外开销，直接传输',
            unaligned: '可能需要额外的内存复制操作',
            recommendation: '必须使用4字节对齐',
        },
        code: `
            const texture4K = new Texture(gl, {
                width: 4096,
                height: 4096,
                format: gl.RGBA,
                type: gl.UNSIGNED_BYTE,
                unpackAlignment: 4, // 关键！
                generateMipmaps: true,
            });
        `,
    },

    // 流式纹理加载
    streamingTextures: {
        concept: '分块加载大型纹理',
        alignmentConsideration: '每个块都需要正确对齐',
        implementation: `
            class StreamingTexture {
                loadTile(x, y, tileWidth, tileHeight, data) {
                    // 确保每个瓦片的对齐正确
                    const alignment = this.calculateTileAlignment(tileWidth, this.format);

                    gl.pixelStorei(gl.UNPACK_ALIGNMENT, alignment);
                    gl.texSubImage2D(
                        gl.TEXTURE_2D, 0,
                        x, y, tileWidth, tileHeight,
                        this.format, this.type, data
                    );
                }

                calculateTileAlignment(width, format) {
                    const bytesPerPixel = this.getBytesPerPixel(format);
                    const rowBytes = width * bytesPerPixel;

                    // 选择最大的可用对齐
                    for (const alignment of [8, 4, 2, 1]) {
                        if (rowBytes % alignment === 0) {
                            return alignment;
                        }
                    }
                    return 1;
                }
            }
        `,
    },
};
```

### 2. 数据纹理优化

```javascript
const dataTextureOptimization = {
    // 计算着色器数据纹理
    computeShaderData: {
        purpose: '存储计算数据，如粒子位置、物理状态',
        alignmentCritical: '对齐错误会导致数据错位',
        example: `
            // 粒子系统数据纹理
            class ParticleDataTexture {
                constructor(gl, particleCount) {
                    // 计算纹理尺寸（正方形）
                    this.size = Math.ceil(Math.sqrt(particleCount));

                    // 每个粒子：位置(xyz) + 速度(xyz) + 生命周期(w) + 其他(w) = 8个float
                    this.data = new Float32Array(this.size * this.size * 4);

                    this.texture = new Texture(gl, {
                        image: this.data,
                        width: this.size,
                        height: this.size,
                        format: gl.RGBA,
                        type: gl.FLOAT,
                        internalFormat: gl.RGBA32F,
                        unpackAlignment: 4, // Float32Array天然4字节对齐
                        generateMipmaps: false,
                        minFilter: gl.NEAREST,
                        magFilter: gl.NEAREST,
                    });
                }

                updateParticle(index, position, velocity, life, other) {
                    const offset = index * 4;
                    this.data[offset] = position.x;
                    this.data[offset + 1] = position.y;
                    this.data[offset + 2] = position.z;
                    this.data[offset + 3] = velocity.x;
                    // ... 更多数据

                    // 标记需要更新
                    this.texture.needsUpdate = true;
                }
            }
        `,
    },

    // HDR纹理处理
    hdrTextures: {
        format: 'RGBA16F 或 RGBA32F',
        alignmentRequirement: '必须正确对齐以避免性能问题',
        example: `
            const hdrTexture = new Texture(gl, {
                image: hdrImageData, // Float32Array
                width: 2048,
                height: 1024,
                format: gl.RGBA,
                type: gl.FLOAT,
                internalFormat: gl.RGBA16F,
                unpackAlignment: 4, // 关键设置
                minFilter: gl.LINEAR,
                magFilter: gl.LINEAR,
            });
        `,
    },
};
```

## 🔍 调试和故障排除

### 1. 常见错误诊断

```javascript
const commonErrors = {
    // 错误1：纹理显示异常
    textureCorruption: {
        symptoms: ['纹理显示错位', '颜色通道混乱', '部分区域显示错误'],
        cause: 'unpackAlignment 设置不正确',
        diagnosis: `
            function diagnoseTextureCorruption(width, format, alignment) {
                const bytesPerPixel = { 'RGBA': 4, 'RGB': 3, 'RG': 2, 'R': 1 }[format];
                const rowBytes = width * bytesPerPixel;
                const remainder = rowBytes % alignment;

                if (remainder !== 0) {
                    console.error('对齐问题检测到:', {
                        width,
                        format,
                        alignment,
                        rowBytes,
                        remainder,
                        suggestion: '使用 unpackAlignment: 1 或调整纹理宽度'
                    });
                    return false;
                }
                return true;
            }
        `,
        solution: '调整 unpackAlignment 或纹理尺寸',
    },

    // 错误2：性能问题
    performanceIssues: {
        symptoms: ['纹理上传缓慢', 'FPS下降', '内存使用异常'],
        cause: '非最优的对齐设置',
        diagnosis: `
            function diagnosePerformance(gl, textureConfig) {
                const { width, height, format, unpackAlignment } = textureConfig;

                // 测试不同对齐的性能
                const alignments = [1, 2, 4, 8];
                const results = {};

                alignments.forEach(alignment => {
                    const testData = new Uint8Array(width * height * 4);
                    const startTime = performance.now();

                    gl.pixelStorei(gl.UNPACK_ALIGNMENT, alignment);
                    const texture = gl.createTexture();
                    gl.bindTexture(gl.TEXTURE_2D, texture);
                    gl.texImage2D(gl.TEXTURE_2D, 0, format, width, height, 0, format, gl.UNSIGNED_BYTE, testData);

                    const endTime = performance.now();
                    results[alignment] = endTime - startTime;

                    gl.deleteTexture(texture);
                });

                console.log('性能测试结果:', results);

                // 找出最快的对齐
                const fastest = Object.keys(results).reduce((a, b) =>
                    results[a] < results[b] ? a : b
                );

                console.log('推荐对齐:', fastest);
            }
        `,
    },
};
```

### 2. 调试工具

```javascript
const debuggingTools = {
    // 对齐验证器
    alignmentValidator: `
        class AlignmentValidator {
            static validate(width, height, format, type, alignment, data) {
                const report = {
                    valid: true,
                    warnings: [],
                    errors: [],
                    recommendations: [],
                };

                // 检查基本参数
                if (![1, 2, 4, 8].includes(alignment)) {
                    report.errors.push('无效的对齐值，必须是 1, 2, 4, 或 8');
                    report.valid = false;
                }

                // 检查格式兼容性
                const bytesPerPixel = this.getBytesPerPixel(format, type);
                const rowBytes = width * bytesPerPixel;

                if (rowBytes % alignment !== 0) {
                    report.warnings.push(
                        \`行字节数(\${rowBytes})不能被对齐值(\${alignment})整除\`
                    );
                    report.recommendations.push('考虑使用 unpackAlignment: 1');
                }

                // 检查数据长度
                if (data && data.length !== width * height * bytesPerPixel) {
                    report.errors.push('数据长度与纹理尺寸不匹配');
                    report.valid = false;
                }

                // 性能建议
                if (format === 'RGB' && alignment === 4) {
                    report.recommendations.push(
                        'RGB格式建议使用 unpackAlignment: 1 或转换为RGBA格式'
                    );
                }

                return report;
            }

            static getBytesPerPixel(format, type) {
                const formatBytes = {
                    'RGBA': 4, 'RGB': 3, 'RG': 2, 'RED': 1,
                    'LUMINANCE_ALPHA': 2, 'LUMINANCE': 1, 'ALPHA': 1
                };

                const typeMultiplier = {
                    'UNSIGNED_BYTE': 1,
                    'UNSIGNED_SHORT': 2,
                    'FLOAT': 4,
                    'HALF_FLOAT': 2,
                };

                return (formatBytes[format] || 4) * (typeMultiplier[type] || 1);
            }
        }
    `,

    // 内存使用分析器
    memoryAnalyzer: `
        class TextureMemoryAnalyzer {
            static analyzeMemoryUsage(textures) {
                let totalMemory = 0;
                let wastedMemory = 0;
                const analysis = [];

                textures.forEach(texture => {
                    const { width, height, format, type, unpackAlignment } = texture;
                    const bytesPerPixel = AlignmentValidator.getBytesPerPixel(format, type);

                    const rawBytesPerRow = width * bytesPerPixel;
                    const alignedBytesPerRow = Math.ceil(rawBytesPerRow / unpackAlignment) * unpackAlignment;
                    const paddingPerRow = alignedBytesPerRow - rawBytesPerRow;

                    const textureMemory = alignedBytesPerRow * height;
                    const wastedPerTexture = paddingPerRow * height;

                    totalMemory += textureMemory;
                    wastedMemory += wastedPerTexture;

                    analysis.push({
                        texture,
                        memoryUsed: textureMemory,
                        memoryWasted: wastedPerTexture,
                        efficiency: (textureMemory - wastedPerTexture) / textureMemory,
                    });
                });

                return {
                    totalMemory,
                    wastedMemory,
                    efficiency: (totalMemory - wastedMemory) / totalMemory,
                    textures: analysis,
                };
            }
        }
    `,
};
```

## 📚 总结与最佳实践

### 核心要点

1. **unpackAlignment 的本质**：控制像素数据在内存中的对齐方式，影响 CPU 到 GPU 的数据传输效率
2. **性能影响**：正确的对齐可以显著提升纹理上传和访问性能
3. **格式匹配**：不同的像素格式需要不同的对齐策略
4. **平台差异**：不同的 GPU 和平台对对齐的敏感度不同

### 实用建议

```javascript
// 快速参考指南
const quickReference = {
    RGBA纹理: 'unpackAlignment: 4 (默认，最优)',
    RGB纹理: 'unpackAlignment: 1 (避免对齐问题)',
    灰度纹理: 'unpackAlignment: 1 (单字节格式)',
    数据纹理: '根据数据类型选择，通常4字节',
    不确定时: 'unpackAlignment: 1 (最安全)',
};

// 实际应用模板
const practicalTemplate = `
    // 创建纹理时的最佳实践
    function createOptimizedTexture(gl, imageData, width, height, format) {
        // 1. 根据格式选择最佳对齐
        const alignment = getOptimalAlignment(format, width);

        // 2. 创建纹理
        const texture = new Texture(gl, {
            image: imageData,
            width,
            height,
            format,
            unpackAlignment: alignment,
            // 其他参数...
        });

        return texture;
    }

    function getOptimalAlignment(format, width) {
        const bytesPerPixel = {
            'RGBA': 4, 'RGB': 3, 'RG': 2, 'R': 1
        }[format] || 4;

        const rowBytes = width * bytesPerPixel;

        // 选择最大的可用对齐
        for (const alignment of [8, 4, 2, 1]) {
            if (rowBytes % alignment === 0) {
                return alignment;
            }
        }

        return 1; // 最安全的选择
    }
`;
```

### 关键记忆点

-   **RGBA 格式**：使用默认的 4 字节对齐，性能最佳
-   **RGB 格式**：使用 1 字节对齐，避免对齐问题
-   **数据纹理**：根据数据类型选择，Float32Array 使用 4 字节对齐
-   **调试时**：使用 1 字节对齐排除对齐相关问题
-   **性能优化**：在确保正确性的前提下选择最大的对齐值

通过理解和正确使用 unpackAlignment，可以确保 WebGL 应用在各种平台上都能获得最佳的纹理处理性能。

```

```
