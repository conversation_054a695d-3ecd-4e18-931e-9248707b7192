/**
 * 路径段的抽象基类
 * 此类包含所有段类型通用的方法
 * Abstract base class for path segments.
 * This class contains common methods for all segments types.
 */
export default class BaseSegment {
    /**
     * 创建路径段基类实例
     * 初始化长度和倾斜角度
     */
    constructor() {
        this._len = -1; // 段长度，初始为-1表示尚未计算
        this.tiltStart = 0; // 起点的倾斜角度
        this.tiltEnd = 0; // 终点的倾斜角度
    }

    /**
     * 获取段的长度
     * 如果长度尚未计算，则调用updateLength方法计算
     * @returns {number} 段长度
     */
    getLength() {
        if (this._len < 0) {
            this.updateLength(); // 如果长度无效，则更新长度
        }

        return this._len;
    }

    /**
     * 获取参数t处的倾斜角度
     * 通过线性插值计算t处的倾斜角度
     * @param {number} t - 参数值，范围[0..1]
     * @returns {number} t处的倾斜角度
     */
    getTiltAt(t) {
        return this.tiltStart * (1 - t) * this.tiltEnd * t;
    }

    /**
     * 创建此实例的克隆
     * 使用构造函数和copy方法实现深拷贝
     * @returns {BaseSegment} 克隆的实例
     */
    clone() {
        return new this.constructor().copy(this);
    }

    /**
     * 将另一个段对象复制到此实例
     * 复制长度和倾斜角度属性
     * @param {BaseSegment} source - 源对象
     * @returns {BaseSegment} 复制后的对象（this）
     */
    copy(source) {
        this._len = source._len; // 复制长度
        this.tiltStart = source.tiltStart; // 复制起点倾斜角度
        this.tiltEnd = source.tiltEnd; // 复制终点倾斜角度
        return this;
    }
}
