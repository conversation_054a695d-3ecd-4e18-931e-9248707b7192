<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Divisor与普通渲染对比</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
        }
        canvas {
            border: 1px solid #ccc;
            display: block;
            margin: 10px auto;
        }
        .code-block {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            font-size: 12px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .explanation {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Divisor与普通渲染对比实验</h1>
        
        <div class="explanation">
            <h3>🎯 实验目的</h3>
            <p>观察设置了 <code>vertexAttribDivisor</code> 但使用普通 <code>drawArrays</code> 时的行为差异</p>
        </div>

        <div class="demo-grid">
            <div class="demo-card">
                <h3>🔹 正常情况：divisor=0 + drawArrays</h3>
                <canvas id="canvas1" width="300" height="200"></canvas>
                <div class="code-block">
gl.vertexAttribDivisor(colorLocation, 0);<br>
gl.drawArrays(gl.TRIANGLES, 0, 6);
                </div>
                <button onclick="runDemo(1)">运行演示</button>
                <p><strong>预期</strong>：每个顶点使用不同颜色</p>
            </div>

            <div class="demo-card">
                <h3>🔹 异常情况：divisor=1 + drawArrays</h3>
                <canvas id="canvas2" width="300" height="200"></canvas>
                <div class="code-block">
gl.vertexAttribDivisor(colorLocation, 1);<br>
gl.drawArrays(gl.TRIANGLES, 0, 6);
                </div>
                <button onclick="runDemo(2)">运行演示</button>
                <p><strong>结果</strong>：顶点按索引读取颜色数组</p>
            </div>

            <div class="demo-card">
                <h3>🔹 异常情况：divisor=2 + drawArrays</h3>
                <canvas id="canvas3" width="300" height="200"></canvas>
                <div class="code-block">
gl.vertexAttribDivisor(colorLocation, 2);<br>
gl.drawArrays(gl.TRIANGLES, 0, 6);
                </div>
                <button onclick="runDemo(3)">运行演示</button>
                <p><strong>结果</strong>：每2个顶点共享一个颜色</p>
            </div>

            <div class="demo-card">
                <h3>🔹 正确用法：divisor=1 + drawArraysInstanced</h3>
                <canvas id="canvas4" width="300" height="200"></canvas>
                <div class="code-block">
gl.vertexAttribDivisor(colorLocation, 1);<br>
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 2);
                </div>
                <button onclick="runDemo(4)">运行演示</button>
                <p><strong>预期</strong>：每个实例使用不同颜色</p>
            </div>
        </div>

        <div class="explanation warning">
            <h3>⚠️ 重要发现</h3>
            <ul>
                <li><strong>divisor设置会影响所有渲染方式</strong>：即使用普通drawArrays也会按divisor规则读取属性</li>
                <li><strong>可能导致意外结果</strong>：顶点可能读取到错误的属性值或越界访问</li>
                <li><strong>最佳实践</strong>：divisor > 0 时应该配合 drawArraysInstanced 使用</li>
            </ul>
        </div>
    </div>

    <script>
        // 顶点着色器
        const vertexShaderSource = `
            attribute vec2 a_position;
            attribute vec3 a_color;
            varying vec3 v_color;
            
            void main() {
                gl_Position = vec4(a_position, 0.0, 1.0);
                v_color = a_color;
            }
        `;

        // 片段着色器
        const fragmentShaderSource = `
            precision mediump float;
            varying vec3 v_color;
            
            void main() {
                gl_FragColor = vec4(v_color, 1.0);
            }
        `;

        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
                gl.deleteShader(shader);
                return null;
            }
            
            return shader;
        }

        function createProgram(gl, vertexShader, fragmentShader) {
            const program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);
            
            if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                console.error('程序链接错误:', gl.getProgramInfoLog(program));
                gl.deleteProgram(program);
                return null;
            }
            
            return program;
        }

        function runDemo(demoType) {
            const canvas = document.getElementById(`canvas${demoType}`);
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                alert('WebGL不支持');
                return;
            }

            // 检查实例化渲染支持
            let instancedExt = null;
            if (!gl.drawArraysInstanced) {
                instancedExt = gl.getExtension('ANGLE_instanced_arrays');
                if (instancedExt) {
                    gl.vertexAttribDivisor = instancedExt.vertexAttribDivisorANGLE.bind(instancedExt);
                    gl.drawArraysInstanced = instancedExt.drawArraysInstancedANGLE.bind(instancedExt);
                }
            }

            // 创建着色器程序
            const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
            const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
            const program = createProgram(gl, vertexShader, fragmentShader);

            // 获取属性位置
            const positionLocation = gl.getAttribLocation(program, 'a_position');
            const colorLocation = gl.getAttribLocation(program, 'a_color');

            // 顶点数据：两个三角形
            const positions = new Float32Array([
                // 第一个三角形
                -0.8, -0.3,
                -0.4, -0.3,
                -0.6,  0.3,
                // 第二个三角形
                 0.4, -0.3,
                 0.8, -0.3,
                 0.6,  0.3
            ]);

            // 颜色数据
            const colors = new Float32Array([
                1.0, 0.0, 0.0,  // 红色
                0.0, 1.0, 0.0,  // 绿色
                0.0, 0.0, 1.0,  // 蓝色
                1.0, 1.0, 0.0,  // 黄色
                1.0, 0.0, 1.0,  // 紫色
                0.0, 1.0, 1.0   // 青色
            ]);

            // 创建缓冲区
            const positionBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

            const colorBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, colors, gl.STATIC_DRAW);

            // 清空画布
            gl.clearColor(0.9, 0.9, 0.9, 1.0);
            gl.clear(gl.COLOR_BUFFER_BIT);

            // 使用程序
            gl.useProgram(program);

            // 设置位置属性
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);
            gl.vertexAttribDivisor(positionLocation, 0);

            // 设置颜色属性（根据演示类型设置不同的divisor）
            gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
            gl.enableVertexAttribArray(colorLocation);
            gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

            // 根据演示类型设置不同的divisor和渲染方式
            switch(demoType) {
                case 1: // 正常：divisor=0 + drawArrays
                    gl.vertexAttribDivisor(colorLocation, 0);
                    gl.drawArrays(gl.TRIANGLES, 0, 6);
                    break;
                case 2: // 异常：divisor=1 + drawArrays
                    gl.vertexAttribDivisor(colorLocation, 1);
                    gl.drawArrays(gl.TRIANGLES, 0, 6);
                    break;
                case 3: // 异常：divisor=2 + drawArrays
                    gl.vertexAttribDivisor(colorLocation, 2);
                    gl.drawArrays(gl.TRIANGLES, 0, 6);
                    break;
                case 4: // 正确：divisor=1 + drawArraysInstanced
                    if (gl.drawArraysInstanced) {
                        gl.vertexAttribDivisor(colorLocation, 1);
                        gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 2);
                    } else {
                        alert('浏览器不支持实例化渲染');
                    }
                    break;
            }
        }

        // 页面加载完成后运行所有演示
        window.onload = function() {
            for(let i = 1; i <= 4; i++) {
                runDemo(i);
            }
        };
    </script>
</body>
</html>
