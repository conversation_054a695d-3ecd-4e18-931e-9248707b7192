# JavaScript 类型化数组与 WebGL 常量对应关系表

## 概述

本笔记通过表格形式清晰展示 JavaScript 类型化数组与 WebGL 数据类型常量的对应关系，便于学习和记忆。理解这些对应关系是掌握 WebGL 数据处理的基础。

## 完整对应关系表

| JavaScript 类型化数组 | WebGL 常量          | 常量值 | 数值范围                        | 字节大小 | 主要用途         | 归一化建议   |
| --------------------- | ------------------- | ------ | ------------------------------- | -------- | ---------------- | ------------ |
| `Int8Array`           | `gl.BYTE`           | 5120   | -128 到 127                     | 1 字节   | 压缩法线、切线   | 建议归一化   |
| `Uint8Array`          | `gl.UNSIGNED_BYTE`  | 5121   | 0 到 255                        | 1 字节   | 颜色数据、小索引 | 建议归一化   |
| `Int16Array`          | `gl.SHORT`          | 5122   | -32,768 到 32,767               | 2 字节   | 压缩位置数据     | 通常不归一化 |
| `Uint16Array`         | `gl.UNSIGNED_SHORT` | 5123   | 0 到 65,535                     | 2 字节   | 中型模型索引     | 不归一化     |
| `Int32Array`          | `gl.INT`            | 5124   | -2,147,483,648 到 2,147,483,647 | 4 字节   | 大整数、特殊 ID  | 不归一化     |
| `Uint32Array`         | `gl.UNSIGNED_INT`   | 5125   | 0 到 4,294,967,295              | 4 字节   | 大型模型索引     | 不归一化     |
| `Float32Array`        | `gl.FLOAT`          | 5126   | ±3.4×10^38 (约 ±340 万亿亿亿亿) | 4 字节   | 位置、法线、UV   | 不归一化     |

## 数值范围记忆技巧

### 32 位整数的实际含义

```javascript
// Int32Array: -2,147,483,648 到 2,147,483,647
// 记忆技巧：约±21亿 (2^31 = 2,147,483,648)
const maxInt32 = 2147483647;
console.log('Int32最大值:', maxInt32.toLocaleString()); // 2,147,483,647

// Uint32Array: 0 到 4,294,967,295
// 记忆技巧：约43亿 (2^32 = 4,294,967,296)
const maxUint32 = 4294967295;
console.log('Uint32最大值:', maxUint32.toLocaleString()); // 4,294,967,295

// 实际应用对比：
console.log('Uint16最大索引:', (65535).toLocaleString()); // 65,535 (约6万5千)
console.log('Uint32最大索引:', (4294967295).toLocaleString()); // 4,294,967,295 (约43亿)

// 顶点数量对比：
console.log('小型模型: < 256个顶点    → 使用Uint8Array');
console.log('中型模型: < 65,536个顶点 → 使用Uint16Array');
console.log('大型模型: < 43亿个顶点   → 使用Uint32Array');
```

## 详细特性对比表

### 整数类型详细对比

| 类型          | 位数  | 符号   | 最大顶点数               | 内存效率 | WebGL 兼容性 | 推荐场景       |
| ------------- | ----- | ------ | ------------------------ | -------- | ------------ | -------------- |
| `Uint8Array`  | 8 位  | 无符号 | 256 (约 0.3 千)          | 最高     | 完全支持     | 微型模型、颜色 |
| `Uint16Array` | 16 位 | 无符号 | 65,536 (约 6.5 万)       | 高       | 完全支持     | 常规模型索引   |
| `Uint32Array` | 32 位 | 无符号 | 4,294,967,296 (约 43 亿) | 中等     | 需要扩展     | 大型模型索引   |

### 数据压缩效果对比

| 原始类型            | 压缩类型          | 内存节省 | 精度损失 | 适用场景       |
| ------------------- | ----------------- | -------- | -------- | -------------- |
| `Float32Array` 颜色 | `Uint8Array` 颜色 | 75%      | 微小     | 大部分颜色数据 |
| `Float32Array` 法线 | `Int8Array` 法线  | 75%      | 可接受   | 法线压缩       |
| `Float32Array` 位置 | `Int16Array` 位置 | 50%      | 需要缩放 | 有限范围位置   |

## 使用场景速查表

### 按 WebGL 属性类型分类

| 属性类型     | 推荐 JavaScript 类型           | WebGL 常量                       | 归一化        | 典型配置    |
| ------------ | ------------------------------ | -------------------------------- | ------------- | ----------- |
| **位置坐标** | `Float32Array`                 | `gl.FLOAT`                       | false         | `size: 3`   |
| **法线向量** | `Float32Array` 或 `Int8Array`  | `gl.FLOAT` 或 `gl.BYTE`          | false 或 true | `size: 3`   |
| **纹理坐标** | `Float32Array`                 | `gl.FLOAT`                       | false         | `size: 2`   |
| **顶点颜色** | `Uint8Array` 或 `Float32Array` | `gl.UNSIGNED_BYTE` 或 `gl.FLOAT` | true 或 false | `size: 3/4` |
| **索引数据** | 根据顶点数选择                 | 对应常量                         | false         | 无 size     |
| **实例矩阵** | `Float32Array`                 | `gl.FLOAT`                       | false         | `size: 16`  |

### 按内存优化级别分类

| 优化级别     | JavaScript 类型             | 内存使用 | 精度 | 适用项目           |
| ------------ | --------------------------- | -------- | ---- | ------------------ |
| **极致优化** | `Uint8Array`, `Int8Array`   | 最小     | 低   | 移动端、大量数据   |
| **平衡优化** | `Uint16Array`, `Int16Array` | 中等     | 中等 | 一般 Web 应用      |
| **性能优先** | `Float32Array`              | 大       | 高   | 桌面端、高质量渲染 |
| **大型数据** | `Uint32Array`               | 很大     | 高   | 复杂模型、CAD 应用 |

## 常见配置模式

### 标准几何体配置

```javascript
// 高质量配置（桌面端推荐）
const highQualityGeometry = {
    position: { size: 3, data: new Float32Array([...]) },    // gl.FLOAT
    normal: { size: 3, data: new Float32Array([...]) },      // gl.FLOAT
    uv: { size: 2, data: new Float32Array([...]) },          // gl.FLOAT
    color: { size: 4, data: new Float32Array([...]) },       // gl.FLOAT
    index: { data: new Uint16Array([...]) }                  // gl.UNSIGNED_SHORT
};

// 内存优化配置（移动端推荐）
const optimizedGeometry = {
    position: { size: 3, data: new Float32Array([...]) },           // gl.FLOAT
    normal: { size: 3, data: new Int8Array([...]), normalized: true }, // gl.BYTE
    uv: { size: 2, data: new Float32Array([...]) },                 // gl.FLOAT
    color: { size: 4, data: new Uint8Array([...]), normalized: true }, // gl.UNSIGNED_BYTE
    index: { data: new Uint16Array([...]) }                         // gl.UNSIGNED_SHORT
};
```

### 大型模型配置

```javascript
// 大型模型配置（需要检查扩展支持）
const largeModelGeometry = {
    position: { size: 3, data: new Float32Array([...]) },    // gl.FLOAT
    normal: { size: 3, data: new Float32Array([...]) },      // gl.FLOAT
    uv: { size: 2, data: new Float32Array([...]) },          // gl.FLOAT
    index: { data: new Uint32Array([...]) }                  // gl.UNSIGNED_INT (需要扩展)
};

// 扩展检查
const ext = gl.getExtension('OES_element_index_uint');
if (!ext) {
    console.warn('不支持32位索引，需要分割模型');
}
```

## 记忆技巧

### 1. 数字规律记忆法

```
字节大小规律：
- 8位类型  → 1字节 → gl.BYTE(5120), gl.UNSIGNED_BYTE(5121)
- 16位类型 → 2字节 → gl.SHORT(5122), gl.UNSIGNED_SHORT(5123)
- 32位类型 → 4字节 → gl.INT(5124), gl.UNSIGNED_INT(5125), gl.FLOAT(5126)

常量值规律：
- 从5120开始递增
- 有符号类型在前，无符号类型在后
- FLOAT最后
```

### 2. 用途分类记忆法

```
颜色数据：Uint8Array + gl.UNSIGNED_BYTE + 归一化
索引数据：Uint16Array + gl.UNSIGNED_SHORT + 不归一化
位置数据：Float32Array + gl.FLOAT + 不归一化
压缩数据：Int8Array + gl.BYTE + 归一化
```

### 3. 性能优化记忆法

```
内存优先：选择最小的合适类型
精度优先：选择Float32Array
兼容优先：避免使用需要扩展的类型
移动优先：多使用8位和16位类型
```

## 快速决策流程图

```
开始 → 确定数据类型
  ↓
是索引数据？
  ├─ 是 → 顶点数 ≤ 256？ → 是 → Uint8Array
  │       ├─ 否 → 顶点数 ≤ 65536？ → 是 → Uint16Array
  │       └─ 否 → 检查扩展支持 → Uint32Array
  └─ 否 → 是颜色数据？
          ├─ 是 → 需要高精度？ → 否 → Uint8Array + 归一化
          │       └─ 是 → Float32Array
          └─ 否 → 是位置/法线/UV？ → Float32Array
```

## 实际代码示例对比

### 相同数据的不同表示方法

```javascript
// 示例：三角形的三个顶点颜色
const colors = [
    1.0,
    0.0,
    0.0,
    1.0, // 红色
    0.0,
    1.0,
    0.0,
    1.0, // 绿色
    0.0,
    0.0,
    1.0,
    1.0, // 蓝色
];

// 方法1：高精度浮点表示
const floatColors = new Float32Array(colors);
geometry.addAttribute('color', {
    size: 4,
    data: floatColors, // gl.FLOAT (自动推断)
    normalized: false, // 不需要归一化
});

// 方法2：内存优化表示
const byteColors = new Uint8Array([
    255,
    0,
    0,
    255, // 红色 (1.0 * 255)
    0,
    255,
    0,
    255, // 绿色
    0,
    0,
    255,
    255, // 蓝色
]);
geometry.addAttribute('color', {
    size: 4,
    data: byteColors, // gl.UNSIGNED_BYTE (自动推断)
    normalized: true, // 需要归一化到[0,1]
});

// 内存使用对比：
// Float32Array: 16个元素 × 4字节 = 64字节
// Uint8Array:   16个元素 × 1字节 = 16字节 (节省75%内存！)
```

### 索引数据的类型选择示例

```javascript
// 根据顶点数量自动选择索引类型
function createOptimalIndices(vertexCount, triangleIndices) {
    const maxIndex = Math.max(...triangleIndices);

    if (maxIndex < 256) {
        console.log('使用 Uint8Array 索引 (1字节/索引)');
        return {
            data: new Uint8Array(triangleIndices),
            type: 'gl.UNSIGNED_BYTE',
            bytesPerElement: 1
        };
    } else if (maxIndex < 65536) {
        console.log('使用 Uint16Array 索引 (2字节/索引)');
        return {
            data: new Uint16Array(triangleIndices),
            type: 'gl.UNSIGNED_SHORT',
            bytesPerElement: 2
        };
    } else {
        console.log('使用 Uint32Array 索引 (4字节/索引)');
        return {
            data: new Uint32Array(triangleIndices),
            type: 'gl.UNSIGNED_INT',
            bytesPerElement: 4
        };
    }
}

// 使用示例
const indices = [0, 1, 2, 3, 4, 5, ..., 100000];
const optimalIndices = createOptimalIndices(100001, indices);
geometry.addAttribute('index', optimalIndices);
```

## 性能基准测试对比

### 内存使用量对比（10 万个顶点）

| 属性类型           | Float32Array | 优化类型          | 内存节省 | 质量损失     |
| ------------------ | ------------ | ----------------- | -------- | ------------ |
| **位置 (x,y,z)**   | 1.2MB        | Int16Array: 600KB | 50%      | 需要缩放处理 |
| **法线 (x,y,z)**   | 1.2MB        | Int8Array: 300KB  | 75%      | 轻微精度损失 |
| **颜色 (r,g,b,a)** | 1.6MB        | Uint8Array: 400KB | 75%      | 几乎无损失   |
| **UV 坐标 (u,v)**  | 800KB        | 保持 Float32Array | 0%       | 精度要求高   |
| **总计**           | 4.8MB        | 2.1MB             | **56%**  | 可接受       |

### 传输速度对比（移动端 4G 网络）

| 数据大小            | 传输时间 | 解析时间 | 总时间  | 用户体验    |
| ------------------- | -------- | -------- | ------- | ----------- |
| **4.8MB (Float32)** | 12 秒    | 0.2 秒   | 12.2 秒 | 较差        |
| **2.1MB (优化)**    | 5.3 秒   | 0.3 秒   | 5.6 秒  | 良好        |
| **节省时间**        | 6.7 秒   | -0.1 秒  | 6.6 秒  | **54%提升** |

## 常见错误对照表

| 错误现象      | 可能原因       | JavaScript 类型 | WebGL 常量          | 解决方案                |
| ------------- | -------------- | --------------- | ------------------- | ----------------------- |
| 颜色过亮/过暗 | 归一化错误     | `Uint8Array`    | `gl.UNSIGNED_BYTE`  | 设置 `normalized: true` |
| 索引越界错误  | 类型容量不足   | `Uint16Array`   | `gl.UNSIGNED_SHORT` | 改用 `Uint32Array`      |
| 精度丢失      | 类型精度不够   | `Int8Array`     | `gl.BYTE`           | 改用 `Float32Array`     |
| 内存占用过大  | 类型选择不当   | `Float32Array`  | `gl.FLOAT`          | 考虑压缩类型            |
| 渲染异常      | 数据类型不匹配 | 混用不同类型    | 对应常量错误        | 检查类型一致性          |

## 浏览器兼容性对照表

| JavaScript 类型 | WebGL 常量          | WebGL 1.0 | WebGL 2.0 | 需要扩展 | 扩展名称                  |
| --------------- | ------------------- | --------- | --------- | -------- | ------------------------- |
| `Int8Array`     | `gl.BYTE`           | ✅        | ✅        | 否       | -                         |
| `Uint8Array`    | `gl.UNSIGNED_BYTE`  | ✅        | ✅        | 否       | -                         |
| `Int16Array`    | `gl.SHORT`          | ✅        | ✅        | 否       | -                         |
| `Uint16Array`   | `gl.UNSIGNED_SHORT` | ✅        | ✅        | 否       | -                         |
| `Int32Array`    | `gl.INT`            | ⚠️        | ✅        | 是       | `OES_vertex_array_object` |
| `Uint32Array`   | `gl.UNSIGNED_INT`   | ⚠️        | ✅        | 是       | `OES_element_index_uint`  |
| `Float32Array`  | `gl.FLOAT`          | ✅        | ✅        | 否       | -                         |

### 扩展检查代码模板

```javascript
// 检查32位整数索引支持
function checkUint32IndexSupport(gl) {
    const ext = gl.getExtension('OES_element_index_uint');
    if (ext) {
        console.log('✅ 支持32位索引');
        return true;
    } else {
        console.warn('❌ 不支持32位索引，最大顶点数: 65536');
        return false;
    }
}

// 检查32位整数顶点属性支持
function checkInt32AttributeSupport(gl) {
    const ext = gl.getExtension('OES_vertex_array_object');
    if (ext) {
        console.log('✅ 支持32位整数属性');
        return true;
    } else {
        console.warn('❌ 不支持32位整数属性');
        return false;
    }
}
```

这个对应关系表帮助你快速选择正确的数据类型组合，提高 WebGL 开发效率！通过表格对比，你可以：

1. **快速查找**：根据需求快速找到合适的类型组合
2. **性能优化**：了解不同选择的内存和性能影响
3. **避免错误**：预防常见的类型匹配和兼容性问题
4. **记忆辅助**：通过规律和技巧更好地记住对应关系
