<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>WebGL API调用次数统计</title>
        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                background-color: #f0f0f0;
            }

            .container {
                max-width: 1000px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }

            .comparison {
                display: flex;
                gap: 20px;
                margin: 20px 0;
            }

            .method {
                flex: 1;
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 15px;
            }

            .method.traditional {
                border-color: #ff6b6b;
            }

            .method.vao {
                border-color: #4ecdc4;
            }

            canvas {
                border: 1px solid #333;
                display: block;
                margin: 10px auto;
                background-color: #000;
            }

            .call-counter {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 15px;
                margin: 10px 0;
                font-family: 'Courier New', monospace;
            }

            .call-item {
                display: flex;
                justify-content: space-between;
                padding: 2px 0;
                border-bottom: 1px solid #eee;
            }

            .call-item:last-child {
                border-bottom: none;
            }

            .call-name {
                color: #0066cc;
            }

            .call-count {
                color: #cc0000;
                font-weight: bold;
            }

            .total {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
                text-align: center;
                font-weight: bold;
                font-size: 18px;
            }

            .controls {
                text-align: center;
                margin: 20px 0;
            }

            button {
                background-color: #4caf50;
                color: white;
                border: none;
                padding: 10px 20px;
                margin: 0 10px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 16px;
            }

            button:hover {
                background-color: #45a049;
            }

            .explanation {
                background-color: #e7f3ff;
                border: 1px solid #b3d9ff;
                border-radius: 4px;
                padding: 15px;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>WebGL API调用次数精确统计</h1>

            <div class="explanation">
                <h3>📊 统计说明</h3>
                <p>这个示例会拦截并统计每次渲染过程中的WebGL API调用次数，让您直观看到两种方式的差异。</p>
                <p><strong>注意</strong>：统计的是每次 <code>render()</code> 方法调用时的WebGL API次数，不包括初始化阶段。</p>
            </div>

            <div class="comparison">
                <div class="method traditional">
                    <h3 style="color: #ff6b6b">🔄 传统方式</h3>
                    <canvas id="traditionalCanvas" width="250" height="150"></canvas>

                    <div class="call-counter">
                        <h4>每次渲染的WebGL调用：</h4>
                        <div id="traditionalCalls"></div>
                    </div>

                    <div class="total" id="traditionalTotal">总调用次数: 0</div>
                </div>

                <div class="method vao">
                    <h3 style="color: #4ecdc4">🚀 VAO方式</h3>
                    <canvas id="vaoCanvas" width="250" height="150"></canvas>

                    <div class="call-counter">
                        <h4>每次渲染的WebGL调用：</h4>
                        <div id="vaoCalls"></div>
                    </div>

                    <div class="total" id="vaoTotal">总调用次数: 0</div>
                </div>
            </div>

            <div class="controls">
                <button id="renderOnce">渲染一次</button>
                <button id="renderMultiple">连续渲染10次</button>
                <button id="reset">重置计数器</button>
            </div>

            <div class="explanation">
                <h3>🔍 详细分析</h3>
                <h4>传统方式每次渲染需要的WebGL调用：</h4>
                <ol>
                    <li><code>gl.clear()</code> - 清除画布</li>
                    <li><code>gl.useProgram()</code> - 使用着色器程序</li>
                    <li><code>gl.bindBuffer()</code> - 绑定位置缓冲区</li>
                    <li><code>gl.enableVertexAttribArray()</code> - 启用位置属性</li>
                    <li><code>gl.vertexAttribPointer()</code> - 配置位置属性</li>
                    <li><code>gl.bindBuffer()</code> - 绑定颜色缓冲区</li>
                    <li><code>gl.enableVertexAttribArray()</code> - 启用颜色属性</li>
                    <li><code>gl.vertexAttribPointer()</code> - 配置颜色属性</li>
                    <li><code>gl.drawArrays()</code> - 绘制</li>
                </ol>

                <h4>VAO方式每次渲染需要的WebGL调用：</h4>
                <ol>
                    <li><code>gl.clear()</code> - 清除画布</li>
                    <li><code>gl.useProgram()</code> - 使用着色器程序</li>
                    <li><code>gl.bindVertexArray()</code> - 绑定VAO（恢复所有顶点状态）</li>
                    <li><code>gl.drawArrays()</code> - 绘制</li>
                </ol>
            </div>
        </div>

        <script>
            // WebGL API调用计数器
            class WebGLCallCounter {
                constructor(gl, name) {
                    this.gl = gl;
                    this.name = name;
                    this.calls = {};
                    this.totalCalls = 0;
                    this.isRecording = false;

                    // 需要监控的WebGL方法
                    this.methodsToTrack = ['clear', 'useProgram', 'bindBuffer', 'enableVertexAttribArray', 'vertexAttribPointer', 'drawArrays', 'bindVertexArray'];

                    this.setupInterceptors();
                }

                setupInterceptors() {
                    this.originalMethods = {};

                    this.methodsToTrack.forEach((method) => {
                        if (this.gl[method]) {
                            this.originalMethods[method] = this.gl[method].bind(this.gl);

                            this.gl[method] = (...args) => {
                                if (this.isRecording) {
                                    this.calls[method] = (this.calls[method] || 0) + 1;
                                    this.totalCalls++;
                                }
                                return this.originalMethods[method](...args);
                            };
                        }
                    });
                }

                startRecording() {
                    this.calls = {};
                    this.totalCalls = 0;
                    this.isRecording = true;
                }

                stopRecording() {
                    this.isRecording = false;
                    return {
                        calls: { ...this.calls },
                        total: this.totalCalls,
                    };
                }

                reset() {
                    this.calls = {};
                    this.totalCalls = 0;
                }
            }

            // 顶点着色器
            const vertexShaderSource = `
            attribute vec3 a_position;
            attribute vec4 a_color;
            varying vec4 v_color;
            void main() {
                gl_Position = vec4(a_position, 1.0);
                v_color = a_color;
            }
        `;

            // 片段着色器
            const fragmentShaderSource = `
            precision mediump float;
            varying vec4 v_color;
            void main() {
                gl_FragColor = v_color;
            }
        `;

            // 创建着色器程序的辅助函数
            function createShader(gl, type, source) {
                const shader = gl.createShader(type);
                gl.shaderSource(shader, source);
                gl.compileShader(shader);
                return shader;
            }

            function createShaderProgram(gl, vertexSource, fragmentSource) {
                const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexSource);
                const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentSource);

                const program = gl.createProgram();
                gl.attachShader(program, vertexShader);
                gl.attachShader(program, fragmentShader);
                gl.linkProgram(program);

                return program;
            }

            // 传统方式渲染器
            class TraditionalRenderer {
                constructor(canvas) {
                    this.canvas = canvas;
                    this.gl = canvas.getContext('webgl');
                    this.counter = new WebGLCallCounter(this.gl, 'Traditional');
                    this.init();
                }

                init() {
                    const gl = this.gl;

                    // 创建着色器程序
                    this.program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

                    // 顶点数据
                    const positions = new Float32Array([0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0]);

                    const colors = new Float32Array([1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0]);

                    // 创建缓冲区
                    this.positionBuffer = gl.createBuffer();
                    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
                    gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

                    this.colorBuffer = gl.createBuffer();
                    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
                    gl.bufferData(gl.ARRAY_BUFFER, colors, gl.STATIC_DRAW);

                    // 获取属性位置
                    this.positionLocation = gl.getAttribLocation(this.program, 'a_position');
                    this.colorLocation = gl.getAttribLocation(this.program, 'a_color');

                    // 设置视口
                    gl.viewport(0, 0, this.canvas.width, this.canvas.height);
                    gl.clearColor(0.1, 0.1, 0.1, 1.0);
                }

                render() {
                    const gl = this.gl;

                    // 开始记录WebGL调用
                    this.counter.startRecording();

                    // 🔄 传统方式渲染 - 每次都要重新设置所有状态
                    gl.clear(gl.COLOR_BUFFER_BIT); // 调用1
                    gl.useProgram(this.program); // 调用2

                    // 设置位置属性
                    gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer); // 调用3
                    gl.enableVertexAttribArray(this.positionLocation); // 调用4
                    gl.vertexAttribPointer(this.positionLocation, 3, gl.FLOAT, false, 0, 0); // 调用5

                    // 设置颜色属性
                    gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer); // 调用6
                    gl.enableVertexAttribArray(this.colorLocation); // 调用7
                    gl.vertexAttribPointer(this.colorLocation, 4, gl.FLOAT, false, 0, 0); // 调用8

                    gl.drawArrays(gl.TRIANGLES, 0, 3); // 调用9

                    // 停止记录并返回结果
                    return this.counter.stopRecording();
                }
            }

            // VAO方式渲染器
            class VAORenderer {
                constructor(canvas) {
                    this.canvas = canvas;
                    this.gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
                    this.counter = new WebGLCallCounter(this.gl, 'VAO');

                    // 检查VAO支持
                    if (!this.gl.createVertexArray) {
                        const ext = this.gl.getExtension('OES_vertex_array_object');
                        if (ext) {
                            this.gl.createVertexArray = ext.createVertexArrayOES.bind(ext);
                            this.gl.bindVertexArray = ext.bindVertexArrayOES.bind(ext);
                            this.gl.deleteVertexArray = ext.deleteVertexArrayOES.bind(ext);
                        }
                    }

                    this.init();
                }

                init() {
                    const gl = this.gl;

                    // 创建着色器程序
                    this.program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

                    // 顶点数据
                    const positions = new Float32Array([0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0]);

                    const colors = new Float32Array([1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0]);

                    // 🚀 创建VAO并一次性配置所有状态
                    this.vao = gl.createVertexArray();
                    gl.bindVertexArray(this.vao);

                    // 配置位置属性
                    const positionBuffer = gl.createBuffer();
                    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
                    gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

                    const positionLocation = gl.getAttribLocation(this.program, 'a_position');
                    gl.enableVertexAttribArray(positionLocation);
                    gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

                    // 配置颜色属性
                    const colorBuffer = gl.createBuffer();
                    gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
                    gl.bufferData(gl.ARRAY_BUFFER, colors, gl.STATIC_DRAW);

                    const colorLocation = gl.getAttribLocation(this.program, 'a_color');
                    gl.enableVertexAttribArray(colorLocation);
                    gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);

                    // 解绑VAO
                    gl.bindVertexArray(null);

                    // 设置视口
                    gl.viewport(0, 0, this.canvas.width, this.canvas.height);
                    gl.clearColor(0.1, 0.1, 0.1, 1.0);
                }

                render() {
                    const gl = this.gl;

                    // 开始记录WebGL调用
                    this.counter.startRecording();

                    // 🚀 VAO方式渲染 - 只需要很少的调用
                    gl.clear(gl.COLOR_BUFFER_BIT); // 调用1
                    gl.useProgram(this.program); // 调用2
                    gl.bindVertexArray(this.vao); // 调用3 - 这一个调用恢复了所有顶点状态！
                    gl.drawArrays(gl.TRIANGLES, 0, 3); // 调用4

                    // 停止记录并返回结果
                    return this.counter.stopRecording();
                }
            }

            // 显示调用统计
            function displayCallStats(stats, containerId, totalId) {
                const container = document.getElementById(containerId);
                const totalElement = document.getElementById(totalId);

                container.innerHTML = '';

                Object.entries(stats.calls).forEach(([method, count]) => {
                    const div = document.createElement('div');
                    div.className = 'call-item';
                    div.innerHTML = `
                    <span class="call-name">gl.${method}()</span>
                    <span class="call-count">${count}次</span>
                `;
                    container.appendChild(div);
                });

                totalElement.textContent = `总调用次数: ${stats.total}`;
            }

            // 初始化
            let traditionalRenderer, vaoRenderer;

            window.addEventListener('load', () => {
                const traditionalCanvas = document.getElementById('traditionalCanvas');
                const vaoCanvas = document.getElementById('vaoCanvas');

                traditionalRenderer = new TraditionalRenderer(traditionalCanvas);
                vaoRenderer = new VAORenderer(vaoCanvas);

                // 初始渲染一次
                renderOnce();

                // 设置按钮事件
                document.getElementById('renderOnce').addEventListener('click', renderOnce);
                document.getElementById('renderMultiple').addEventListener('click', renderMultiple);
                document.getElementById('reset').addEventListener('click', resetCounters);
            });

            function renderOnce() {
                const traditionalStats = traditionalRenderer.render();
                const vaoStats = vaoRenderer.render();

                displayCallStats(traditionalStats, 'traditionalCalls', 'traditionalTotal');
                displayCallStats(vaoStats, 'vaoCalls', 'vaoTotal');
            }

            function renderMultiple() {
                let traditionalTotal = 0;
                let vaoTotal = 0;

                for (let i = 0; i < 10; i++) {
                    const traditionalStats = traditionalRenderer.render();
                    const vaoStats = vaoRenderer.render();

                    traditionalTotal += traditionalStats.total;
                    vaoTotal += vaoStats.total;
                }

                document.getElementById('traditionalTotal').textContent = `总调用次数: ${traditionalTotal} (10次渲染)`;
                document.getElementById('vaoTotal').textContent = `总调用次数: ${vaoTotal} (10次渲染)`;
            }

            function resetCounters() {
                traditionalRenderer.counter.reset();
                vaoRenderer.counter.reset();

                document.getElementById('traditionalCalls').innerHTML = '';
                document.getElementById('vaoCalls').innerHTML = '';
                document.getElementById('traditionalTotal').textContent = '总调用次数: 0';
                document.getElementById('vaoTotal').textContent = '总调用次数: 0';
            }
        </script>
    </body>
</html>
