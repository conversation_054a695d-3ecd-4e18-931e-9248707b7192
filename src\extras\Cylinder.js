import { Geometry } from '../core/Geometry.js';
import { Vec3 } from '../math/Vec3.js';

/**
 * 圆柱体几何体类
 * 可以创建圆柱体、圆锥体或截锥体
 */
export class Cylinder extends Geometry {
    /**
     * 创建一个圆柱体几何体
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} [options] - 配置选项
     * @param {Number} [options.radiusTop=0.5] - 顶部半径
     * @param {Number} [options.radiusBottom=0.5] - 底部半径
     * @param {Number} [options.height=1] - 高度
     * @param {Number} [options.radialSegments=8] - 径向分段数（围绕Y轴）
     * @param {Number} [options.heightSegments=1] - 高度分段数（沿Y轴）
     * @param {Boolean} [options.openEnded=false] - 是否开口（不封顶）
     * @param {Number} [options.thetaStart=0] - 起始角度
     * @param {Number} [options.thetaLength=Math.PI*2] - 角度跨度
     * @param {Object} [options.attributes={}] - 自定义几何体属性
     */
    constructor(gl, { radiusTop = 0.5, radiusBottom = 0.5, height = 1, radialSegments = 8, heightSegments = 1, openEnded = false, thetaStart = 0, thetaLength = Math.PI * 2, attributes = {} } = {}) {
        // 简化变量名
        const rSegs = radialSegments;
        const hSegs = heightSegments;
        const tStart = thetaStart;
        const tLength = thetaLength;

        // 计算顶部和底部的数量
        // 如果开口，则没有顶部和底部
        // 如果顶部或底部半径为0，则只有一个底部或顶部
        const numCaps = openEnded ? 0 : radiusBottom && radiusTop ? 2 : 1;

        // 计算顶点和索引数量
        const num = (rSegs + 1) * (hSegs + 1 + numCaps) + numCaps; // 顶点总数
        const numIndices = rSegs * hSegs * 6 + numCaps * rSegs * 3; // 索引总数

        // 创建数据数组
        const position = new Float32Array(num * 3); // 顶点位置
        const normal = new Float32Array(num * 3); // 顶点法线
        const uv = new Float32Array(num * 2); // 纹理坐标
        // 根据顶点数选择合适的索引类型
        const index = num > 65536 ? new Uint32Array(numIndices) : new Uint16Array(numIndices);

        // 初始化计数器
        let i = 0; // 顶点计数器
        let ii = 0; // 索引计数器
        const indexArray = []; // 存储每行的顶点索引

        // 创建圆柱体侧面
        addHeight();

        // 如果不是开口的，添加顶部和底部
        if (!openEnded) {
            if (radiusTop) addCap(true); // 添加顶部
            if (radiusBottom) addCap(false); // 添加底部
        }

        /**
         * 创建圆柱体侧面
         * 通过在高度方向上创建多个环，然后连接它们形成侧面
         */
        function addHeight() {
            let x, y;
            const n = new Vec3();
            // 计算侧面的斜率，用于法线计算
            const slope = (radiusBottom - radiusTop) / height;

            // 沿高度方向创建顶点
            for (y = 0; y <= hSegs; y++) {
                const indexRow = []; // 存储当前行的顶点索引
                const v = y / hSegs; // 纹理坐标v [0, 1]

                // 计算当前高度的半径（线性插值）
                const r = v * (radiusBottom - radiusTop) + radiusTop;

                // 围绕Y轴创建一圈顶点
                for (x = 0; x <= rSegs; x++) {
                    const u = x / rSegs; // 纹理坐标u [0, 1]
                    const theta = u * tLength + tStart; // 当前角度
                    const sinTheta = Math.sin(theta);
                    const cosTheta = Math.cos(theta);

                    // 设置顶点位置
                    // x = r * sin(θ), y = height * (0.5 - v), z = r * cos(θ)
                    position.set([r * sinTheta, (0.5 - v) * height, r * cosTheta], i * 3);

                    // 计算法线向量（考虑斜率）
                    n.set(sinTheta, slope, cosTheta).normalize();
                    normal.set([n.x, n.y, n.z], i * 3);

                    // 设置纹理坐标
                    uv.set([u, 1 - v], i * 2);

                    // 存储顶点索引
                    indexRow.push(i++);
                }
                indexArray.push(indexRow);
            }

            // 创建三角形索引
            for (x = 0; x < rSegs; x++) {
                for (y = 0; y < hSegs; y++) {
                    // 获取四边形的四个顶点索引
                    const a = indexArray[y][x]; // 左上
                    const b = indexArray[y + 1][x]; // 左下
                    const c = indexArray[y + 1][x + 1]; // 右下
                    const d = indexArray[y][x + 1]; // 右上

                    // 创建两个三角形（一个四边形）
                    index.set([a, b, d, b, c, d], ii * 3);
                    ii += 2;
                }
            }
        }

        /**
         * 创建圆柱体的顶部或底部
         * @param {Boolean} isTop - 是否是顶部
         */
        function addCap(isTop) {
            let x;
            // 根据是顶部还是底部选择半径
            const r = isTop === true ? radiusTop : radiusBottom;
            // 根据是顶部还是底部选择方向（影响法线和位置）
            const sign = isTop === true ? 1 : -1;

            // 创建中心点
            const centerIndex = i;
            position.set([0, 0.5 * height * sign, 0], i * 3); // 中心点位置
            normal.set([0, sign, 0], i * 3); // 法线指向上或下
            uv.set([0.5, 0.5], i * 2); // 纹理中心
            i++;

            // 创建圆周上的点
            for (x = 0; x <= rSegs; x++) {
                const u = x / rSegs;
                const theta = u * tLength + tStart;
                const cosTheta = Math.cos(theta);
                const sinTheta = Math.sin(theta);

                // 设置顶点位置
                position.set([r * sinTheta, 0.5 * height * sign, r * cosTheta], i * 3);
                // 法线指向上或下
                normal.set([0, sign, 0], i * 3);
                // 设置纹理坐标（将圆形映射到正方形纹理）
                uv.set([cosTheta * 0.5 + 0.5, sinTheta * 0.5 * sign + 0.5], i * 2);
                i++;
            }

            // 创建三角形索引（从中心点到圆周形成扇形）
            for (x = 0; x < rSegs; x++) {
                const j = centerIndex + x + 1;
                if (isTop) {
                    // 顶部的三角形顺序（顺时针）
                    index.set([j, j + 1, centerIndex], ii * 3);
                } else {
                    // 底部的三角形顺序（逆时针，保证法线朝外）
                    index.set([j + 1, j, centerIndex], ii * 3);
                }
                ii++;
            }
        }

        // 设置几何体属性
        Object.assign(attributes, {
            position: { size: 3, data: position },
            normal: { size: 3, data: normal },
            uv: { size: 2, data: uv },
            index: { data: index },
        });

        // 调用父类构造函数
        super(gl, attributes);
    }
}
