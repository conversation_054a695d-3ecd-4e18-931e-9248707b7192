* {
    box-sizing: border-box;
}

body,
html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    height: 100%;
    overscroll-behavior-y: contain;
}

body,
button,
p,
ul {
    margin: 0;
    padding: 0;
}

button,
select {
    border: none;
    outline: none;
    background: none;
    font-family: inherit;
}

a,
button,
input,
select,
textarea {
    -webkit-tap-highlight-color: transparent;
}

a {
    color: inherit; /* blue colors for links too */
    text-decoration: inherit; /* no underline */
}

:root {
    overflow-x: hidden;
    height: 100%;
}

@font-face {
    font-family: 'Raleway';
    src: url('fonts/raleway-regular-webfont.woff2') format('woff2'),
         url('fonts/raleway-regular-webfont.woff') format('woff');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Raleway';
    src: url('fonts/raleway-bold-webfont.woff2') format('woff2'),
         url('fonts/raleway-bold-webfont.woff') format('woff');
    font-weight: 700;
    font-style: normal;
}

body {
    font-family: 'Raleway', sans-serif;
    font-weight: 700;
    font-size: 14px;
}

.Side {
    position: relative;
    width: 300px;
    height: 100%;
    overflow: auto;
    padding-bottom: 100px;
    padding-left: 20px;
    padding-right: 40px;
    background: #fff;
}
.Iframe {
    position: absolute;
    left: 300px;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    width: calc(100% - 300px);
    border: none;
    margin: 0;
}
.Title {
    font-size: 18px;
    margin: 20px 0;
    display: block;
}
.SubTitle {
    font-weight: 400;
    font-size: 17px;
    margin: 20px 0 50px;
}
.Section {
    font-weight: 400;
    margin: 40px 0 20px;
    padding-left: 15px;
}
.Example {
    margin: 10px 0;
    padding-left: 30px;
    display: block;
}
.Example.active {
    background-color: #3dd6ff;
    margin: 0px -40px 0px -20px;
    padding: 5px 0px 5px 50px;
    font-weight: bold;
    color: white;
}
div.Example {
    opacity: 0.2;
}

.SideIcon,
.CodeIcon {
    position: absolute;
    display: block;
    z-index: 1;
    margin: 30px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background: #fff;
    border-radius: 100%;
    font-size: 19px;
    letter-spacing: -0.1em;
    box-shadow: 1px 1px 6px rgba(0, 0, 0, 0.1);
}
.CodeIcon {
    bottom: 0;
    right: 0;
}
.SideIcon {
    top: 20px;
    left: 245px;
    display: none;
}

@media (max-width: 600px) {
    .Side {
        z-index: 1;
    }
    .Iframe {
        left: 0;
        width: 100%;
    }
    .SideIcon {
        display: block;
    }
    [data-hideSidebar] .Side {
        transform: translateX(-100%);
    }

    [data-hideSidebar] .SideIcon {
        left: 0;
        transform: rotate(180deg);
    }
}

.Info {
    padding: 20px;
    position: relative;
    z-index: 1;
}
.Info a {
    color: rgb(39, 124, 235);
}
canvas {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

#dropdown {
    margin: 1em 0em;
}
