// TODO: 重心坐标代码不应该在这里，但应该放在哪里？
// TODO: 实现球体投射？

import { Vec2 } from '../math/Vec2.js';
import { Vec3 } from '../math/Vec3.js';
import { Mat4 } from '../math/Mat4.js';

// 预先创建临时向量以避免重复创建对象
const tempVec2a = /* @__PURE__ */ new Vec2();
const tempVec2b = /* @__PURE__ */ new Vec2();
const tempVec2c = /* @__PURE__ */ new Vec2();

const tempVec3a = /* @__PURE__ */ new Vec3();
const tempVec3b = /* @__PURE__ */ new Vec3();
const tempVec3c = /* @__PURE__ */ new Vec3();
const tempVec3d = /* @__PURE__ */ new Vec3();
const tempVec3e = /* @__PURE__ */ new Vec3();
const tempVec3f = /* @__PURE__ */ new Vec3();
const tempVec3g = /* @__PURE__ */ new Vec3();
const tempVec3h = /* @__PURE__ */ new Vec3();
const tempVec3i = /* @__PURE__ */ new Vec3();
const tempVec3j = /* @__PURE__ */ new Vec3();
const tempVec3k = /* @__PURE__ */ new Vec3();

const tempMat4 = /* @__PURE__ */ new Mat4();

/**
 * 射线投射类
 * 用于检测射线与3D对象的交点
 */
export class Raycast {
    /**
     * 创建一个射线投射实例
     */
    constructor() {
        this.origin = new Vec3(); // 射线起点
        this.direction = new Vec3(); // 射线方向（归一化）
    }

    /**
     * 从鼠标位置创建射线
     * @param {Object} camera - 相机对象
     * @param {Array} [mouse=[0,0]] - 归一化的鼠标坐标，范围[-1, 1]
     */
    castMouse(camera, mouse = [0, 0]) {
        if (camera.type === 'orthographic') {
            // 设置射线起点
            // 由于相机是正交的，起点不是相机位置
            const { left, right, bottom, top, zoom } = camera;
            // 计算鼠标在世界空间中的x和y坐标
            const x = left / zoom + ((right - left) / zoom) * (mouse[0] * 0.5 + 0.5);
            const y = bottom / zoom + ((top - bottom) / zoom) * (mouse[1] * 0.5 + 0.5);
            this.origin.set(x, y, 0);
            // 将起点从相机局部空间转换到世界空间
            this.origin.applyMatrix4(camera.worldMatrix);

            // 设置射线方向
            // 正交相机的射线方向就是相机的前方向（-z轴）
            // https://community.khronos.org/t/get-direction-from-transformation-matrix-or-quat/65502/2
            this.direction.x = -camera.worldMatrix[8]; // 矩阵的第三列是相机的前方向
            this.direction.y = -camera.worldMatrix[9];
            this.direction.z = -camera.worldMatrix[10];
        } else {
            // 透视相机

            // 设置射线起点为相机位置
            camera.worldMatrix.getTranslation(this.origin);

            // 设置射线方向
            // 将鼠标坐标转换为世界空间中的点，然后计算从相机到该点的方向
            this.direction.set(mouse[0], mouse[1], 0.5);
            camera.unproject(this.direction); // 将点从裁剪空间转换到世界空间
            this.direction.sub(this.origin).normalize(); // 计算方向并归一化
        }
    }

    /**
     * 检测射线与物体包围盒的交点
     * @param {Array|Object} meshes - 要检测的网格对象或数组
     * @param {Object} [options] - 配置选项
     * @param {Number} [options.maxDistance] - 最大检测距离
     * @param {Array} [options.output=[]] - 输出结果的数组
     * @returns {Array} 与射线相交的网格数组，按距离排序
     */
    intersectBounds(meshes, { maxDistance, output = [] } = {}) {
        // 确保meshes是数组
        if (!Array.isArray(meshes)) meshes = [meshes];

        // 使用临时变量避免创建新对象
        const invWorldMat4 = tempMat4; // 逆世界矩阵
        const origin = tempVec3a; // 局部空间中的射线起点
        const direction = tempVec3b; // 局部空间中的射线方向

        // 使用传入的输出数组
        const hits = output;
        hits.length = 0; // 清空数组

        meshes.forEach((mesh) => {
            // 确保包围盒已计算
            if (!mesh.geometry.bounds || mesh.geometry.bounds.radius === Infinity) mesh.geometry.computeBoundingSphere();
            const bounds = mesh.geometry.bounds;
            // 计算网格的逆世界矩阵
            invWorldMat4.inverse(mesh.worldMatrix);

            // 计算局部空间中的最大距离
            let localMaxDistance;
            if (maxDistance) {
                direction.copy(this.direction).scaleRotateMatrix4(invWorldMat4);
                localMaxDistance = maxDistance * direction.len();
            }

            // 将世界空间中的射线转换到物体的局部空间
            origin.copy(this.origin).applyMatrix4(invWorldMat4);
            direction.copy(this.direction).transformDirection(invWorldMat4);

            // 如果包围盒太远，提前退出
            if (maxDistance) {
                if (origin.distance(bounds.center) - bounds.radius > localMaxDistance) return;
            }

            let localDistance = 0;

            // 检查射线起点是否在包围盒内部
            if (mesh.geometry.raycast === 'sphere') {
                // 球体包围盒
                if (origin.distance(bounds.center) > bounds.radius) {
                    // 起点在球体外部，计算交点距离
                    localDistance = this.intersectSphere(bounds, origin, direction);
                    if (!localDistance) return; // 没有交点，跳过
                }
            } else {
                // 轴对齐包围盒(AABB)
                if (origin.x < bounds.min.x || origin.x > bounds.max.x || origin.y < bounds.min.y || origin.y > bounds.max.y || origin.z < bounds.min.z || origin.z > bounds.max.z) {
                    // 起点在包围盒外部，计算交点距离
                    localDistance = this.intersectBox(bounds, origin, direction);
                    if (!localDistance) return; // 没有交点，跳过
                }
            }

            // 如果交点距离超过最大距离，跳过
            if (maxDistance && localDistance > localMaxDistance) return;

            // 在网格上创建hit对象，避免生成大量新对象
            if (!mesh.hit) mesh.hit = { localPoint: new Vec3(), point: new Vec3() };

            // 计算局部空间中的交点
            mesh.hit.localPoint.copy(direction).multiply(localDistance).add(origin);
            // 将交点转换到世界空间
            mesh.hit.point.copy(mesh.hit.localPoint).applyMatrix4(mesh.worldMatrix);
            // 计算交点到射线起点的距离
            mesh.hit.distance = mesh.hit.point.distance(this.origin);

            // 添加到结果数组
            hits.push(mesh);
        });

        // 按距离排序
        hits.sort((a, b) => a.hit.distance - b.hit.distance);
        return hits;
    }

    intersectMeshes(meshes, { cullFace = true, maxDistance, includeUV = true, includeNormal = true, output = [] } = {}) {
        // Test bounds first before testing geometry
        const hits = this.intersectBounds(meshes, { maxDistance, output });
        if (!hits.length) return hits;

        const invWorldMat4 = tempMat4;
        const origin = tempVec3a;
        const direction = tempVec3b;
        const a = tempVec3c;
        const b = tempVec3d;
        const c = tempVec3e;
        const closestFaceNormal = tempVec3f;
        const faceNormal = tempVec3g;
        const barycoord = tempVec3h;
        const uvA = tempVec2a;
        const uvB = tempVec2b;
        const uvC = tempVec2c;

        for (let i = hits.length - 1; i >= 0; i--) {
            const mesh = hits[i];
            invWorldMat4.inverse(mesh.worldMatrix);

            // Get max distance locally
            let localMaxDistance;
            if (maxDistance) {
                direction.copy(this.direction).scaleRotateMatrix4(invWorldMat4);
                localMaxDistance = maxDistance * direction.len();
            }

            // Take world space ray and make it object space to align with bounding box
            origin.copy(this.origin).applyMatrix4(invWorldMat4);
            direction.copy(this.direction).transformDirection(invWorldMat4);

            let localDistance = 0;
            let closestA, closestB, closestC;

            const geometry = mesh.geometry;
            const attributes = geometry.attributes;
            const index = attributes.index;
            const position = attributes.position;

            const start = Math.max(0, geometry.drawRange.start);
            const end = Math.min(index ? index.count : position.count, geometry.drawRange.start + geometry.drawRange.count);
            // Data loaded shouldn't haave stride, only buffers
            // const stride = position.stride ? position.stride / position.data.BYTES_PER_ELEMENT : position.size;
            const stride = position.size;

            for (let j = start; j < end; j += 3) {
                // Position attribute indices for each triangle
                const ai = index ? index.data[j] : j;
                const bi = index ? index.data[j + 1] : j + 1;
                const ci = index ? index.data[j + 2] : j + 2;

                a.fromArray(position.data, ai * stride);
                b.fromArray(position.data, bi * stride);
                c.fromArray(position.data, ci * stride);

                const distance = this.intersectTriangle(a, b, c, cullFace, origin, direction, faceNormal);
                if (!distance) continue;

                // Too far away
                if (maxDistance && distance > localMaxDistance) continue;

                if (!localDistance || distance < localDistance) {
                    localDistance = distance;
                    closestA = ai;
                    closestB = bi;
                    closestC = ci;
                    closestFaceNormal.copy(faceNormal);
                }
            }

            if (!localDistance) hits.splice(i, 1);

            // Update hit values from bounds-test
            mesh.hit.localPoint.copy(direction).multiply(localDistance).add(origin);
            mesh.hit.point.copy(mesh.hit.localPoint).applyMatrix4(mesh.worldMatrix);
            mesh.hit.distance = mesh.hit.point.distance(this.origin);

            // Add unique hit objects on mesh to avoid generating lots of objects
            if (!mesh.hit.faceNormal) {
                mesh.hit.localFaceNormal = new Vec3();
                mesh.hit.faceNormal = new Vec3();
                mesh.hit.uv = new Vec2();
                mesh.hit.localNormal = new Vec3();
                mesh.hit.normal = new Vec3();
            }

            // Add face normal data which is already computed
            mesh.hit.localFaceNormal.copy(closestFaceNormal);
            mesh.hit.faceNormal.copy(mesh.hit.localFaceNormal).transformDirection(mesh.worldMatrix);

            // Optional data, opt out to optimise a bit if necessary
            if (includeUV || includeNormal) {
                // Calculate barycoords to find uv values at hit point
                a.fromArray(position.data, closestA * 3);
                b.fromArray(position.data, closestB * 3);
                c.fromArray(position.data, closestC * 3);
                this.getBarycoord(mesh.hit.localPoint, a, b, c, barycoord);
            }

            if (includeUV && attributes.uv) {
                uvA.fromArray(attributes.uv.data, closestA * 2);
                uvB.fromArray(attributes.uv.data, closestB * 2);
                uvC.fromArray(attributes.uv.data, closestC * 2);
                mesh.hit.uv.set(uvA.x * barycoord.x + uvB.x * barycoord.y + uvC.x * barycoord.z, uvA.y * barycoord.x + uvB.y * barycoord.y + uvC.y * barycoord.z);
            }

            if (includeNormal && attributes.normal) {
                a.fromArray(attributes.normal.data, closestA * 3);
                b.fromArray(attributes.normal.data, closestB * 3);
                c.fromArray(attributes.normal.data, closestC * 3);
                mesh.hit.localNormal.set(
                    a.x * barycoord.x + b.x * barycoord.y + c.x * barycoord.z,
                    a.y * barycoord.x + b.y * barycoord.y + c.y * barycoord.z,
                    a.z * barycoord.x + b.z * barycoord.y + c.z * barycoord.z
                );

                mesh.hit.normal.copy(mesh.hit.localNormal).transformDirection(mesh.worldMatrix);
            }
        }

        hits.sort((a, b) => a.hit.distance - b.hit.distance);
        return hits;
    }

    intersectPlane(plane, origin = this.origin, direction = this.direction) {
        const xminp = tempVec3a;
        xminp.sub(plane.origin, origin);

        const a = xminp.dot(plane.normal);
        const b = direction.dot(plane.normal);
        // Assuming we don't want to count a ray parallel to the plane as intersecting
        if (b == 0) return 0;
        const delta = a / b;
        if (delta <= 0) return 0;
        return origin.add(direction.scale(delta));
    }

    /**
     * 检测射线与球体的交点
     * @param {Object} sphere - 球体对象，包含center和radius属性
     * @param {Vec3} [origin=this.origin] - 射线起点
     * @param {Vec3} [direction=this.direction] - 射线方向
     * @returns {Number} 交点距离，如果没有交点则返回0
     */
    intersectSphere(sphere, origin = this.origin, direction = this.direction) {
        const ray = tempVec3c;
        // 计算从射线起点到球心的向量
        ray.sub(sphere.center, origin);

        // 计算射线方向上的投影长度
        const tca = ray.dot(direction);

        // 计算射线到球心的最短距离的平方
        const d2 = ray.dot(ray) - tca * tca;

        // 球体半径的平方
        const radius2 = sphere.radius * sphere.radius;

        // 如果最短距离大于半径，则没有交点
        if (d2 > radius2) return 0;

        // 计算从最短距离点到交点的距离
        const thc = Math.sqrt(radius2 - d2);

        // 计算两个交点的距离
        const t0 = tca - thc; // 第一个交点（近的）
        const t1 = tca + thc; // 第二个交点（远的）

        // 如果两个交点都在射线起点的反方向，则没有交点
        if (t0 < 0 && t1 < 0) return 0;

        // 如果第一个交点在射线起点的反方向，返回第二个交点
        if (t0 < 0) return t1;

        // 否则返回第一个交点（较近的）
        return t0;
    }

    /**
     * 检测射线与轴对齐包围盒(AABB)的交点
     * @param {Object} box - 包围盒对象，包含min和max属性
     * @param {Vec3} [origin=this.origin] - 射线起点
     * @param {Vec3} [direction=this.direction] - 射线方向
     * @returns {Number} 交点距离，如果没有交点则返回0
     */
    intersectBox(box, origin = this.origin, direction = this.direction) {
        // 射线与AABB的交点算法使用"slab"方法
        let tmin, tmax, tYmin, tYmax, tZmin, tZmax;

        // 计算射线方向的倒数，用于优化计算
        const invdirx = 1 / direction.x;
        const invdiry = 1 / direction.y;
        const invdirz = 1 / direction.z;

        const min = box.min;
        const max = box.max;

        // 计算与x平面的交点参数
        // 根据射线方向选择正确的边界
        tmin = ((invdirx >= 0 ? min.x : max.x) - origin.x) * invdirx;
        tmax = ((invdirx >= 0 ? max.x : min.x) - origin.x) * invdirx;

        // 计算与y平面的交点参数
        tYmin = ((invdiry >= 0 ? min.y : max.y) - origin.y) * invdiry;
        tYmax = ((invdiry >= 0 ? max.y : min.y) - origin.y) * invdiry;

        // 如果x和y的交点区间不重叠，则没有交点
        if (tmin > tYmax || tYmin > tmax) return 0;

        // 更新交点区间
        if (tYmin > tmin) tmin = tYmin;
        if (tYmax < tmax) tmax = tYmax;

        // 计算与z平面的交点参数
        tZmin = ((invdirz >= 0 ? min.z : max.z) - origin.z) * invdirz;
        tZmax = ((invdirz >= 0 ? max.z : min.z) - origin.z) * invdirz;

        // 如果当前区间与z的交点区间不重叠，则没有交点
        if (tmin > tZmax || tZmin > tmax) return 0;

        // 更新交点区间
        if (tZmin > tmin) tmin = tZmin;
        if (tZmax < tmax) tmax = tZmax;

        // 如果最远交点在射线起点的反方向，则没有交点
        if (tmax < 0) return 0;

        // 返回最近的交点距离
        return tmin >= 0 ? tmin : tmax;
    }

    /**
     * 检测射线与三角形的交点
     * 使用Möller–Trumbore算法
     * @param {Vec3} a - 三角形第一个顶点
     * @param {Vec3} b - 三角形第二个顶点
     * @param {Vec3} c - 三角形第三个顶点
     * @param {Boolean} [backfaceCulling=true] - 是否启用背面剔除
     * @param {Vec3} [origin=this.origin] - 射线起点
     * @param {Vec3} [direction=this.direction] - 射线方向
     * @param {Vec3} [normal=tempVec3g] - 用于存储计算的法线的临时向量
     * @returns {Number} 交点距离，如果没有交点则返回0
     */
    intersectTriangle(a, b, c, backfaceCulling = true, origin = this.origin, direction = this.direction, normal = tempVec3g) {
        // 算法来源：
        // from https://github.com/mrdoob/three.js/blob/master/src/math/Ray.js
        // which is from http://www.geometrictools.com/GTEngine/Include/Mathematics/GteIntrRay3Triangle3.h

        // 计算三角形的两条边
        const edge1 = tempVec3h;
        const edge2 = tempVec3i;
        const diff = tempVec3j;

        edge1.sub(b, a); // 边1：从a到b的向量
        edge2.sub(c, a); // 边2：从a到c的向量

        // 计算三角形法线
        normal.cross(edge1, edge2);

        // 计算射线方向与法线的点积
        let DdN = direction.dot(normal);

        // 如果射线平行于三角形平面，则没有交点
        if (!DdN) return 0;

        let sign;
        // 确定射线是从正面还是背面穿过三角形
        if (DdN > 0) {
            // 射线从背面穿过三角形
            if (backfaceCulling) return 0; // 如果启用背面剔除，则忽略
            sign = 1;
        } else {
            // 射线从正面穿过三角形
            sign = -1;
            DdN = -DdN; // 使DdN为正值
        }

        // 计算从起点到三角形第一个顶点的向量
        diff.sub(origin, a);

        // 计算重心坐标的第一个分量
        let DdQxE2 = sign * direction.dot(edge2.cross(diff, edge2));

        // 如果结果为负，则交点在三角形外部
        if (DdQxE2 < 0) return 0;

        // 计算重心坐标的第二个分量
        let DdE1xQ = sign * direction.dot(edge1.cross(diff));

        // 如果结果为负，则交点在三角形外部
        if (DdE1xQ < 0) return 0;

        // 检查重心坐标的总和是否超过1
        if (DdQxE2 + DdE1xQ > DdN) return 0;

        // 计算参数t，即射线起点到交点的距离
        let QdN = -sign * diff.dot(normal);

        // 如果t为负，则交点在射线起点的反方向
        if (QdN < 0) return 0;

        // 返回交点距离
        return QdN / DdN;
    }

    getBarycoord(point, a, b, c, target = tempVec3h) {
        // From https://github.com/mrdoob/three.js/blob/master/src/math/Triangle.js
        // static/instance method to calculate barycentric coordinates
        // based on: http://www.blackpawn.com/texts/pointinpoly/default.html
        const v0 = tempVec3i;
        const v1 = tempVec3j;
        const v2 = tempVec3k;
        v0.sub(c, a);
        v1.sub(b, a);
        v2.sub(point, a);
        const dot00 = v0.dot(v0);
        const dot01 = v0.dot(v1);
        const dot02 = v0.dot(v2);
        const dot11 = v1.dot(v1);
        const dot12 = v1.dot(v2);
        const denom = dot00 * dot11 - dot01 * dot01;
        if (denom === 0) return target.set(-2, -1, -1);
        const invDenom = 1 / denom;
        const u = (dot11 * dot02 - dot01 * dot12) * invDenom;
        const v = (dot00 * dot12 - dot01 * dot02) * invDenom;
        return target.set(1 - u - v, v, u);
    }
}
