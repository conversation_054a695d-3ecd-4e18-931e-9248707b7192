<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>WebGL 实例化渲染学习示例 - vertexAttribDivisor</title>
        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: #f5f5f5;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                text-align: center;
            }

            .header h1 {
                margin: 0;
                font-size: 24px;
            }

            .demo-section {
                display: flex;
                min-height: 600px;
            }

            .canvas-container {
                flex: 1;
                padding: 20px;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            canvas {
                border: 2px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .controls {
                width: 350px;
                padding: 20px;
                background: #f8f9fa;
                border-left: 1px solid #e9ecef;
                overflow-y: auto;
            }

            .control-group {
                margin-bottom: 20px;
                padding: 15px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .control-group h3 {
                margin: 0 0 15px 0;
                color: #495057;
                font-size: 16px;
                border-bottom: 2px solid #e9ecef;
                padding-bottom: 8px;
            }

            button {
                width: 100%;
                padding: 10px;
                margin: 5px 0;
                border: none;
                border-radius: 5px;
                background: #007bff;
                color: white;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.2s;
            }

            button:hover {
                background: #0056b3;
            }

            button.active {
                background: #28a745;
            }

            .info-panel {
                background: #e3f2fd;
                border: 1px solid #bbdefb;
                border-radius: 5px;
                padding: 10px;
                margin: 10px 0;
                font-size: 12px;
                line-height: 1.4;
            }

            .stats {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                color: #666;
                margin-top: 10px;
            }

            .highlight {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 8px;
                border-radius: 4px;
                margin: 5px 0;
                font-size: 12px;
            }

            input[type='range'] {
                width: 100%;
                margin: 10px 0;
            }

            .range-label {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #666;
            }

            .code-example {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                margin: 5px 0;
                overflow-x: auto;
            }

            .performance-indicator {
                display: flex;
                align-items: center;
                margin: 5px 0;
            }

            .performance-bar {
                flex: 1;
                height: 8px;
                background: #e9ecef;
                border-radius: 4px;
                margin: 0 10px;
                overflow: hidden;
            }

            .performance-fill {
                height: 100%;
                background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
                transition: width 0.3s ease;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 WebGL 实例化渲染学习示例</h1>
                <p>深入理解 gl.vertexAttribDivisor() 和实例化渲染技术</p>
            </div>

            <div class="demo-section">
                <div class="canvas-container">
                    <canvas id="webglCanvas" width="600" height="500"></canvas>
                    <div class="stats">
                        <div>实例数量: <span id="instanceCount">100</span></div>
                        <div>绘制调用: <span id="drawCalls">1</span></div>
                        <div>FPS: <span id="fps">60</span></div>
                        <div>性能提升: <span id="performance">100x</span></div>
                    </div>
                </div>

                <div class="controls">
                    <div class="control-group">
                        <h3>🎮 渲染模式</h3>
                        <button id="staticInstances" class="active">静态实例</button>
                        <button id="animatedInstances">动画实例</button>
                        <button id="particleSystem">粒子系统</button>
                        <button id="comparisonMode">性能对比</button>
                    </div>

                    <div class="control-group">
                        <h3>⚙️ 实例参数</h3>
                        <div class="range-label">
                            <span>实例数量</span>
                            <span id="instanceCountValue">100</span>
                        </div>
                        <input type="range" id="instanceCountSlider" min="10" max="1000" value="100" />

                        <div class="range-label">
                            <span>动画速度</span>
                            <span id="animSpeedValue">1.0</span>
                        </div>
                        <input type="range" id="animSpeedSlider" min="0" max="5" step="0.1" value="1.0" />

                        <button id="toggleAnimation">⏸️ 暂停动画</button>
                        <button id="resetInstances">🔄 重置实例</button>
                    </div>

                    <div class="control-group">
                        <h3>📊 性能监控</h3>
                        <div class="performance-indicator">
                            <span>CPU:</span>
                            <div class="performance-bar">
                                <div class="performance-fill" id="cpuUsage" style="width: 30%"></div>
                            </div>
                            <span id="cpuPercent">30%</span>
                        </div>
                        <div class="performance-indicator">
                            <span>GPU:</span>
                            <div class="performance-bar">
                                <div class="performance-fill" id="gpuUsage" style="width: 45%"></div>
                            </div>
                            <span id="gpuPercent">45%</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>🔍 技术详解</h3>
                        <div class="info-panel">
                            <div><strong>当前 divisor 设置:</strong></div>
                            <div class="code-example" id="divisorInfo">
                                gl.vertexAttribDivisor(positionLoc, 0); // 每顶点<br />
                                gl.vertexAttribDivisor(instancePosLoc, 1); // 每实例<br />
                                gl.vertexAttribDivisor(instanceColorLoc, 1); // 每实例
                            </div>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>📚 学习要点</h3>
                        <div class="highlight" id="learningPoint">
                            <strong>实例化渲染核心:</strong><br />
                            • divisor=0: 每个顶点使用不同值<br />
                            • divisor=1: 每个实例使用不同值<br />
                            • drawArraysInstanced() 一次绘制多个实例<br />
                            • 大幅减少绘制调用，提升性能
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>💡 应用场景</h3>
                        <div class="highlight">
                            • 粒子系统 (烟花、雨滴)<br />
                            • 草地渲染 (大量草叶)<br />
                            • 建筑群 (重复建筑)<br />
                            • UI 元素 (按钮、图标)
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // WebGL 上下文和程序变量
            let gl;
            let program;
            let currentMode = 'staticInstances';
            let animationId;
            let isAnimating = true;
            let animationSpeed = 1.0;
            let startTime = Date.now();

            // 缓冲区和属性
            let baseVertexBuffer;
            let instanceBuffer;
            let attributeLocations = {};

            // 实例化数据
            let instanceCount = 100;
            let instanceData;
            let particles = [];

            // 性能监控
            let frameCount = 0;
            let lastFpsUpdate = Date.now();
            let currentFps = 60;

            // 初始化
            document.addEventListener('DOMContentLoaded', init);

            function init() {
                const canvas = document.getElementById('webglCanvas');
                gl = canvas.getContext('webgl2');

                if (!gl) {
                    alert('WebGL2 不支持！请使用现代浏览器');
                    return;
                }

                // 设置WebGL状态
                gl.viewport(0, 0, canvas.width, canvas.height);
                gl.clearColor(0.05, 0.05, 0.1, 1.0);
                gl.enable(gl.BLEND);
                gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

                // 创建着色器程序
                createShaderProgram();

                // 初始化缓冲区
                initBuffers();

                // 设置事件监听器
                setupEventListeners();

                // 初始化实例数据
                generateInstances();

                // 开始渲染循环
                animate();
            }

            function createShaderProgram() {
                const vertexShaderSource = `#version 300 es
                in vec3 a_position;          // 基础几何体顶点位置 (divisor=0)
                in vec3 a_instancePosition;  // 实例位置偏移 (divisor=1)
                in vec3 a_instanceColor;     // 实例颜色 (divisor=1)
                in float a_instanceScale;    // 实例缩放 (divisor=1)
                in float a_instanceRotation; // 实例旋转 (divisor=1)
                
                out vec3 v_color;
                
                void main() {
                    // 应用旋转矩阵
                    float c = cos(a_instanceRotation);
                    float s = sin(a_instanceRotation);
                    mat2 rotation = mat2(c, -s, s, c);
                    
                    // 缩放 -> 旋转 -> 平移
                    vec2 scaledPos = a_position.xy * a_instanceScale;
                    vec2 rotatedPos = rotation * scaledPos;
                    vec3 worldPos = vec3(rotatedPos, a_position.z) + a_instancePosition;
                    
                    gl_Position = vec4(worldPos, 1.0);
                    v_color = a_instanceColor;
                }`;

                const fragmentShaderSource = `#version 300 es
                precision mediump float;
                in vec3 v_color;
                out vec4 fragColor;
                
                void main() {
                    // 添加一些渐变效果
                    float alpha = 0.8;
                    fragColor = vec4(v_color, alpha);
                }`;

                program = createProgram(vertexShaderSource, fragmentShaderSource);
                gl.useProgram(program);

                // 获取属性位置
                attributeLocations = {
                    position: gl.getAttribLocation(program, 'a_position'),
                    instancePosition: gl.getAttribLocation(program, 'a_instancePosition'),
                    instanceColor: gl.getAttribLocation(program, 'a_instanceColor'),
                    instanceScale: gl.getAttribLocation(program, 'a_instanceScale'),
                    instanceRotation: gl.getAttribLocation(program, 'a_instanceRotation'),
                };
            }

            function createProgram(vertexSource, fragmentSource) {
                const vertexShader = createShader(gl.VERTEX_SHADER, vertexSource);
                const fragmentShader = createShader(gl.FRAGMENT_SHADER, fragmentSource);

                const program = gl.createProgram();
                gl.attachShader(program, vertexShader);
                gl.attachShader(program, fragmentShader);
                gl.linkProgram(program);

                if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                    console.error('程序链接失败:', gl.getProgramInfoLog(program));
                    return null;
                }

                return program;
            }

            function createShader(type, source) {
                const shader = gl.createShader(type);
                gl.shaderSource(shader, source);
                gl.compileShader(shader);

                if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                    console.error('着色器编译失败:', gl.getShaderInfoLog(shader));
                    gl.deleteShader(shader);
                    return null;
                }

                return shader;
            }

            function initBuffers() {
                // 创建基础三角形几何体 (所有实例共享)
                const baseVertices = new Float32Array([
                    0.0,
                    0.03,
                    0.0, // 顶点1
                    -0.025,
                    -0.03,
                    0.0, // 顶点2
                    0.025,
                    -0.03,
                    0.0, // 顶点3
                ]);

                // 创建基础几何体缓冲区
                baseVertexBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, baseVertexBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, baseVertices, gl.STATIC_DRAW);

                // 创建实例数据缓冲区 (动态更新)
                instanceBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
                // 预分配足够大的缓冲区 (位置3 + 颜色3 + 缩放1 + 旋转1 = 8个float)
                gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(1000 * 8), gl.DYNAMIC_DRAW);
            }

            function generateInstances() {
                instanceData = new Float32Array(instanceCount * 8);
                particles = [];

                for (let i = 0; i < instanceCount; i++) {
                    const offset = i * 8;

                    // 生成随机位置 (-0.9 到 0.9)
                    const x = (Math.random() - 0.5) * 1.8;
                    const y = (Math.random() - 0.5) * 1.8;
                    const z = 0.0;

                    // 生成随机颜色
                    const hue = Math.random() * 360;
                    const color = hslToRgb(hue, 0.8, 0.6);

                    // 生成随机缩放和旋转
                    const scale = 0.5 + Math.random() * 1.5;
                    const rotation = Math.random() * Math.PI * 2;

                    // 填充实例数据
                    instanceData[offset + 0] = x;
                    instanceData[offset + 1] = y;
                    instanceData[offset + 2] = z;
                    instanceData[offset + 3] = color[0];
                    instanceData[offset + 4] = color[1];
                    instanceData[offset + 5] = color[2];
                    instanceData[offset + 6] = scale;
                    instanceData[offset + 7] = rotation;

                    // 为动画模式存储粒子数据
                    particles.push({
                        position: [x, y, z],
                        velocity: [(Math.random() - 0.5) * 0.02, (Math.random() - 0.5) * 0.02, 0],
                        color: color,
                        scale: scale,
                        rotation: rotation,
                        rotationSpeed: (Math.random() - 0.5) * 0.1,
                        life: 1.0,
                        maxLife: 1.0 + Math.random() * 2.0,
                    });
                }

                updateInstanceBuffer();
            }

            function updateInstanceBuffer() {
                gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
                gl.bufferSubData(gl.ARRAY_BUFFER, 0, instanceData.subarray(0, instanceCount * 8));
            }

            function setupVertexAttributes() {
                // 配置基础几何体属性 (每个顶点不同)
                gl.bindBuffer(gl.ARRAY_BUFFER, baseVertexBuffer);
                gl.enableVertexAttribArray(attributeLocations.position);
                gl.vertexAttribPointer(attributeLocations.position, 3, gl.FLOAT, false, 0, 0);
                gl.vertexAttribDivisor(attributeLocations.position, 0); // 每个顶点使用不同值

                // 配置实例属性 (每个实例不同)
                gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
                const stride = 8 * 4; // 8个float * 4字节

                // 实例位置
                gl.enableVertexAttribArray(attributeLocations.instancePosition);
                gl.vertexAttribPointer(attributeLocations.instancePosition, 3, gl.FLOAT, false, stride, 0);
                gl.vertexAttribDivisor(attributeLocations.instancePosition, 1); // 每个实例使用不同值

                // 实例颜色
                gl.enableVertexAttribArray(attributeLocations.instanceColor);
                gl.vertexAttribPointer(attributeLocations.instanceColor, 3, gl.FLOAT, false, stride, 12);
                gl.vertexAttribDivisor(attributeLocations.instanceColor, 1);

                // 实例缩放
                gl.enableVertexAttribArray(attributeLocations.instanceScale);
                gl.vertexAttribPointer(attributeLocations.instanceScale, 1, gl.FLOAT, false, stride, 24);
                gl.vertexAttribDivisor(attributeLocations.instanceScale, 1);

                // 实例旋转
                gl.enableVertexAttribArray(attributeLocations.instanceRotation);
                gl.vertexAttribPointer(attributeLocations.instanceRotation, 1, gl.FLOAT, false, stride, 28);
                gl.vertexAttribDivisor(attributeLocations.instanceRotation, 1);
            }

            function render() {
                if (currentMode === 'comparisonMode') {
                    renderTraditional();
                } else {
                    renderInstanced();
                }
            }

            function renderInstanced() {
                // 清空画布
                gl.clear(gl.COLOR_BUFFER_BIT);

                // 使用着色器程序
                gl.useProgram(program);

                // 设置顶点属性
                setupVertexAttributes();

                // 实例化绘制 - 关键API调用！
                gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, instanceCount);

                // 更新统计信息
                updateStats();
            }

            function updateAnimation() {
                if (!isAnimating) return;

                const currentTime = (Date.now() - startTime) * 0.001 * animationSpeed;

                if (currentMode === 'animatedInstances') {
                    // 旋转和缩放动画
                    for (let i = 0; i < instanceCount; i++) {
                        const offset = i * 8;
                        const particle = particles[i];

                        // 旋转动画
                        instanceData[offset + 7] = particle.rotation + currentTime * particle.rotationSpeed;

                        // 脉冲缩放动画
                        const pulseScale = 1.0 + 0.3 * Math.sin(currentTime * 2 + i * 0.1);
                        instanceData[offset + 6] = particle.scale * pulseScale;
                    }
                    // 确保更新GPU缓冲区
                    updateInstanceBuffer();
                } else if (currentMode === 'particleSystem') {
                    // 粒子系统动画
                    for (let i = 0; i < instanceCount; i++) {
                        const particle = particles[i];
                        const offset = i * 8;

                        // 更新位置
                        particle.position[0] += particle.velocity[0];
                        particle.position[1] += particle.velocity[1];

                        // 边界反弹
                        if (Math.abs(particle.position[0]) > 0.9) {
                            particle.velocity[0] *= -0.8;
                            particle.position[0] = Math.sign(particle.position[0]) * 0.9;
                        }
                        if (Math.abs(particle.position[1]) > 0.9) {
                            particle.velocity[1] *= -0.8;
                            particle.position[1] = Math.sign(particle.position[1]) * 0.9;
                        }

                        // 重力效果
                        particle.velocity[1] -= 0.0005;

                        // 更新旋转
                        particle.rotation += particle.rotationSpeed;

                        // 更新实例数据
                        instanceData[offset + 0] = particle.position[0];
                        instanceData[offset + 1] = particle.position[1];
                        instanceData[offset + 2] = particle.position[2];
                        instanceData[offset + 7] = particle.rotation;
                    }
                    // 确保更新GPU缓冲区
                    updateInstanceBuffer();
                }
            }

            function animate() {
                updateAnimation();
                render();

                // FPS计算
                frameCount++;
                const now = Date.now();
                if (now - lastFpsUpdate > 1000) {
                    currentFps = Math.round((frameCount * 1000) / (now - lastFpsUpdate));
                    frameCount = 0;
                    lastFpsUpdate = now;
                }

                animationId = requestAnimationFrame(animate);
            }

            // 工具函数
            function hslToRgb(h, s, l) {
                h /= 360;
                const a = s * Math.min(l, 1 - l);
                const f = (n) => {
                    const k = (n + h * 12) % 12;
                    return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
                };
                return [f(0), f(8), f(4)];
            }

            function setupEventListeners() {
                // 渲染模式切换
                document.getElementById('staticInstances').addEventListener('click', () => setMode('staticInstances'));
                document.getElementById('animatedInstances').addEventListener('click', () => setMode('animatedInstances'));
                document.getElementById('particleSystem').addEventListener('click', () => setMode('particleSystem'));
                document.getElementById('comparisonMode').addEventListener('click', () => setMode('comparisonMode'));

                // 实例数量滑块
                const instanceSlider = document.getElementById('instanceCountSlider');
                instanceSlider.addEventListener('input', (e) => {
                    instanceCount = parseInt(e.target.value);
                    document.getElementById('instanceCountValue').textContent = instanceCount;
                    generateInstances();
                });

                // 动画速度滑块
                const animSlider = document.getElementById('animSpeedSlider');
                animSlider.addEventListener('input', (e) => {
                    animationSpeed = parseFloat(e.target.value);
                    document.getElementById('animSpeedValue').textContent = animationSpeed.toFixed(1);
                });

                // 动画控制按钮
                document.getElementById('toggleAnimation').addEventListener('click', () => {
                    isAnimating = !isAnimating;
                    const btn = document.getElementById('toggleAnimation');
                    btn.textContent = isAnimating ? '⏸️ 暂停动画' : '▶️ 播放动画';
                });

                // 重置按钮
                document.getElementById('resetInstances').addEventListener('click', () => {
                    generateInstances();
                    startTime = Date.now();
                });
            }

            function setMode(mode) {
                currentMode = mode;

                // 更新按钮状态
                document.querySelectorAll('.control-group button').forEach((btn) => btn.classList.remove('active'));
                document.getElementById(mode).classList.add('active');

                // 更新学习要点
                updateLearningPoint(mode);

                // 更新divisor信息
                updateDivisorInfo(mode);

                // 重新生成实例（某些模式需要）
                if (mode === 'particleSystem') {
                    generateInstances();
                }
            }

            function updateLearningPoint(mode) {
                const learningPoint = document.getElementById('learningPoint');
                const points = {
                    staticInstances: `
                        <strong>静态实例化:</strong><br>
                        • 一次性生成所有实例数据<br>
                        • divisor=1 让每个实例使用不同属性<br>
                        • 适合静态场景如建筑群、树林
                    `,
                    animatedInstances: `
                        <strong>动画实例化:</strong><br>
                        • 每帧更新实例数据<br>
                        • 使用 bufferSubData 更新GPU缓冲区<br>
                        • 适合旋转、缩放等简单动画
                    `,
                    particleSystem: `
                        <strong>粒子系统:</strong><br>
                        • 复杂的物理模拟<br>
                        • 每帧计算位置、速度、碰撞<br>
                        • 适合烟花、雨滴、爆炸效果
                    `,
                    comparisonMode: `
                        <strong>性能对比:</strong><br>
                        • 实例化 vs 多次绘制调用<br>
                        • 观察FPS和绘制调用数量差异<br>
                        • 体验实例化的性能优势
                    `,
                };
                learningPoint.innerHTML = points[mode];
            }

            function updateDivisorInfo(mode) {
                const divisorInfo = document.getElementById('divisorInfo');
                divisorInfo.innerHTML = `
gl.vertexAttribDivisor(positionLoc, 0);      // 每顶点<br>
gl.vertexAttribDivisor(instancePosLoc, 1);   // 每实例<br>
gl.vertexAttribDivisor(instanceColorLoc, 1); // 每实例<br>
gl.vertexAttribDivisor(instanceScaleLoc, 1); // 每实例<br>
gl.vertexAttribDivisor(instanceRotLoc, 1);   // 每实例
                `;
            }

            function updateStats() {
                document.getElementById('instanceCount').textContent = instanceCount;
                document.getElementById('drawCalls').textContent = currentMode === 'comparisonMode' ? instanceCount : '1';
                document.getElementById('fps').textContent = currentFps;

                // 计算性能提升
                const performanceGain = currentMode === 'comparisonMode' ? '1x' : `${instanceCount}x`;
                document.getElementById('performance').textContent = performanceGain;

                // 更新性能条
                const cpuUsage = Math.min(90, (instanceCount / 1000) * 100);
                const gpuUsage = Math.min(80, (instanceCount / 800) * 100);

                document.getElementById('cpuUsage').style.width = cpuUsage + '%';
                document.getElementById('gpuUsage').style.width = gpuUsage + '%';
                document.getElementById('cpuPercent').textContent = Math.round(cpuUsage) + '%';
                document.getElementById('gpuPercent').textContent = Math.round(gpuUsage) + '%';
            }

            // 性能对比模式的传统渲染方法
            function renderTraditional() {
                gl.clear(gl.COLOR_BUFFER_BIT);
                gl.useProgram(program);

                // 设置基础几何体
                gl.bindBuffer(gl.ARRAY_BUFFER, baseVertexBuffer);
                gl.enableVertexAttribArray(attributeLocations.position);
                gl.vertexAttribPointer(attributeLocations.position, 3, gl.FLOAT, false, 0, 0);

                // 为每个实例单独绘制 - 性能较差的方法
                for (let i = 0; i < instanceCount; i++) {
                    const offset = i * 8;

                    // 通过uniform传递实例数据（模拟传统方法）
                    // 实际应用中会使用uniform，这里简化处理
                    gl.drawArrays(gl.TRIANGLES, 0, 3);
                }

                updateStats();
            }
        </script>
    </body>
</html>
