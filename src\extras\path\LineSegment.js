import BaseSegment from './BaseSegment.js';
import { Vec3 } from '../../math/Vec3.js';
import { lerp as lerp3 } from '../../math/functions/Vec3Func.js';

// 预先创建临时向量以避免重复创建对象
const tempVec3 = /* @__PURE__ */ new Vec3();

/**
 * 线段类
 * 表示路径中的直线段
 */
export default class LineSegment extends BaseSegment {
    /**
     * 创建一个线段
     * @param {Vec3} p0 - 起点
     * @param {Vec3} p1 - 终点
     * @param {number} [tiltStart=0] - 起点的倾斜角度
     * @param {number} [tiltEnd=0] - 终点的倾斜角度
     */
    constructor(p0, p1, tiltStart = 0, tiltEnd = 0) {
        super();
        this.p0 = p0; // 线段起点
        this.p1 = p1; // 线段终点

        this.tiltStart = tiltStart; // 起点的倾斜角度
        this.tiltEnd = tiltEnd; // 终点的倾斜角度

        this._len = -1; // 初始化长度为-1，表示尚未计算
    }

    /**
     * 更新线段长度
     * 每次更改线段的控制点后，必须调用此方法
     */
    updateLength() {
        // 计算起点到终点的距离作为线段长度
        this._len = tempVec3.sub(this.p1, this.p0).len();
    }

    /**
     * 获取线段上相对位置的点
     * @param {number} t - 参数值，范围[0..1]
     * @param {Vec3} [out=new Vec3()] - 可选的输出向量
     * @returns {Vec3} 线段上的点
     */
    getPointAt(t, out = new Vec3()) {
        // 使用线性插值计算t处的点
        lerp3(out, this.p0, this.p1, t);
        return out;
    }

    /**
     * 获取参数t处的单位切向量
     * @param {number} t - 参数值，范围[0..1]
     * @param {Vec3} [out=new Vec3()] - 可选的输出向量
     * @returns {Vec3} 单位切向量
     */
    getTangentAt(t, out = new Vec3()) {
        // 线段的切向量是从起点指向终点的单位向量
        // 对于线段，切向量在任何t值处都相同
        return out.sub(this.p1, this.p0).normalize();
    }

    /**
     * 获取线段的最后一个点（终点）
     * @returns {Vec3} 线段的终点
     */
    lastPoint() {
        return this.p1;
    }
}
