# OGL示例学习顺序指南

## 学习策略
示例是理解OGL最好的方式。建议按照从简单到复杂的顺序学习，每个示例都建立在前面的知识基础上。

## 第一阶段：基础渲染 (必学)

### 1. triangle-screen-shader.html
**难度：** ⭐
**学习目标：** 最基础的WebGL渲染
**核心概念：**
- 全屏三角形技巧
- 基础着色器
- 渲染循环

**关键代码片段：**
```javascript
// 覆盖整个屏幕的三角形
const geometry = new Geometry(gl, {
    position: { size: 2, data: new Float32Array([-1, -1, 3, -1, -1, 3]) },
    uv: { size: 2, data: new Float32Array([0, 0, 2, 0, 0, 2]) },
});
```

### 2. base-primitives.html
**难度：** ⭐⭐
**学习目标：** 基础几何体渲染
**核心概念：**
- 3D几何体
- 相机设置
- 基础变换

### 3. textures.html
**难度：** ⭐⭐
**学习目标：** 纹理映射
**核心概念：**
- 纹理加载
- UV坐标
- 纹理参数

### 4. orbit-controls.html
**难度：** ⭐⭐
**学习目标：** 交互控制
**核心概念：**
- 鼠标交互
- 相机控制
- 轨道控制器

## 第二阶段：场景管理 (重要)

### 5. scene-graph.html
**难度：** ⭐⭐⭐
**学习目标：** 场景图管理
**核心概念：**
- 父子关系
- 变换继承
- 场景遍历

### 6. frustum-culling.html
**难度：** ⭐⭐⭐
**学习目标：** 性能优化
**核心概念：**
- 视锥体裁剪
- 包围盒计算
- 渲染优化

### 7. high-mesh-count.html
**难度：** ⭐⭐⭐
**学习目标：** 大量对象渲染
**核心概念：**
- 批量渲染
- 性能监控
- 内存管理

## 第三阶段：光照和材质 (进阶)

### 8. point-lighting.html
**难度：** ⭐⭐⭐
**学习目标：** 基础光照
**核心概念：**
- 点光源
- 漫反射和镜面反射
- 法线计算

### 9. normal-maps.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 法线贴图
**核心概念：**
- 切线空间
- 法线贴图原理
- 表面细节增强

### 10. flat-shading-matcap.html
**难度：** ⭐⭐⭐
**学习目标：** 材质捕获
**核心概念：**
- MatCap技术
- 球面映射
- 快速材质渲染

### 11. pbr.html
**难度：** ⭐⭐⭐⭐⭐
**学习目标：** 基于物理的渲染
**核心概念：**
- PBR材质模型
- 金属度和粗糙度
- 环境光照

## 第四阶段：高级渲染技术 (专业)

### 12. shadow-maps.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 阴影映射
**核心概念：**
- 深度纹理
- 阴影映射算法
- 阴影偏移

### 13. render-to-texture.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 离屏渲染
**核心概念：**
- 帧缓冲对象
- 渲染目标
- 多通道渲染

### 14. post-bloom.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 后期处理
**核心概念：**
- 高斯模糊
- 亮度提取
- 混合模式

### 15. post-fxaa.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 抗锯齿
**核心概念：**
- FXAA算法
- 边缘检测
- 图像质量优化

## 第五阶段：动画和交互 (创意)

### 16. skinning.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 骨骼动画
**核心概念：**
- 骨骼绑定
- 顶点权重
- 动画插值

### 17. particles.html
**难度：** ⭐⭐⭐
**学习目标：** 粒子系统
**核心概念：**
- 粒子生命周期
- 随机数生成
- 批量渲染

### 18. gpgpu-particles.html
**难度：** ⭐⭐⭐⭐⭐
**学习目标：** GPU计算
**核心概念：**
- 计算着色器模拟
- 纹理存储数据
- GPU并行计算

### 19. raycasting.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 射线投射
**核心概念：**
- 鼠标拾取
- 射线-三角形相交
- 3D交互

## 第六阶段：特殊效果 (艺术)

### 20. mouse-flowmap.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 流体效果
**核心概念：**
- 流动图
- 鼠标轨迹
- 动态纹理

### 21. curves.html
**难度：** ⭐⭐⭐
**学习目标：** 曲线渲染
**核心概念：**
- 贝塞尔曲线
- 曲线细分
- 路径动画

### 22. polylines.html
**难度：** ⭐⭐⭐
**学习目标：** 线条渲染
**核心概念：**
- 线条宽度
- 连接处理
- 批量线条

### 23. wireframe.html & wireframe-shader.html
**难度：** ⭐⭐⭐
**学习目标：** 线框渲染
**核心概念：**
- 边缘检测
- 几何着色器模拟
- 调试可视化

## 第七阶段：模型加载和格式 (实用)

### 24. load-json.html
**难度：** ⭐⭐
**学习目标：** 自定义格式加载
**核心概念：**
- JSON数据解析
- 异步加载
- 数据转换

### 25. load-gltf.html
**难度：** ⭐⭐⭐
**学习目标：** 标准格式支持
**核心概念：**
- GLTF格式
- 材质系统
- 场景层次

### 26. gltf-draco-webp.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 压缩格式
**核心概念：**
- Draco几何压缩
- WebP纹理
- 加载优化

### 27. compressed-textures.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 纹理压缩
**核心概念：**
- 硬件压缩格式
- 平台兼容性
- 内存优化

## 第八阶段：性能和优化 (专业)

### 28. instancing.html
**难度：** ⭐⭐⭐⭐
**学习目标：** 实例化渲染
**核心概念：**
- 实例化绘制
- 属性分流
- 大量对象优化

### 29. instancing-gpu-picking.html
**难度：** ⭐⭐⭐⭐⭐
**学习目标：** GPU拾取
**核心概念：**
- GPU拾取算法
- 实例ID传递
- 高性能交互

### 30. indexed-vs-non-indexed.html
**难度：** ⭐⭐
**学习目标：** 渲染优化对比
**核心概念：**
- 索引缓冲区
- 顶点重用
- 性能分析

## 学习建议

### 1. 循序渐进
- 严格按照顺序学习
- 确保理解当前示例再进入下一个
- 遇到困难可以暂时跳过，稍后回来

### 2. 动手实践
- 修改示例参数观察效果
- 尝试组合不同技术
- 创建自己的变体

### 3. 深入理解
- 阅读着色器代码
- 理解数学原理
- 查阅相关资料

### 4. 性能意识
- 使用浏览器性能工具
- 监控帧率和内存使用
- 学习优化技巧

## 每个阶段的检查点

**阶段1完成：** 能够创建基础的3D场景
**阶段2完成：** 能够管理复杂的场景结构
**阶段3完成：** 理解光照和材质系统
**阶段4完成：** 掌握高级渲染技术
**阶段5完成：** 能够创建动态交互效果
**阶段6完成：** 能够实现创意视觉效果
**阶段7完成：** 能够处理各种3D资源
**阶段8完成：** 具备性能优化能力

## 推荐的实践项目

### 初级项目 (完成阶段1-2)
- 3D产品展示器
- 简单的3D游戏场景
- 交互式数据可视化

### 中级项目 (完成阶段3-5)
- 角色动画系统
- 建筑可视化
- 粒子特效编辑器

### 高级项目 (完成阶段6-8)
- 实时渲染引擎
- WebGL游戏引擎
- 专业可视化工具

通过系统地学习这些示例，您将全面掌握OGL库的使用，并具备创建复杂3D应用的能力。
