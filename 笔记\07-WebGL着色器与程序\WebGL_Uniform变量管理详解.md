# WebGL Uniform 变量管理详解

═══════════════════════════════════════════════════════════════════════════════

## 概述

Uniform 变量是 WebGL 中用于向着色器传递全局数据的重要机制。它们在整个绘制调用中保持常量值，是连接 JavaScript 应用程序和 GPU 着色器的关键桥梁。

## 🔧 核心工作流程

### 1. 完整的 Uniform 工作流程

```
JavaScript 应用程序
        ↓
    位置查询阶段 (getUniformLocation)
        ↓
    数据传输阶段 (uniform* 函数)
        ↓
    GPU 绑定表更新
        ↓
    着色器程序使用
        ↓
    渲染管线执行
```

### 2. 详细工作步骤

#### 步骤 1: 位置查询阶段

```javascript
// 1. 程序必须已经链接成功
const program = createAndLinkProgram(vertexShader, fragmentShader);

// 2. 查询 uniform 变量在 GPU 中的位置
const modelMatrixLocation = gl.getUniformLocation(program, 'u_modelMatrix');
const colorLocation = gl.getUniformLocation(program, 'u_color');
const textureLocation = gl.getUniformLocation(program, 'u_texture');
```

**内部机制：**

-   WebGL 驱动程序扫描着色器程序的符号表
-   为每个活动的 uniform 变量分配一个唯一的位置标识符
-   位置标识符映射到 GPU 内存中的特定地址

#### 步骤 2: 数据传输阶段

```javascript
// 3. 激活着色器程序
gl.useProgram(program);

// 4. 向 GPU 传输数据
gl.uniformMatrix4fv(modelMatrixLocation, false, modelMatrix);
gl.uniform3f(colorLocation, 1.0, 0.0, 0.0);
gl.uniform1i(textureLocation, 0);
```

**内部机制：**

-   数据通过 WebGL API 传输到 GPU 驱动程序
-   驱动程序将数据写入 GPU 的常量缓冲区
-   GPU 更新内部的绑定表，建立位置到内存地址的映射

#### 步骤 3: GPU 绑定表更新

```
GPU 内部操作：
┌─────────────────┬──────────────────┬─────────────────┐
│ Uniform 位置    │ 内存地址         │ 数据类型        │
├─────────────────┼──────────────────┼─────────────────┤
│ 0 (modelMatrix) │ 0x1000-0x103F    │ mat4 (64 bytes) │
│ 1 (color)       │ 0x1040-0x104B    │ vec3 (12 bytes) │
│ 2 (texture)     │ 0x104C-0x104F    │ int (4 bytes)   │
└─────────────────┴──────────────────┴─────────────────┘
```

#### 步骤 4: 着色器程序使用

```glsl
// 顶点着色器中
uniform mat4 u_modelMatrix;
uniform vec3 u_color;

void main() {
    gl_Position = u_modelMatrix * a_position;
    v_color = u_color;
}
```

**内部机制：**

-   着色器执行时，GPU 根据 uniform 变量名查找绑定表
-   从对应内存地址读取数据
-   数据在所有顶点/片段处理中保持不变

## 🔗 与其他模块的协作关系

### 1. 与着色器程序模块的协作

```javascript
// 着色器程序生命周期中的 Uniform 管理
class ShaderProgram {
    constructor(vertexSource, fragmentSource) {
        this.program = this.createProgram(vertexSource, fragmentSource);
        this.uniformLocations = new Map();
        this.uniformCache = new Map();
        this.attributeLocations = new Map();
        this.isValid = false;

        if (this.program) {
            this.isValid = true;
            this.cacheAllLocations();
        }
    }

    createProgram(vertexSource, fragmentSource) {
        // 创建和编译着色器
        const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource);
        const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource);

        if (!vertexShader || !fragmentShader) {
            return null;
        }

        // 创建程序
        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);

        // 检查链接状态
        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            console.error('Program link failed:', gl.getProgramInfoLog(program));
            gl.deleteProgram(program);
            return null;
        }

        // 清理着色器（程序已链接，不再需要）
        gl.deleteShader(vertexShader);
        gl.deleteShader(fragmentShader);

        return program;
    }

    createShader(type, source) {
        const shader = gl.createShader(type);
        gl.shaderSource(shader, source);
        gl.compileShader(shader);

        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            const typeName = type === gl.VERTEX_SHADER ? 'vertex' : 'fragment';
            console.error(`${typeName} shader compile failed:`, gl.getShaderInfoLog(shader));
            gl.deleteShader(shader);
            return null;
        }

        return shader;
    }

    // 自动缓存所有 uniform 和 attribute 位置
    cacheAllLocations() {
        // 缓存所有uniform位置
        const uniformCount = gl.getProgramParameter(this.program, gl.ACTIVE_UNIFORMS);
        for (let i = 0; i < uniformCount; i++) {
            const info = gl.getActiveUniform(this.program, i);
            const location = gl.getUniformLocation(this.program, info.name);
            if (location !== null) {
                this.uniformLocations.set(info.name, {
                    location,
                    type: info.type,
                    size: info.size,
                });
            }
        }

        // 缓存所有attribute位置
        const attributeCount = gl.getProgramParameter(this.program, gl.ACTIVE_ATTRIBUTES);
        for (let i = 0; i < attributeCount; i++) {
            const info = gl.getActiveAttrib(this.program, i);
            const location = gl.getAttribLocation(this.program, info.name);
            if (location !== -1) {
                this.attributeLocations.set(info.name, {
                    location,
                    type: info.type,
                    size: info.size,
                });
            }
        }
    }

    // 批量获取 uniform 位置
    cacheUniformLocations(uniformNames) {
        uniformNames.forEach((name) => {
            const location = gl.getUniformLocation(this.program, name);
            if (location !== null) {
                // 获取uniform信息
                const uniformCount = gl.getProgramParameter(this.program, gl.ACTIVE_UNIFORMS);
                for (let i = 0; i < uniformCount; i++) {
                    const info = gl.getActiveUniform(this.program, i);
                    if (info.name === name) {
                        this.uniformLocations.set(name, {
                            location,
                            type: info.type,
                            size: info.size,
                        });
                        break;
                    }
                }
            }
        });
    }

    // 设置 uniform 值（带缓存优化）
    setUniform(name, value) {
        const uniformInfo = this.uniformLocations.get(name);
        if (!uniformInfo) {
            console.warn(`Uniform '${name}' not found in program`);
            return false;
        }

        // 检查缓存
        if (this.isSameValue(name, value)) {
            return true; // 值未改变，跳过更新
        }

        // 更新uniform
        if (this.updateUniform(uniformInfo.location, value, uniformInfo.type)) {
            this.uniformCache.set(name, this.cloneValue(value));
            return true;
        }

        return false;
    }

    updateUniform(location, value, type) {
        try {
            switch (type) {
                case gl.FLOAT:
                    gl.uniform1f(location, value);
                    break;
                case gl.FLOAT_VEC2:
                    gl.uniform2fv(location, value);
                    break;
                case gl.FLOAT_VEC3:
                    gl.uniform3fv(location, value);
                    break;
                case gl.FLOAT_VEC4:
                    gl.uniform4fv(location, value);
                    break;
                case gl.INT:
                case gl.BOOL:
                case gl.SAMPLER_2D:
                case gl.SAMPLER_CUBE:
                    gl.uniform1i(location, value);
                    break;
                case gl.FLOAT_MAT2:
                    gl.uniformMatrix2fv(location, false, value);
                    break;
                case gl.FLOAT_MAT3:
                    gl.uniformMatrix3fv(location, false, value);
                    break;
                case gl.FLOAT_MAT4:
                    gl.uniformMatrix4fv(location, false, value);
                    break;
                default:
                    console.warn(`Unsupported uniform type: ${type}`);
                    return false;
            }
            return true;
        } catch (error) {
            console.error(`Failed to set uniform:`, error);
            return false;
        }
    }

    isSameValue(name, value) {
        const cached = this.uniformCache.get(name);
        if (cached === undefined) return false;

        const uniformInfo = this.uniformLocations.get(name);
        if (!uniformInfo) return false;

        return this.compareValues(cached, value, uniformInfo.type);
    }

    compareValues(a, b, type) {
        switch (type) {
            case gl.FLOAT:
                return Math.abs(a - b) < 1e-6;
            case gl.INT:
            case gl.BOOL:
            case gl.SAMPLER_2D:
            case gl.SAMPLER_CUBE:
                return a === b;
            case gl.FLOAT_VEC2:
                return this.compareArrays(a, b, 2);
            case gl.FLOAT_VEC3:
                return this.compareArrays(a, b, 3);
            case gl.FLOAT_VEC4:
                return this.compareArrays(a, b, 4);
            case gl.FLOAT_MAT2:
                return this.compareArrays(a, b, 4);
            case gl.FLOAT_MAT3:
                return this.compareArrays(a, b, 9);
            case gl.FLOAT_MAT4:
                return this.compareArrays(a, b, 16);
            default:
                return a === b;
        }
    }

    compareArrays(a, b, length) {
        if (!a || !b || a.length !== length || b.length !== length) {
            return false;
        }

        for (let i = 0; i < length; i++) {
            if (Math.abs(a[i] - b[i]) > 1e-6) {
                return false;
            }
        }
        return true;
    }

    cloneValue(value) {
        if (Array.isArray(value) || value instanceof Float32Array) {
            return new Float32Array(value);
        }
        if (Array.isArray(value) || value instanceof Int32Array) {
            return new Int32Array(value);
        }
        return value;
    }

    // 使用程序
    use() {
        if (this.isValid) {
            gl.useProgram(this.program);
            return true;
        }
        return false;
    }

    // 获取attribute位置
    getAttributeLocation(name) {
        const info = this.attributeLocations.get(name);
        return info ? info.location : -1;
    }

    // 获取uniform位置
    getUniformLocation(name) {
        const info = this.uniformLocations.get(name);
        return info ? info.location : null;
    }

    // 清理资源
    dispose() {
        if (this.program) {
            gl.deleteProgram(this.program);
            this.program = null;
        }
        this.uniformLocations.clear();
        this.uniformCache.clear();
        this.attributeLocations.clear();
        this.isValid = false;
    }

    // 获取程序信息
    getInfo() {
        return {
            isValid: this.isValid,
            uniformCount: this.uniformLocations.size,
            attributeCount: this.attributeLocations.size,
            uniforms: Array.from(this.uniformLocations.keys()),
            attributes: Array.from(this.attributeLocations.keys()),
        };
    }
}

// 使用示例
const vertexShaderSource = `
    attribute vec4 a_position;
    attribute vec2 a_texCoord;

    uniform mat4 u_modelMatrix;
    uniform mat4 u_viewMatrix;
    uniform mat4 u_projectionMatrix;

    varying vec2 v_texCoord;

    void main() {
        gl_Position = u_projectionMatrix * u_viewMatrix * u_modelMatrix * a_position;
        v_texCoord = a_texCoord;
    }
`;

const fragmentShaderSource = `
    precision mediump float;

    uniform sampler2D u_texture;
    uniform vec3 u_color;
    uniform float u_alpha;

    varying vec2 v_texCoord;

    void main() {
        vec4 texColor = texture2D(u_texture, v_texCoord);
        gl_FragColor = vec4(texColor.rgb * u_color, texColor.a * u_alpha);
    }
`;

const shaderProgram = new ShaderProgram(vertexShaderSource, fragmentShaderSource);

if (shaderProgram.isValid) {
    // 使用程序
    shaderProgram.use();

    // 设置uniform值
    shaderProgram.setUniform('u_modelMatrix', modelMatrix);
    shaderProgram.setUniform('u_viewMatrix', viewMatrix);
    shaderProgram.setUniform('u_projectionMatrix', projectionMatrix);
    shaderProgram.setUniform('u_color', [1.0, 0.0, 0.0]);
    shaderProgram.setUniform('u_alpha', 1.0);
    shaderProgram.setUniform('u_texture', 0);

    console.log('Program Info:', shaderProgram.getInfo());
}
```

### 2. 与纹理系统的协作

```javascript
// Uniform 与纹理单元的绑定
class TextureManager {
    constructor(maxTextureUnits = 16) {
        // 如果 GPU 只支持 8 个纹理单元，而我们想用 16 个，那么只能用 8 个
        // 如果 GPU 支持 32 个纹理单元，而我们只想用 16 个，那么用 16 个就够了
        this.maxTextureUnits = Math.min(maxTextureUnits, gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS));
        this.boundTextures = new Array(this.maxTextureUnits).fill(null);
        this.activeUnit = 0;
    }

    // 绑定单个纹理到指定单元
    bindTexture(texture, unit, target = gl.TEXTURE_2D) {
        if (unit >= this.maxTextureUnits) {
            console.error(`Texture unit ${unit} exceeds maximum ${this.maxTextureUnits}`);
            return false;
        }

        // 激活纹理单元
        if (this.activeUnit !== unit) {
            gl.activeTexture(gl.TEXTURE0 + unit);
            this.activeUnit = unit;
        }

        // 绑定纹理
        gl.bindTexture(target, texture);
        this.boundTextures[unit] = { texture, target };

        return true;
    }

    // 批量绑定纹理并设置对应的uniform
    bindTextures(textures, program) {
        let successCount = 0;

        textures.forEach((textureInfo, index) => {
            if (index >= this.maxTextureUnits) {
                console.warn(`Skipping texture ${index}, exceeds maximum units`);
                return;
            }

            const { texture, uniformName, target = gl.TEXTURE_2D } = textureInfo;

            // 绑定纹理到单元
            if (this.bindTexture(texture, index, target)) {
                // 设置uniform指向纹理单元
                const location = gl.getUniformLocation(program, uniformName);
                if (location !== null) {
                    gl.uniform1i(location, index);
                    successCount++;
                } else {
                    console.warn(`Uniform '${uniformName}' not found in program`);
                }
            }
        });

        return successCount;
    }

    // 高级纹理绑定，支持纹理缓存和优化
    bindTexturesOptimized(textures, uniformManager) {
        let bindCount = 0;
        let uniformCount = 0;

        textures.forEach((textureInfo, index) => {
            if (index >= this.maxTextureUnits) return;

            const { texture, uniformName, target = gl.TEXTURE_2D } = textureInfo;

            // 检查是否需要重新绑定纹理
            const currentBinding = this.boundTextures[index];
            if (!currentBinding || currentBinding.texture !== texture || currentBinding.target !== target) {
                if (this.bindTexture(texture, index, target)) {
                    bindCount++;
                }
            }

            // 使用优化的uniform管理器设置uniform
            if (uniformManager.setUniform(uniformName, index)) {
                uniformCount++;
            }
        });

        return { bindCount, uniformCount };
    }

    // 创建纹理对象
    createTexture(image, options = {}) {
        const texture = gl.createTexture();
        if (!texture) {
            console.error('Failed to create texture');
            return null;
        }

        const { target = gl.TEXTURE_2D, minFilter = gl.LINEAR, magFilter = gl.LINEAR, wrapS = gl.CLAMP_TO_EDGE, wrapT = gl.CLAMP_TO_EDGE, generateMipmap = false } = options;

        // 临时绑定纹理进行设置
        const originalUnit = this.activeUnit;
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(target, texture);

        // 设置纹理数据
        if (image) {
            gl.texImage2D(target, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);
        }

        // 设置纹理参数
        gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, minFilter);
        gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, magFilter);
        gl.texParameteri(target, gl.TEXTURE_WRAP_S, wrapS);
        gl.texParameteri(target, gl.TEXTURE_WRAP_T, wrapT);

        // 生成mipmap
        if (generateMipmap && this.isPowerOfTwo(image.width) && this.isPowerOfTwo(image.height)) {
            gl.generateMipmap(target);
        }

        // 恢复原始纹理单元
        gl.activeTexture(gl.TEXTURE0 + originalUnit);

        return texture;
    }

    // 检查是否为2的幂次方
    isPowerOfTwo(value) {
        return (value & (value - 1)) === 0;
    }

    // 解绑所有纹理
    unbindAll() {
        for (let i = 0; i < this.maxTextureUnits; i++) {
            if (this.boundTextures[i]) {
                gl.activeTexture(gl.TEXTURE0 + i);
                gl.bindTexture(this.boundTextures[i].target, null);
                this.boundTextures[i] = null;
            }
        }
        this.activeUnit = 0;
    }

    // 获取纹理绑定状态
    getBindingInfo() {
        return {
            maxUnits: this.maxTextureUnits,
            activeUnit: this.activeUnit,
            boundTextures: this.boundTextures.map((binding, index) => ({
                unit: index,
                bound: binding !== null,
                target: binding ? binding.target : null,
            })),
        };
    }
}

// 使用示例
const textureManager = new TextureManager();

// 创建纹理
const diffuseTexture = textureManager.createTexture(diffuseImage, {
    minFilter: gl.LINEAR_MIPMAP_LINEAR,
    magFilter: gl.LINEAR,
    generateMipmap: true,
});

const normalTexture = textureManager.createTexture(normalImage);

// 批量绑定纹理
const textures = [
    { texture: diffuseTexture, uniformName: 'u_diffuseTexture' },
    { texture: normalTexture, uniformName: 'u_normalTexture' },
    { texture: specularTexture, uniformName: 'u_specularTexture' },
];

const boundCount = textureManager.bindTextures(textures, program);
console.log(`Successfully bound ${boundCount} textures`);

// 使用优化的绑定方式
const stats = textureManager.bindTexturesOptimized(textures, uniformManager);
console.log(`Bind operations: ${stats.bindCount}, Uniform updates: ${stats.uniformCount}`);
```

### 3. 与变换矩阵系统的协作

```javascript
// 矩阵变换与 Uniform 的集成
class Transform {
    constructor(uniformManager) {
        this.uniformManager = uniformManager;

        // 变换属性
        this.position = [0, 0, 0];
        this.rotation = [0, 0, 0]; // 欧拉角 (x, y, z)
        this.scale = [1, 1, 1];

        // 矩阵缓存
        this.modelMatrix = new Float32Array(16);
        this.normalMatrix = new Float32Array(9);
        this.mvpMatrix = new Float32Array(16);

        // 脏标记
        this.isDirty = true;
        this.isNormalMatrixDirty = true;

        // 初始化为单位矩阵
        this.resetMatrices();
    }

    resetMatrices() {
        // 单位矩阵
        this.modelMatrix.set([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);

        this.normalMatrix.set([1, 0, 0, 0, 1, 0, 0, 0, 1]);
    }

    // 设置位置
    setPosition(x, y, z) {
        this.position[0] = x;
        this.position[1] = y;
        this.position[2] = z;
        this.markDirty();
    }

    // 设置旋转（欧拉角，弧度）
    setRotation(x, y, z) {
        this.rotation[0] = x;
        this.rotation[1] = y;
        this.rotation[2] = z;
        this.markDirty();
    }

    // 设置缩放
    setScale(x, y, z) {
        this.scale[0] = x;
        this.scale[1] = y;
        this.scale[2] = z;
        this.markDirty();
    }

    // 标记为需要更新
    markDirty() {
        this.isDirty = true;
        this.isNormalMatrixDirty = true;
    }

    // 计算模型矩阵
    calculateModelMatrix() {
        if (!this.isDirty) return this.modelMatrix;

        // 重置为单位矩阵
        this.resetMatrices();

        // 应用缩放
        this.applyScale();

        // 应用旋转
        this.applyRotation();

        // 应用平移
        this.applyTranslation();

        this.isDirty = false;
        return this.modelMatrix;
    }

    applyScale() {
        this.modelMatrix[0] *= this.scale[0];
        this.modelMatrix[5] *= this.scale[1];
        this.modelMatrix[10] *= this.scale[2];
    }

    applyRotation() {
        // 简化的旋转实现（实际项目中建议使用成熟的数学库）
        const [rx, ry, rz] = this.rotation;

        // X轴旋转
        if (rx !== 0) {
            const cos = Math.cos(rx);
            const sin = Math.sin(rx);
            this.multiplyMatrix([1, 0, 0, 0, 0, cos, -sin, 0, 0, sin, cos, 0, 0, 0, 0, 1]);
        }

        // Y轴旋转
        if (ry !== 0) {
            const cos = Math.cos(ry);
            const sin = Math.sin(ry);
            this.multiplyMatrix([cos, 0, sin, 0, 0, 1, 0, 0, -sin, 0, cos, 0, 0, 0, 0, 1]);
        }

        // Z轴旋转
        if (rz !== 0) {
            const cos = Math.cos(rz);
            const sin = Math.sin(rz);
            this.multiplyMatrix([cos, -sin, 0, 0, sin, cos, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);
        }
    }

    applyTranslation() {
        this.modelMatrix[12] += this.position[0];
        this.modelMatrix[13] += this.position[1];
        this.modelMatrix[14] += this.position[2];
    }

    // 矩阵乘法（简化实现）
    multiplyMatrix(matrix) {
        const result = new Float32Array(16);
        const a = this.modelMatrix;
        const b = matrix;

        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                result[i * 4 + j] = a[i * 4 + 0] * b[0 * 4 + j] + a[i * 4 + 1] * b[1 * 4 + j] + a[i * 4 + 2] * b[2 * 4 + j] + a[i * 4 + 3] * b[3 * 4 + j];
            }
        }

        this.modelMatrix.set(result);
    }

    // 计算法线矩阵
    calculateNormalMatrix() {
        if (!this.isNormalMatrixDirty) return this.normalMatrix;

        // 法线矩阵是模型矩阵的逆转置矩阵的左上3x3部分
        // 这里简化为只取左上3x3部分（假设只有均匀缩放）
        const m = this.calculateModelMatrix();

        this.normalMatrix[0] = m[0];
        this.normalMatrix[1] = m[1];
        this.normalMatrix[2] = m[2];
        this.normalMatrix[3] = m[4];
        this.normalMatrix[4] = m[5];
        this.normalMatrix[5] = m[6];
        this.normalMatrix[6] = m[8];
        this.normalMatrix[7] = m[9];
        this.normalMatrix[8] = m[10];

        this.isNormalMatrixDirty = false;
        return this.normalMatrix;
    }

    // 更新所有矩阵并传输到GPU
    updateMatrices(camera) {
        // 计算模型矩阵
        const modelMatrix = this.calculateModelMatrix();

        // 获取视图和投影矩阵
        const viewMatrix = camera.getViewMatrix();
        const projectionMatrix = camera.getProjectionMatrix();

        // 计算复合矩阵
        this.calculateMVPMatrix(modelMatrix, viewMatrix, projectionMatrix);

        // 计算法线矩阵
        const normalMatrix = this.calculateNormalMatrix();

        // 使用uniform管理器传输矩阵
        this.uniformManager.setUniform('u_modelMatrix', modelMatrix);
        this.uniformManager.setUniform('u_viewMatrix', viewMatrix);
        this.uniformManager.setUniform('u_projectionMatrix', projectionMatrix);
        this.uniformManager.setUniform('u_mvpMatrix', this.mvpMatrix);
        this.uniformManager.setUniform('u_normalMatrix', normalMatrix);

        // 传输变换相关的向量
        this.uniformManager.setUniform('u_modelPosition', this.position);
        this.uniformManager.setUniform('u_modelScale', this.scale);
    }

    // 计算MVP矩阵
    calculateMVPMatrix(modelMatrix, viewMatrix, projectionMatrix) {
        // MVP = Projection * View * Model
        // 这里简化实现，实际项目中建议使用数学库

        // 先计算 MV = View * Model
        const mv = new Float32Array(16);
        this.multiplyMatrices(mv, viewMatrix, modelMatrix);

        // 再计算 MVP = Projection * MV
        this.multiplyMatrices(this.mvpMatrix, projectionMatrix, mv);
    }

    // 通用矩阵乘法
    multiplyMatrices(result, a, b) {
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                result[i * 4 + j] = a[i * 4 + 0] * b[0 * 4 + j] + a[i * 4 + 1] * b[1 * 4 + j] + a[i * 4 + 2] * b[2 * 4 + j] + a[i * 4 + 3] * b[3 * 4 + j];
            }
        }
    }

    // 获取变换信息
    getTransformInfo() {
        return {
            position: [...this.position],
            rotation: [...this.rotation],
            scale: [...this.scale],
            isDirty: this.isDirty,
            isNormalMatrixDirty: this.isNormalMatrixDirty,
        };
    }

    // 重置变换
    reset() {
        this.setPosition(0, 0, 0);
        this.setRotation(0, 0, 0);
        this.setScale(1, 1, 1);
    }
}

// 使用示例
const transform = new Transform(uniformManager);

// 设置变换
transform.setPosition(5, 0, -10);
transform.setRotation(0, Math.PI / 4, 0); // Y轴旋转45度
transform.setScale(2, 2, 2);

// 在渲染循环中更新矩阵
function render() {
    // 更新变换矩阵并传输到GPU
    transform.updateMatrices(camera);

    // 绘制对象
    gl.drawElements(gl.TRIANGLES, indexCount, gl.UNSIGNED_SHORT, 0);
}

// 动画示例
function animate(time) {
    // 旋转动画
    transform.setRotation(0, time * 0.001, 0);

    // 上下浮动
    transform.setPosition(0, Math.sin(time * 0.002) * 2, -10);

    render();
    requestAnimationFrame(animate);
}
```

### 4. 与渲染管线的协作

**⚠️ 重要概念澄清：**

在 WebGL 中，**没有真正的"全局"uniform 变量**。每个着色器程序都有自己独立的 uniform 变量空间。所谓的"全局 uniform"实际上是指：

1. **共享的数据值** - 多个着色器程序使用相同名称和相同值的 uniform
2. **分别设置** - 需要为每个程序单独设置这些 uniform 值
3. **缓存优化** - 通过缓存避免重复设置相同的值

**正确的工作流程：**

```
准备全局数据 → 切换程序 → 应用全局uniform → 设置材质uniform → 渲染
```

**错误的理解：**

```
设置全局uniform → 切换程序 → 设置材质uniform → 渲染  ❌
```

```javascript
// 修正后的渲染循环中的 Uniform 管理
class Renderer {
    constructor() {
        this.uniformManager = new OptimizedUniformManager();
        this.textureManager = new TextureManager();
        this.currentProgram = null;
        this.globalUniforms = new Map();
        this.frameCount = 0;
        this.renderStats = {
            drawCalls: 0,
            uniformUpdates: 0,
            textureBinds: 0,
            programSwitches: 0,
        };
    }

    // 主渲染函数
    render(scene, camera) {
        this.frameCount++;
        this.resetStats();

        // 1. 准备全局 uniform 数据（缓存，不直接传输到GPU）
        const globalUniformData = this.prepareGlobalUniformData(scene, camera);

        // 2. 按材质分组渲染（减少状态切换）
        const groupedObjects = this.groupObjectsByMaterial(scene.objects);

        Object.entries(groupedObjects).forEach(([materialId, objects]) => {
            const material = objects[0].material;

            // 3. 切换着色器程序
            this.useProgram(material.program);

            // 4. 应用全局 uniform 到当前程序
            this.applyGlobalUniforms(material.program, globalUniformData);

            // 5. 设置材质 uniform
            this.setMaterialUniforms(material);

            // 6. 绑定材质纹理
            this.bindMaterialTextures(material);

            // 7. 渲染所有使用该材质的对象
            objects.forEach((object) => {
                this.renderObject(object, camera);
            });
        });

        // 8. 输出渲染统计
        this.logRenderStats();
    }

    // 准备全局uniform数据（仅缓存，不传输到GPU）
    prepareGlobalUniformData(scene, camera) {
        const globalData = new Map();

        // 相机相关uniform
        globalData.set('u_viewMatrix', camera.getViewMatrix());
        globalData.set('u_projectionMatrix', camera.getProjectionMatrix());
        globalData.set('u_cameraPosition', camera.position);

        // 时间相关uniform
        globalData.set('u_time', performance.now() * 0.001);
        globalData.set('u_frameCount', this.frameCount);

        // 光照相关uniform
        if (scene.lights && scene.lights.length > 0) {
            const lightData = this.prepareLightData(scene.lights);
            globalData.set('u_lightData', lightData.lightData);
            globalData.set('u_lightCount', lightData.lightCount);
        }

        // 环境相关uniform
        globalData.set('u_ambientColor', scene.ambientColor || [0.1, 0.1, 0.1]);
        globalData.set('u_fogColor', scene.fogColor || [0.5, 0.5, 0.5]);
        globalData.set('u_fogDensity', scene.fogDensity || 0.0);

        return globalData;
    }

    // 将全局uniform应用到当前激活的着色器程序
    applyGlobalUniforms(program, globalUniformData) {
        // 确保程序已激活
        if (this.currentProgram !== program) {
            console.warn('Program not active when applying global uniforms');
            return;
        }

        // 为当前程序应用所有全局uniform
        globalUniformData.forEach((value, name) => {
            // 使用缓存优化，避免重复设置相同值
            this.setUniformWithCache(program, name, value);
        });
    }

    // 带缓存的uniform设置
    setUniformWithCache(program, name, value) {
        const programId = this.getProgramId(program);
        const cacheKey = `${programId}_${name}`;

        // 检查缓存，避免重复设置
        if (!this.globalUniforms.has(cacheKey) || !this.compareUniformValues(this.globalUniforms.get(cacheKey), value)) {
            // 实际设置uniform
            if (this.setUniform(name, value)) {
                this.globalUniforms.set(cacheKey, this.cloneUniformValue(value));
            }
        }
    }

    // 准备光照数据（仅处理数据，不传输到GPU）
    prepareLightData(lights) {
        const maxLights = 8;
        const lightData = new Float32Array(maxLights * 8);

        for (let i = 0; i < Math.min(lights.length, maxLights); i++) {
            const light = lights[i];
            const offset = i * 8;

            // 位置
            lightData[offset + 0] = light.position[0];
            lightData[offset + 1] = light.position[1];
            lightData[offset + 2] = light.position[2];

            // 颜色
            lightData[offset + 3] = light.color[0];
            lightData[offset + 4] = light.color[1];
            lightData[offset + 5] = light.color[2];

            // 强度和范围
            lightData[offset + 6] = light.intensity || 1.0;
            lightData[offset + 7] = light.range || 100.0;
        }

        return {
            lightData: lightData,
            lightCount: Math.min(lights.length, maxLights),
        };
    }

    // 获取程序ID（用于缓存键）
    getProgramId(program) {
        if (!program._id) {
            program._id = Math.random().toString(36).substr(2, 9);
        }
        return program._id;
    }

    // 比较uniform值是否相同
    compareUniformValues(a, b) {
        if (a === b) return true;

        // 处理数组/类型化数组
        if ((Array.isArray(a) || a instanceof Float32Array) && (Array.isArray(b) || b instanceof Float32Array)) {
            if (a.length !== b.length) return false;

            for (let i = 0; i < a.length; i++) {
                if (Math.abs(a[i] - b[i]) > 1e-6) return false;
            }
            return true;
        }

        // 处理数值
        if (typeof a === 'number' && typeof b === 'number') {
            return Math.abs(a - b) < 1e-6;
        }

        return false;
    }

    // 克隆uniform值
    cloneUniformValue(value) {
        if (Array.isArray(value) || value instanceof Float32Array) {
            return new Float32Array(value);
        }
        if (Array.isArray(value) || value instanceof Int32Array) {
            return new Int32Array(value);
        }
        return value;
    }

    // 按材质分组对象
    groupObjectsByMaterial(objects) {
        const groups = {};
        objects.forEach((object) => {
            const materialId = object.material.id || 'default';
            if (!groups[materialId]) {
                groups[materialId] = [];
            }
            groups[materialId].push(object);
        });
        return groups;
    }

    // 使用着色器程序
    useProgram(program) {
        if (this.currentProgram !== program) {
            gl.useProgram(program);
            this.currentProgram = program;
            this.uniformManager.program = program;
            this.renderStats.programSwitches++;
        }
    }

    // 设置材质uniform
    setMaterialUniforms(material) {
        // 基础材质属性
        this.setUniform('u_diffuseColor', material.diffuseColor || [1, 1, 1]);
        this.setUniform('u_specularColor', material.specularColor || [1, 1, 1]);
        this.setUniform('u_shininess', material.shininess || 32.0);
        this.setUniform('u_opacity', material.opacity || 1.0);

        // 材质标志
        this.setUniform('u_hasNormalMap', material.normalMap ? 1 : 0);
        this.setUniform('u_hasSpecularMap', material.specularMap ? 1 : 0);
        this.setUniform('u_hasEmissiveMap', material.emissiveMap ? 1 : 0);

        // PBR材质属性
        if (material.type === 'pbr') {
            this.setUniform('u_metallic', material.metallic || 0.0);
            this.setUniform('u_roughness', material.roughness || 0.5);
            this.setUniform('u_emissive', material.emissive || [0, 0, 0]);
        }
    }

    // 绑定材质纹理
    bindMaterialTextures(material) {
        const textures = [];

        if (material.diffuseMap) {
            textures.push({ texture: material.diffuseMap, uniformName: 'u_diffuseMap' });
        }

        if (material.normalMap) {
            textures.push({ texture: material.normalMap, uniformName: 'u_normalMap' });
        }

        if (material.specularMap) {
            textures.push({ texture: material.specularMap, uniformName: 'u_specularMap' });
        }

        if (material.emissiveMap) {
            textures.push({ texture: material.emissiveMap, uniformName: 'u_emissiveMap' });
        }

        if (textures.length > 0) {
            const stats = this.textureManager.bindTexturesOptimized(textures, this.uniformManager);
            this.renderStats.textureBinds += stats.bindCount;
        }
    }

    // 渲染单个对象
    renderObject(object, camera) {
        // 设置对象变换矩阵
        if (object.transform) {
            object.transform.updateMatrices(camera);
        }

        // 设置对象特定的uniform
        this.setObjectUniforms(object);

        // 绑定顶点数据
        this.bindObjectGeometry(object);

        // 执行绘制调用
        if (object.indices) {
            gl.drawElements(object.primitive || gl.TRIANGLES, object.indexCount, gl.UNSIGNED_SHORT, 0);
        } else {
            gl.drawArrays(object.primitive || gl.TRIANGLES, 0, object.vertexCount);
        }

        this.renderStats.drawCalls++;
    }

    // 设置对象特定的uniform
    setObjectUniforms(object) {
        // 对象ID（用于拾取等）
        this.setUniform('u_objectId', object.id || 0);

        // 对象特定的颜色调制
        if (object.colorTint) {
            this.setUniform('u_colorTint', object.colorTint);
        }

        // 对象特定的透明度
        if (object.alpha !== undefined) {
            this.setUniform('u_objectAlpha', object.alpha);
        }

        // 对象特定的标志
        this.setUniform('u_isSelected', object.isSelected ? 1 : 0);
        this.setUniform('u_isHighlighted', object.isHighlighted ? 1 : 0);
    }

    // 绑定对象几何数据
    bindObjectGeometry(object) {
        if (object.vao) {
            // 使用VAO（推荐方式）
            gl.bindVertexArray(object.vao);
        } else {
            // 手动绑定顶点属性
            this.bindVertexAttributes(object);
        }
    }

    // 手动绑定顶点属性
    bindVertexAttributes(object) {
        const attributes = object.attributes || {};

        Object.entries(attributes).forEach(([name, attribute]) => {
            const location = gl.getAttribLocation(this.currentProgram, name);
            if (location !== -1) {
                gl.bindBuffer(gl.ARRAY_BUFFER, attribute.buffer);
                gl.enableVertexAttribArray(location);
                gl.vertexAttribPointer(location, attribute.size, attribute.type || gl.FLOAT, attribute.normalized || false, attribute.stride || 0, attribute.offset || 0);
            }
        });

        // 绑定索引缓冲
        if (object.indexBuffer) {
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, object.indexBuffer);
        }
    }

    // 设置uniform（使用优化管理器）
    setUniform(name, value) {
        if (this.uniformManager.setUniform(name, value)) {
            this.renderStats.uniformUpdates++;
        }
    }

    // 重置统计信息
    resetStats() {
        this.renderStats.drawCalls = 0;
        this.renderStats.uniformUpdates = 0;
        this.renderStats.textureBinds = 0;
        this.renderStats.programSwitches = 0;
    }

    // 输出渲染统计
    logRenderStats() {
        if (this.frameCount % 60 === 0) {
            // 每60帧输出一次
            console.log('Render Stats:', {
                frame: this.frameCount,
                drawCalls: this.renderStats.drawCalls,
                uniformUpdates: this.renderStats.uniformUpdates,
                textureBinds: this.renderStats.textureBinds,
                programSwitches: this.renderStats.programSwitches,
                uniformCacheHitRate: this.uniformManager.getStats().cacheHitRate,
            });
        }
    }

    // 清理资源
    dispose() {
        this.uniformManager.clearCache();
        this.textureManager.unbindAll();
        this.currentProgram = null;
    }
}

// 使用示例
const renderer = new Renderer();

// 创建场景
const scene = {
    objects: [
        {
            id: 1,
            material: material1,
            transform: transform1,
            vao: vao1,
            indexCount: 36,
        },
        {
            id: 2,
            material: material2,
            transform: transform2,
            vao: vao2,
            indexCount: 24,
        },
    ],
    lights: [
        {
            position: [10, 10, 10],
            color: [1, 1, 1],
            intensity: 1.0,
            range: 50.0,
        },
    ],
    ambientColor: [0.1, 0.1, 0.1],
};

// 渲染循环
function renderLoop() {
    // 清除缓冲区
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

    // 渲染场景
    renderer.render(scene, camera);

    requestAnimationFrame(renderLoop);
}

renderLoop();
```

## 🚀 性能优化策略

### 1. Uniform 缓存机制

```javascript
class UniformCache {
    constructor() {
        this.cache = new Map();
    }

    setUniform(location, value, type) {
        const key = location.toString();
        const cached = this.cache.get(key);

        if (!cached || !this.compareValues(cached.value, value, type)) {
            this.updateUniform(location, value, type);
            this.cache.set(key, { value: this.cloneValue(value), type });
        }
    }

    updateUniform(location, value, type) {
        switch (type) {
            case 'float':
                gl.uniform1f(location, value);
                break;
            case 'vec2':
                gl.uniform2fv(location, value);
                break;
            case 'vec3':
                gl.uniform3fv(location, value);
                break;
            case 'vec4':
                gl.uniform4fv(location, value);
                break;
            case 'int':
                gl.uniform1i(location, value);
                break;
            case 'matrix2':
                gl.uniformMatrix2fv(location, false, value);
                break;
            case 'matrix3':
                gl.uniformMatrix3fv(location, false, value);
                break;
            case 'matrix4':
                gl.uniformMatrix4fv(location, false, value);
                break;
            default:
                console.warn(`Unsupported uniform type: ${type}`);
        }
    }

    compareValues(a, b, type) {
        switch (type) {
            case 'matrix4':
                return this.compareArrays(a, b, 16);
            case 'matrix3':
                return this.compareArrays(a, b, 9);
            case 'matrix2':
                return this.compareArrays(a, b, 4);
            case 'vec4':
                return this.compareArrays(a, b, 4);
            case 'vec3':
                return this.compareArrays(a, b, 3);
            case 'vec2':
                return this.compareArrays(a, b, 2);
            case 'float':
                return Math.abs(a - b) < 1e-6;
            case 'int':
                return a === b;
            default:
                return a === b;
        }
    }

    compareArrays(a, b, length) {
        if (!a || !b || a.length !== length || b.length !== length) {
            return false;
        }

        for (let i = 0; i < length; i++) {
            if (Math.abs(a[i] - b[i]) > 1e-6) {
                return false;
            }
        }
        return true;
    }

    cloneValue(value) {
        if (Array.isArray(value) || value instanceof Float32Array) {
            return new Float32Array(value);
        }
        return value;
    }

    clear() {
        this.cache.clear();
    }

    getStats() {
        return {
            cacheSize: this.cache.size,
            entries: Array.from(this.cache.entries()).map(([key, value]) => ({
                location: key,
                type: value.type,
                value: value.value,
            })),
        };
    }
}

// 使用示例
const uniformCache = new UniformCache();

// 设置uniform（会自动缓存和比较）
uniformCache.setUniform(mvpLocation, mvpMatrix, 'matrix4');
uniformCache.setUniform(colorLocation, [1.0, 0.0, 0.0], 'vec3');
uniformCache.setUniform(timeLocation, currentTime, 'float');

// 再次设置相同值时会被跳过
uniformCache.setUniform(colorLocation, [1.0, 0.0, 0.0], 'vec3'); // 跳过更新

// 查看缓存统计
console.log(uniformCache.getStats());
```

### 2. 批量 Uniform 更新

```javascript
class BatchUniformUpdater {
    constructor() {
        this.pendingUpdates = [];
    }

    queueUpdate(location, value, type) {
        this.pendingUpdates.push({ location, value, type });
    }

    flush() {
        // 按类型分组，减少 GL 状态切换
        const grouped = this.groupByType(this.pendingUpdates);

        Object.entries(grouped).forEach(([type, updates]) => {
            this.processBatch(type, updates);
        });

        this.pendingUpdates.length = 0;
    }

    groupByType(updates) {
        const groups = {};
        updates.forEach((update) => {
            if (!groups[update.type]) {
                groups[update.type] = [];
            }
            groups[update.type].push(update);
        });
        return groups;
    }

    processBatch(type, updates) {
        switch (type) {
            case 'float':
                updates.forEach((update) => {
                    gl.uniform1f(update.location, update.value);
                });
                break;
            case 'vec3':
                updates.forEach((update) => {
                    gl.uniform3fv(update.location, update.value);
                });
                break;
            case 'matrix4':
                updates.forEach((update) => {
                    gl.uniformMatrix4fv(update.location, false, update.value);
                });
                break;
            case 'int':
                updates.forEach((update) => {
                    gl.uniform1i(update.location, update.value);
                });
                break;
            default:
                console.warn(`Unsupported uniform type: ${type}`);
        }
    }
}

// 使用示例
const batchUpdater = new BatchUniformUpdater();

// 队列多个更新
batchUpdater.queueUpdate(timeLocation, currentTime, 'float');
batchUpdater.queueUpdate(colorLocation, [1.0, 0.0, 0.0], 'vec3');
batchUpdater.queueUpdate(mvpLocation, mvpMatrix, 'matrix4');

// 一次性执行所有更新
batchUpdater.flush();
```

## 🔍 调试和监控

### 1. Uniform 状态监控

```javascript
class UniformDebugger {
    logUniformState(program) {
        const uniformCount = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
        console.log(`程序包含 ${uniformCount} 个活动 uniform:`);

        for (let i = 0; i < uniformCount; i++) {
            const info = gl.getActiveUniform(program, i);
            const location = gl.getUniformLocation(program, info.name);
            const value = gl.getUniform(program, location);

            console.log(`${info.name}: ${this.formatValue(value, info.type)}`);
        }
    }
}
```

### 2. 性能分析

```javascript
class UniformProfiler {
    constructor() {
        this.updateCount = 0;
        this.updateTime = 0;
    }

    profileUpdate(fn) {
        const start = performance.now();
        fn();
        const end = performance.now();

        this.updateCount++;
        this.updateTime += end - start;
    }

    getStats() {
        return {
            totalUpdates: this.updateCount,
            totalTime: this.updateTime,
            averageTime: this.updateTime / this.updateCount,
        };
    }
}
```

## 📝 最佳实践

1. **位置缓存**: 在程序初始化时获取所有 uniform 位置并缓存
2. **值比较**: 避免设置相同的 uniform 值
3. **批量更新**: 将相关的 uniform 更新组织在一起
4. **类型安全**: 使用 TypeScript 或运行时检查确保类型正确
5. **内存管理**: 及时清理不再使用的 uniform 缓存

这个工作流程展示了 Uniform 变量如何作为 WebGL 渲染管线中的关键组件，协调 JavaScript 应用程序、GPU 硬件和着色器程序之间的数据传输。

## 🔬 GPU 内部操作详解

### 1. bindTexture 操作的内部机制

当调用 `gl.bindTexture()` 时，GPU 内部发生以下操作：

```
CPU 端调用: gl.bindTexture(gl.TEXTURE_2D, texture)
     ↓
WebGL 驱动程序处理
     ↓
GPU 绑定表更新:
┌─────────────────┬──────────────────┬─────────────────┐
│ 纹理单元        │ 纹理对象 ID      │ 内存地址        │
├─────────────────┼──────────────────┼─────────────────┤
│ TEXTURE0        │ texture_id_123   │ 0x2000000       │
│ TEXTURE1        │ texture_id_456   │ 0x2100000       │
└─────────────────┴──────────────────┴─────────────────┘
     ↓
GPU 缓存更新 (纹理缓存、采样器状态)
     ↓
内存映射建立 (虚拟地址 → 物理地址)
```

**详细步骤：**

1. **驱动程序验证**: 检查纹理对象是否有效
2. **绑定表更新**: 更新当前活动纹理单元的绑定
3. **缓存管理**: 如果纹理不在 GPU 缓存中，标记为需要加载
4. **内存映射**: 建立纹理数据的内存映射关系

### 2. Uniform 变量的 GPU 内存布局

```
GPU 常量缓冲区布局:
┌─────────────────────────────────────────────────────┐
│ Uniform Block 0 (Global Uniforms)                  │
│ ┌─────────────────┬─────────────────┬─────────────┐ │
│ │ u_viewMatrix    │ u_projMatrix    │ u_lightPos  │ │
│ │ (64 bytes)      │ (64 bytes)      │ (12 bytes)  │ │
│ └─────────────────┴─────────────────┴─────────────┘ │
├─────────────────────────────────────────────────────┤
│ Uniform Block 1 (Material Uniforms)                │
│ ┌─────────────────┬─────────────────┬─────────────┐ │
│ │ u_diffuseColor  │ u_specularColor │ u_shininess │ │
│ │ (16 bytes)      │ (16 bytes)      │ (4 bytes)   │ │
│ └─────────────────┴─────────────────┴─────────────┘ │
└─────────────────────────────────────────────────────┘
```

### 3. 纹理采样的完整流程

```javascript
// JavaScript 端设置
gl.activeTexture(gl.TEXTURE0);
gl.bindTexture(gl.TEXTURE_2D, diffuseTexture);
gl.uniform1i(diffuseLocation, 0);

gl.activeTexture(gl.TEXTURE1);
gl.bindTexture(gl.TEXTURE_2D, normalTexture);
gl.uniform1i(normalLocation, 1);
```

**GPU 内部处理流程：**

```
着色器执行: texture2D(u_diffuse, v_texCoord)
     ↓
1. 查找 uniform 绑定表
   u_diffuse → 纹理单元 0
     ↓
2. 访问纹理单元 0 的绑定表
   TEXTURE0 → texture_id_123 → 内存地址 0x2000000
     ↓
3. 纹理坐标变换
   v_texCoord (0.5, 0.5) → 像素坐标 (256, 256)
     ↓
4. 内存访问和过滤
   - 读取周围 4 个像素
   - 应用双线性过滤
   - 返回插值结果
     ↓
5. 返回最终颜色值
```

## 🎯 实际应用场景

### 1. 多光源渲染系统

```javascript
class MultiLightRenderer {
    constructor() {
        this.maxLights = 8;
        this.lightData = new Float32Array(this.maxLights * 8); // 每个光源8个float
    }

    updateLights(lights) {
        // 将光源数据打包到数组中
        for (let i = 0; i < Math.min(lights.length, this.maxLights); i++) {
            const offset = i * 8;
            const light = lights[i];

            // 位置 (3 floats)
            this.lightData[offset + 0] = light.position.x;
            this.lightData[offset + 1] = light.position.y;
            this.lightData[offset + 2] = light.position.z;

            // 颜色 (3 floats)
            this.lightData[offset + 3] = light.color.r;
            this.lightData[offset + 4] = light.color.g;
            this.lightData[offset + 5] = light.color.b;

            // 强度和范围 (2 floats)
            this.lightData[offset + 6] = light.intensity;
            this.lightData[offset + 7] = light.range;
        }

        // 一次性传输所有光源数据
        gl.uniform1fv(this.lightDataLocation, this.lightData);
        gl.uniform1i(this.lightCountLocation, lights.length);
    }
}
```

**对应的着色器代码：**

```glsl
#define MAX_LIGHTS 8

uniform float u_lightData[MAX_LIGHTS * 8];
uniform int u_lightCount;

vec3 calculateLighting(vec3 worldPos, vec3 normal) {
    vec3 totalLight = vec3(0.0);

    for (int i = 0; i < MAX_LIGHTS; i++) {
        if (i >= u_lightCount) break;

        int offset = i * 8;
        vec3 lightPos = vec3(u_lightData[offset], u_lightData[offset + 1], u_lightData[offset + 2]);
        vec3 lightColor = vec3(u_lightData[offset + 3], u_lightData[offset + 4], u_lightData[offset + 5]);
        float intensity = u_lightData[offset + 6];
        float range = u_lightData[offset + 7];

        // 计算光照贡献
        vec3 lightDir = lightPos - worldPos;
        float distance = length(lightDir);

        if (distance < range) {
            lightDir = normalize(lightDir);
            float attenuation = 1.0 - (distance / range);
            float ndotl = max(dot(normal, lightDir), 0.0);
            totalLight += lightColor * intensity * attenuation * ndotl;
        }
    }

    return totalLight;
}
```

### 2. 动画系统集成

```javascript
class AnimationSystem {
    constructor() {
        this.animationData = {
            time: 0,
            deltaTime: 0,
            frameCount: 0,
        };
    }

    update(currentTime) {
        const deltaTime = currentTime - this.animationData.time;
        this.animationData.deltaTime = deltaTime;
        this.animationData.time = currentTime;
        this.animationData.frameCount++;

        // 传输动画相关的 uniform
        gl.uniform1f(this.timeLocation, currentTime * 0.001); // 转换为秒
        gl.uniform1f(this.deltaTimeLocation, deltaTime * 0.001);
        gl.uniform1i(this.frameCountLocation, this.animationData.frameCount);

        // 传输周期性动画参数
        gl.uniform1f(this.sinTimeLocation, Math.sin(currentTime * 0.001));
        gl.uniform1f(this.cosTimeLocation, Math.cos(currentTime * 0.001));
    }
}
```

### 3. 后处理效果管道

```javascript
class PostProcessPipeline {
    constructor() {
        this.effects = [];
        this.framebuffers = [];
    }

    addEffect(effect) {
        this.effects.push(effect);
    }

    render(inputTexture) {
        let currentTexture = inputTexture;

        this.effects.forEach((effect, index) => {
            // 绑定输出帧缓冲
            const outputFB = this.framebuffers[index % 2];
            gl.bindFramebuffer(gl.FRAMEBUFFER, outputFB);

            // 使用效果的着色器程序
            gl.useProgram(effect.program);

            // 设置输入纹理
            gl.activeTexture(gl.TEXTURE0);
            gl.bindTexture(gl.TEXTURE_2D, currentTexture);
            gl.uniform1i(effect.inputTextureLocation, 0);

            // 设置效果特定的 uniform
            this.setEffectUniforms(effect);

            // 渲染全屏四边形
            this.renderFullscreenQuad();

            // 更新当前纹理为输出结果
            currentTexture = outputFB.colorTexture;
        });

        return currentTexture;
    }

    setEffectUniforms(effect) {
        switch (effect.type) {
            case 'blur':
                gl.uniform2f(effect.texelSizeLocation, 1.0 / this.width, 1.0 / this.height);
                gl.uniform1f(effect.blurRadiusLocation, effect.radius);
                break;

            case 'bloom':
                gl.uniform1f(effect.thresholdLocation, effect.threshold);
                gl.uniform1f(effect.intensityLocation, effect.intensity);
                break;

            case 'tonemap':
                gl.uniform1f(effect.exposureLocation, effect.exposure);
                gl.uniform1f(effect.gammaLocation, effect.gamma);
                break;
        }
    }
}
```

这些详细的说明展示了 Uniform 变量管理系统如何与 WebGL 的各个组件深度集成，以及在实际应用中如何高效地管理复杂的渲染状态。

## ⚠️ 常见问题和故障排除

### 1. Uniform 位置查询失败

**问题现象：**

```javascript
const location = gl.getUniformLocation(program, 'u_modelMatrix');
console.log(location); // 输出: null
```

**可能原因和解决方案：**

```javascript
// 原因1: 着色器程序未正确链接
if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
    console.error('程序链接失败:', gl.getProgramInfoLog(program));
    // 解决: 检查着色器编译和链接过程
}

// 原因2: uniform变量名拼写错误
const location = gl.getUniformLocation(program, 'u_modelMatrix'); // 正确
const location = gl.getUniformLocation(program, 'u_modelmatrix'); // 错误 - 大小写不匹配

// 原因3: uniform变量被优化器移除（未使用）
// 着色器中声明但未使用的uniform会被移除
// 解决: 确保在着色器中实际使用该uniform

// 原因4: 程序未激活
gl.useProgram(program); // 必须先激活程序
const location = gl.getUniformLocation(program, 'u_modelMatrix');
```

### 2. 纹理绑定问题

**问题现象：**

```javascript
// 纹理显示为黑色或白色
gl.uniform1i(textureLocation, 0);
// 但纹理没有正确显示
```

**解决方案：**

```javascript
// 完整的纹理绑定流程
function bindTextureCorrectly(texture, unit, location) {
    // 1. 激活纹理单元
    gl.activeTexture(gl.TEXTURE0 + unit);

    // 2. 绑定纹理到当前活动单元
    gl.bindTexture(gl.TEXTURE_2D, texture);

    // 3. 设置uniform指向正确的纹理单元
    gl.uniform1i(location, unit);

    // 4. 验证纹理状态
    if (!gl.isTexture(texture)) {
        console.error('无效的纹理对象');
    }

    // 5. 检查纹理完整性
    const width = gl.getTexParameter(gl.TEXTURE_2D, gl.TEXTURE_WIDTH);
    const height = gl.getTexParameter(gl.TEXTURE_2D, gl.TEXTURE_HEIGHT);
    if (width === 0 || height === 0) {
        console.error('纹理尺寸无效');
    }
}
```

### 3. 矩阵传输问题

**问题现象：**

```javascript
// 3D对象显示异常或不可见
gl.uniformMatrix4fv(mvpLocation, false, matrix);
```

**常见错误和解决方案：**

```javascript
// 错误1: 矩阵数据格式不正确
const matrix = [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]; // 普通数组 - 可能导致性能问题

// 正确做法: 使用Float32Array
const matrix = new Float32Array([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);

// 错误2: 矩阵转置参数错误
gl.uniformMatrix4fv(location, true, matrix); // 错误 - WebGL通常使用列主序
gl.uniformMatrix4fv(location, false, matrix); // 正确

// 错误3: 矩阵计算顺序错误
// 错误的变换顺序
const wrongMVP = multiply(model, multiply(view, projection));

// 正确的变换顺序
const correctMVP = multiply(projection, multiply(view, model));
```

### 4. 性能问题诊断

**问题现象：**

```javascript
// 帧率下降，特别是在更新uniform时
function render() {
    // 每帧都在查询uniform位置 - 性能杀手!
    const location = gl.getUniformLocation(program, 'u_time');
    gl.uniform1f(location, performance.now());
}
```

**优化解决方案：**

```javascript
class OptimizedUniformManager {
    constructor(program) {
        this.program = program;
        this.locations = new Map();
        this.cache = new Map();
        this.updateCount = 0;
        this.skipCount = 0;
        this.cacheUniformLocations();
    }

    cacheUniformLocations() {
        const uniformCount = gl.getProgramParameter(this.program, gl.ACTIVE_UNIFORMS);

        for (let i = 0; i < uniformCount; i++) {
            const info = gl.getActiveUniform(this.program, i);
            const location = gl.getUniformLocation(this.program, info.name);
            this.locations.set(info.name, {
                location,
                type: info.type,
                size: info.size,
            });
        }
    }

    setUniform(name, value) {
        const uniform = this.locations.get(name);
        if (!uniform) {
            console.warn(`Uniform ${name} not found`);
            return false;
        }

        // 值缓存检查
        const cacheKey = name;
        const cachedValue = this.cache.get(cacheKey);

        if (this.valuesEqual(cachedValue, value, uniform.type)) {
            this.skipCount++;
            return true; // 值未改变，跳过更新
        }

        this.updateUniform(uniform, value);
        this.cache.set(cacheKey, this.cloneValue(value));
        this.updateCount++;
        return true;
    }

    updateUniform(uniform, value) {
        const { location, type } = uniform;

        switch (type) {
            case gl.FLOAT:
                gl.uniform1f(location, value);
                break;
            case gl.FLOAT_VEC2:
                gl.uniform2fv(location, value);
                break;
            case gl.FLOAT_VEC3:
                gl.uniform3fv(location, value);
                break;
            case gl.FLOAT_VEC4:
                gl.uniform4fv(location, value);
                break;
            case gl.INT:
            case gl.BOOL:
            case gl.SAMPLER_2D:
            case gl.SAMPLER_CUBE:
                gl.uniform1i(location, value);
                break;
            case gl.INT_VEC2:
            case gl.BOOL_VEC2:
                gl.uniform2iv(location, value);
                break;
            case gl.INT_VEC3:
            case gl.BOOL_VEC3:
                gl.uniform3iv(location, value);
                break;
            case gl.INT_VEC4:
            case gl.BOOL_VEC4:
                gl.uniform4iv(location, value);
                break;
            case gl.FLOAT_MAT2:
                gl.uniformMatrix2fv(location, false, value);
                break;
            case gl.FLOAT_MAT3:
                gl.uniformMatrix3fv(location, false, value);
                break;
            case gl.FLOAT_MAT4:
                gl.uniformMatrix4fv(location, false, value);
                break;
            default:
                console.warn(`Unsupported uniform type: ${type}`);
                return false;
        }
        return true;
    }

    valuesEqual(a, b, type) {
        if (a === undefined || b === undefined) return false;

        switch (type) {
            case gl.FLOAT_MAT4:
                return this.arraysEqual(a, b, 16);
            case gl.FLOAT_MAT3:
                return this.arraysEqual(a, b, 9);
            case gl.FLOAT_MAT2:
                return this.arraysEqual(a, b, 4);
            case gl.FLOAT_VEC4:
            case gl.INT_VEC4:
            case gl.BOOL_VEC4:
                return this.arraysEqual(a, b, 4);
            case gl.FLOAT_VEC3:
            case gl.INT_VEC3:
            case gl.BOOL_VEC3:
                return this.arraysEqual(a, b, 3);
            case gl.FLOAT_VEC2:
            case gl.INT_VEC2:
            case gl.BOOL_VEC2:
                return this.arraysEqual(a, b, 2);
            case gl.FLOAT:
                return Math.abs(a - b) < 1e-6;
            case gl.INT:
            case gl.BOOL:
            case gl.SAMPLER_2D:
            case gl.SAMPLER_CUBE:
                return a === b;
            default:
                return a === b;
        }
    }

    arraysEqual(a, b, length) {
        if (!a || !b || a.length !== length || b.length !== length) {
            return false;
        }

        for (let i = 0; i < length; i++) {
            if (Math.abs(a[i] - b[i]) > 1e-6) {
                return false;
            }
        }
        return true;
    }

    cloneValue(value) {
        if (Array.isArray(value) || value instanceof Float32Array) {
            return new Float32Array(value);
        }
        if (Array.isArray(value) || value instanceof Int32Array) {
            return new Int32Array(value);
        }
        return value;
    }

    // 批量设置uniform
    setUniforms(uniforms) {
        let successCount = 0;
        Object.entries(uniforms).forEach(([name, value]) => {
            if (this.setUniform(name, value)) {
                successCount++;
            }
        });
        return successCount;
    }

    // 清除缓存
    clearCache() {
        this.cache.clear();
        this.updateCount = 0;
        this.skipCount = 0;
    }

    // 获取性能统计
    getStats() {
        return {
            totalUniforms: this.locations.size,
            cacheSize: this.cache.size,
            updateCount: this.updateCount,
            skipCount: this.skipCount,
            cacheHitRate: this.skipCount / (this.updateCount + this.skipCount) || 0
        };
    }

    // 列出所有uniform信息
    listUniforms() {
        return Array.from(this.locations.entries()).map(([name, info]) => ({
            name,
            type: this.getTypeName(info.type),
            size: info.size,
            location: info.location,
            cached: this.cache.has(name)
        }));
    }

    getTypeName(type) {
        const typeNames = {
            [gl.FLOAT]: 'float',
            [gl.FLOAT_VEC2]: 'vec2',
            [gl.FLOAT_VEC3]: 'vec3',
            [gl.FLOAT_VEC4]: 'vec4',
            [gl.INT]: 'int',
            [gl.INT_VEC2]: 'ivec2',
            [gl.INT_VEC3]: 'ivec3',
            [gl.INT_VEC4]: 'ivec4',
            [gl.BOOL]: 'bool',
            [gl.BOOL_VEC2]: 'bvec2',
            [gl.BOOL_VEC3]: 'bvec3',
            [gl.BOOL_VEC4]: 'bvec4',
            [gl.FLOAT_MAT2]: 'mat2',
            [gl.FLOAT_MAT3]: 'mat3',
            [gl.FLOAT_MAT4]: 'mat4',
            [gl.SAMPLER_2D]: 'sampler2D',
            [gl.SAMPLER_CUBE]: 'samplerCube'
        };
        return typeNames[type] || `unknown(${type})`;
    }
}

// 使用示例
const uniformManager = new OptimizedUniformManager(program);

// 单个uniform设置
uniformManager.setUniform('u_modelMatrix', modelMatrix);
uniformManager.setUniform('u_color', [1.0, 0.0, 0.0]);
uniformManager.setUniform('u_texture', 0);

// 批量uniform设置
uniformManager.setUniforms({
    'u_viewMatrix': viewMatrix,
    'u_projectionMatrix': projectionMatrix,
    'u_lightPosition': [10.0, 10.0, 10.0],
    'u_lightColor': [1.0, 1.0, 1.0],
    'u_time': performance.now() * 0.001
});

// 查看性能统计
console.log('Uniform Manager Stats:', uniformManager.getStats());
console.log('All Uniforms:', uniformManager.listUniforms());
}
```

### 5. 调试工具和技巧

```javascript
class UniformDebugger {
    static logAllUniforms(program) {
        console.group('Uniform Variables Debug Info');

        const uniformCount = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
        console.log(`Total active uniforms: ${uniformCount}`);

        for (let i = 0; i < uniformCount; i++) {
            const info = gl.getActiveUniform(program, i);
            const location = gl.getUniformLocation(program, info.name);
            const value = gl.getUniform(program, location);

            console.log(`${info.name}:`, {
                type: this.getTypeName(info.type),
                size: info.size,
                location: location,
                value: value,
            });
        }

        console.groupEnd();
    }

    static getTypeName(type) {
        const typeNames = {
            [gl.FLOAT]: 'float',
            [gl.FLOAT_VEC2]: 'vec2',
            [gl.FLOAT_VEC3]: 'vec3',
            [gl.FLOAT_VEC4]: 'vec4',
            [gl.INT]: 'int',
            [gl.BOOL]: 'bool',
            [gl.FLOAT_MAT2]: 'mat2',
            [gl.FLOAT_MAT3]: 'mat3',
            [gl.FLOAT_MAT4]: 'mat4',
            [gl.SAMPLER_2D]: 'sampler2D',
            [gl.SAMPLER_CUBE]: 'samplerCube',
        };
        return typeNames[type] || `unknown(${type})`;
    }

    static validateUniformUpdate(location, value, expectedType) {
        if (location === null) {
            console.error('Uniform location is null');
            return false;
        }

        if (value === undefined || value === null) {
            console.error('Uniform value is null or undefined');
            return false;
        }

        // 类型检查
        switch (expectedType) {
            case gl.FLOAT_MAT4:
                if (!(value instanceof Float32Array) || value.length !== 16) {
                    console.error('Matrix4 uniform requires Float32Array with 16 elements');
                    return false;
                }
                break;
            case gl.FLOAT_VEC3:
                if ((!Array.isArray(value) && !(value instanceof Float32Array)) || value.length !== 3) {
                    console.error('Vec3 uniform requires array with 3 elements');
                    return false;
                }
                break;
        }

        return true;
    }
}

// 使用示例
UniformDebugger.logAllUniforms(program);
```

### 6. WebGL 错误检查

```javascript
function checkGLError(operation) {
    const error = gl.getError();
    if (error !== gl.NO_ERROR) {
        const errorNames = {
            [gl.INVALID_ENUM]: 'INVALID_ENUM',
            [gl.INVALID_VALUE]: 'INVALID_VALUE',
            [gl.INVALID_OPERATION]: 'INVALID_OPERATION',
            [gl.OUT_OF_MEMORY]: 'OUT_OF_MEMORY',
            [gl.CONTEXT_LOST_WEBGL]: 'CONTEXT_LOST_WEBGL',
        };

        console.error(`WebGL Error after ${operation}: ${errorNames[error] || error}`);
        return false;
    }
    return true;
}

// 在uniform更新后检查错误
gl.uniformMatrix4fv(location, false, matrix);
checkGLError('uniformMatrix4fv');
```

这些故障排除指南帮助开发者快速识别和解决 Uniform 变量管理中的常见问题，确保 WebGL 应用程序的稳定性和性能。

## 🔧 文档修正说明

### 修正的主要问题

本文档在 2024 年进行了重要修正，解决了以下关键概念错误：

#### 1. **"全局 Uniform"概念错误**

**❌ 原始错误理解：**

```javascript
// 错误：认为可以一次性设置全局uniform影响所有程序
setGlobalUniforms(scene, camera) {
    this.setUniform('u_viewMatrix', camera.getViewMatrix()); // 没有激活程序！
    this.setUniform('u_projectionMatrix', camera.getProjectionMatrix());
}

render() {
    this.setGlobalUniforms(scene, camera); // 在useProgram之前调用
    // ... 后续程序切换
}
```

**✅ 修正后的正确理解：**

```javascript
// 正确：准备数据，然后为每个程序单独应用
prepareGlobalUniformData(scene, camera) {
    // 只准备数据，不传输到GPU
    const globalData = new Map();
    globalData.set('u_viewMatrix', camera.getViewMatrix());
    globalData.set('u_projectionMatrix', camera.getProjectionMatrix());
    return globalData;
}

applyGlobalUniforms(program, globalUniformData) {
    // 确保程序已激活后再设置uniform
    globalUniformData.forEach((value, name) => {
        this.setUniformWithCache(program, name, value);
    });
}
```

#### 2. **时机错误**

**❌ 错误的执行顺序：**

```
设置全局uniform → 切换程序 → 设置材质uniform → 渲染
```

**✅ 正确的执行顺序：**

```
准备全局数据 → 切换程序 → 应用全局uniform → 设置材质uniform → 渲染
```

#### 3. **缺少缓存优化**

**修正前：** 每次都重复设置 uniform，没有缓存机制
**修正后：** 添加了程序级别的 uniform 缓存，避免重复设置

### 关键修正点

1. **概念澄清**: WebGL 中没有真正的"全局"uniform，每个程序都有独立的 uniform 空间
2. **流程修正**: 先准备数据，再为每个程序单独应用
3. **性能优化**: 添加缓存机制，避免重复设置相同值
4. **错误处理**: 添加程序状态检查和错误提示

### 学习要点

-   **Uniform 变量是程序级别的**，不是全局的
-   **必须在 useProgram 之后设置 uniform**
-   **使用缓存优化避免重复设置**
-   **理解 WebGL 状态机的工作原理**

这些修正确保了文档的技术准确性，避免了可能导致渲染错误或性能问题的概念误解。
