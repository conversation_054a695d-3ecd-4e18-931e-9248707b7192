# WebGL 渲染流程实战教程

## 📖 教程概述

本教程通过渲染三个经典 3D 形状（立方体、球体、三角形）来深入理解 WebGL 的完整渲染流程。我们将提供两个版本的实现：

-   **OGL 框架版本**：使用当前项目的 OGL 框架
-   **原生 WebGL 版本**：使用纯 WebGL API

通过对比学习，您将深入理解 WebGL 的底层机制和框架的抽象价值。

---

## 🎯 学习目标

-   掌握 WebGL 渲染管线的完整流程
-   理解顶点数据、着色器、纹理的作用
-   学会创建和渲染基础 3D 几何体
-   对比框架与原生 API 的差异
-   建立完整的 3D 渲染知识体系

---

## 📋 WebGL 渲染流程概览

### 核心渲染管线

```
1. 顶点数据准备 → 2. 着色器编译 → 3. 缓冲区创建 → 4. 渲染状态设置 → 5. 绘制调用
```

### 详细流程步骤

1. **初始化阶段**

    - 获取 WebGL 上下文
    - 设置视口和画布
    - 编译着色器程序

2. **数据准备阶段**

    - 创建几何体顶点数据
    - 生成缓冲区对象
    - 设置顶点属性

3. **渲染设置阶段**

    - 配置相机和投影矩阵
    - 设置光照和材质参数
    - 绑定纹理和统一变量

4. **绘制执行阶段**
    - 清除缓冲区
    - 激活着色器程序
    - 执行绘制调用

---

## 🔺 场景一：三角形渲染

### OGL 框架版本

```javascript
import { Renderer, Camera, Transform, Program, Geometry, Mesh } from '../src/index.js';

// 1. 初始化渲染器
const renderer = new Renderer({
    canvas: document.getElementById('canvas'),
    width: 800,
    height: 600,
    alpha: false,
});
const gl = renderer.gl;

// 2. 创建相机
const camera = new Camera(gl, { fov: 45 });
camera.position.set(0, 0, 5);

// 3. 定义三角形顶点数据
const triangleVertices = new Float32Array([
    0.0,
    0.5,
    0.0, // 顶点
    -0.5,
    -0.5,
    0.0, // 左下
    0.5,
    -0.5,
    0.0, // 右下
]);

const triangleColors = new Float32Array([
    1.0,
    0.0,
    0.0, // 红色
    0.0,
    1.0,
    0.0, // 绿色
    0.0,
    0.0,
    1.0, // 蓝色
]);

// 4. 创建几何体
const geometry = new Geometry(gl, {
    position: { size: 3, data: triangleVertices },
    color: { size: 3, data: triangleColors },
});

// 5. 定义着色器
const vertexShader = `
    attribute vec3 position;
    attribute vec3 color;
    uniform mat4 modelViewMatrix;
    uniform mat4 projectionMatrix;
    varying vec3 vColor;
    
    void main() {
        vColor = color;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
`;

const fragmentShader = `
    precision mediump float;
    varying vec3 vColor;
    
    void main() {
        gl_FragColor = vec4(vColor, 1.0);
    }
`;

// 6. 创建着色器程序
const program = new Program(gl, {
    vertex: vertexShader,
    fragment: fragmentShader,
});

// 7. 创建网格
const triangle = new Mesh(gl, { geometry, program });

// 8. 渲染循环
function render() {
    triangle.rotation.z += 0.01;
    renderer.render({ scene: triangle, camera });
    requestAnimationFrame(render);
}
render();
```

### 原生 WebGL 版本

```javascript
// 1. 获取WebGL上下文
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl');

// 2. 编译着色器函数
function createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
    }
    return shader;
}

// 3. 创建着色器程序
function createProgram(gl, vertexShader, fragmentShader) {
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('程序链接错误:', gl.getProgramInfoLog(program));
        gl.deleteProgram(program);
        return null;
    }
    return program;
}

// 4. 着色器源码
const vertexShaderSource = `
    attribute vec3 a_position;
    attribute vec3 a_color;
    uniform mat4 u_matrix;
    varying vec3 v_color;
    
    void main() {
        v_color = a_color;
        gl_Position = u_matrix * vec4(a_position, 1.0);
    }
`;

const fragmentShaderSource = `
    precision mediump float;
    varying vec3 v_color;
    
    void main() {
        gl_FragColor = vec4(v_color, 1.0);
    }
`;

// 5. 编译着色器
const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
const program = createProgram(gl, vertexShader, fragmentShader);

// 6. 获取属性和统一变量位置
const positionLocation = gl.getAttribLocation(program, 'a_position');
const colorLocation = gl.getAttribLocation(program, 'a_color');
const matrixLocation = gl.getUniformLocation(program, 'u_matrix');

// 7. 创建缓冲区
const positionBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.bufferData(gl.ARRAY_BUFFER, triangleVertices, gl.STATIC_DRAW);

const colorBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
gl.bufferData(gl.ARRAY_BUFFER, triangleColors, gl.STATIC_DRAW);

// 8. 设置视口和渲染状态
gl.viewport(0, 0, canvas.width, canvas.height);
gl.clearColor(0.0, 0.0, 0.0, 1.0);
gl.enable(gl.DEPTH_TEST);

// 9. 渲染函数
function drawTriangle() {
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    gl.useProgram(program);

    // 绑定位置属性
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

    // 绑定颜色属性
    gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
    gl.enableVertexAttribArray(colorLocation);
    gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

    // 设置变换矩阵
    const matrix = createPerspectiveMatrix();
    gl.uniformMatrix4fv(matrixLocation, false, matrix);

    // 绘制三角形
    gl.drawArrays(gl.TRIANGLES, 0, 3);
}

// 10. 渲染循环
function render() {
    drawTriangle();
    requestAnimationFrame(render);
}
render();
```

---

## 📦 场景二：立方体渲染

### OGL 框架版本

```javascript
import { BoxGeometry } from '../src/extras/index.js';

// 1. 创建立方体几何体（使用内置几何体）
const cubeGeometry = new BoxGeometry(gl, {
    width: 1,
    height: 1,
    depth: 1,
});

// 2. 创建纹理
const texture = new Texture(gl, {
    generateMipmaps: false,
});

// 加载纹理图像
const image = new Image();
image.onload = () => (texture.image = image);
image.src = 'path/to/texture.jpg';

// 3. 立方体着色器
const cubeVertexShader = `
    attribute vec3 position;
    attribute vec3 normal;
    attribute vec2 uv;

    uniform mat4 modelViewMatrix;
    uniform mat4 projectionMatrix;
    uniform mat3 normalMatrix;

    varying vec3 vNormal;
    varying vec2 vUv;

    void main() {
        vNormal = normalize(normalMatrix * normal);
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
`;

const cubeFragmentShader = `
    precision mediump float;

    uniform sampler2D tMap;
    uniform vec3 uLightDirection;

    varying vec3 vNormal;
    varying vec2 vUv;

    void main() {
        vec3 normal = normalize(vNormal);
        float light = dot(normal, normalize(uLightDirection));
        light = max(0.2, light);

        vec4 texColor = texture2D(tMap, vUv);
        gl_FragColor = vec4(texColor.rgb * light, texColor.a);
    }
`;

// 4. 创建立方体程序
const cubeProgram = new Program(gl, {
    vertex: cubeVertexShader,
    fragment: cubeFragmentShader,
    uniforms: {
        tMap: { value: texture },
        uLightDirection: { value: [1, 1, 1] },
    },
});

// 5. 创建立方体网格
const cube = new Mesh(gl, {
    geometry: cubeGeometry,
    program: cubeProgram,
});

cube.position.set(0, 0, 0);

// 6. 渲染循环
function renderCube() {
    cube.rotation.x += 0.01;
    cube.rotation.y += 0.01;
    renderer.render({ scene: cube, camera });
    requestAnimationFrame(renderCube);
}
renderCube();
```

### 原生 WebGL 版本

```javascript
// 1. 立方体顶点数据（包含位置、法线、UV）
const cubeVertices = new Float32Array([
    // 前面
    -0.5, -0.5, 0.5, 0.0, 0.0, 1.0, 0.0, 0.0, 0.5, -0.5, 0.5, 0.0, 0.0, 1.0, 1.0, 0.0, 0.5, 0.5, 0.5, 0.0, 0.0, 1.0, 1.0, 1.0, -0.5, 0.5, 0.5, 0.0, 0.0, 1.0, 0.0, 1.0,

    // 后面
    -0.5, -0.5, -0.5, 0.0, 0.0, -1.0, 1.0, 0.0, -0.5, 0.5, -0.5, 0.0, 0.0, -1.0, 1.0, 1.0, 0.5, 0.5, -0.5, 0.0, 0.0, -1.0, 0.0, 1.0, 0.5, -0.5, -0.5, 0.0, 0.0, -1.0, 0.0, 0.0,

    // 顶面
    -0.5, 0.5, -0.5, 0.0, 1.0, 0.0, 0.0, 1.0, -0.5, 0.5, 0.5, 0.0, 1.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 0.0, 1.0, 0.0, 1.0, 0.0, 0.5, 0.5, -0.5, 0.0, 1.0, 0.0, 1.0, 1.0,

    // 底面
    -0.5, -0.5, -0.5, 0.0, -1.0, 0.0, 1.0, 1.0, 0.5, -0.5, -0.5, 0.0, -1.0, 0.0, 0.0, 1.0, 0.5, -0.5, 0.5, 0.0, -1.0, 0.0, 0.0, 0.0, -0.5, -0.5, 0.5, 0.0, -1.0, 0.0, 1.0, 0.0,

    // 右面
    0.5, -0.5, -0.5, 1.0, 0.0, 0.0, 1.0, 0.0, 0.5, 0.5, -0.5, 1.0, 0.0, 0.0, 1.0, 1.0, 0.5, 0.5, 0.5, 1.0, 0.0, 0.0, 0.0, 1.0, 0.5, -0.5, 0.5, 1.0, 0.0, 0.0, 0.0, 0.0,

    // 左面
    -0.5, -0.5, -0.5, -1.0, 0.0, 0.0, 0.0, 0.0, -0.5, -0.5, 0.5, -1.0, 0.0, 0.0, 1.0, 0.0, -0.5, 0.5, 0.5, -1.0, 0.0, 0.0, 1.0, 1.0, -0.5, 0.5, -0.5, -1.0, 0.0, 0.0, 0.0, 1.0,
]);

// 2. 立方体索引数据
const cubeIndices = new Uint16Array([
    0,
    1,
    2,
    0,
    2,
    3, // 前面
    4,
    5,
    6,
    4,
    6,
    7, // 后面
    8,
    9,
    10,
    8,
    10,
    11, // 顶面
    12,
    13,
    14,
    12,
    14,
    15, // 底面
    16,
    17,
    18,
    16,
    18,
    19, // 右面
    20,
    21,
    22,
    20,
    22,
    23, // 左面
]);

// 3. 立方体着色器源码
const cubeVertexShaderSource = `
    attribute vec3 a_position;
    attribute vec3 a_normal;
    attribute vec2 a_uv;

    uniform mat4 u_modelViewMatrix;
    uniform mat4 u_projectionMatrix;
    uniform mat3 u_normalMatrix;

    varying vec3 v_normal;
    varying vec2 v_uv;

    void main() {
        v_normal = normalize(u_normalMatrix * a_normal);
        v_uv = a_uv;
        gl_Position = u_projectionMatrix * u_modelViewMatrix * vec4(a_position, 1.0);
    }
`;

const cubeFragmentShaderSource = `
    precision mediump float;

    uniform sampler2D u_texture;
    uniform vec3 u_lightDirection;

    varying vec3 v_normal;
    varying vec2 v_uv;

    void main() {
        vec3 normal = normalize(v_normal);
        float light = dot(normal, normalize(u_lightDirection));
        light = max(0.2, light);

        vec4 texColor = texture2D(u_texture, v_uv);
        gl_FragColor = vec4(texColor.rgb * light, texColor.a);
    }
`;

// 4. 创建立方体程序
const cubeVertexShader = createShader(gl, gl.VERTEX_SHADER, cubeVertexShaderSource);
const cubeFragmentShader = createShader(gl, gl.FRAGMENT_SHADER, cubeFragmentShaderSource);
const cubeProgram = createProgram(gl, cubeVertexShader, cubeFragmentShader);

// 5. 获取属性和统一变量位置
const cubePositionLocation = gl.getAttribLocation(cubeProgram, 'a_position');
const cubeNormalLocation = gl.getAttribLocation(cubeProgram, 'a_normal');
const cubeUvLocation = gl.getAttribLocation(cubeProgram, 'a_uv');
const cubeModelViewMatrixLocation = gl.getUniformLocation(cubeProgram, 'u_modelViewMatrix');
const cubeProjectionMatrixLocation = gl.getUniformLocation(cubeProgram, 'u_projectionMatrix');
const cubeNormalMatrixLocation = gl.getUniformLocation(cubeProgram, 'u_normalMatrix');
const cubeTextureLocation = gl.getUniformLocation(cubeProgram, 'u_texture');
const cubeLightDirectionLocation = gl.getUniformLocation(cubeProgram, 'u_lightDirection');

// 6. 创建缓冲区
const cubeVertexBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, cubeVertexBuffer);
gl.bufferData(gl.ARRAY_BUFFER, cubeVertices, gl.STATIC_DRAW);

const cubeIndexBuffer = gl.createBuffer();
gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, cubeIndexBuffer);
gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, cubeIndices, gl.STATIC_DRAW);

// 7. 创建纹理
function createTexture(gl, image) {
    const texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    return texture;
}

// 8. 立方体渲染函数
let cubeRotation = 0;
function drawCube(texture) {
    gl.useProgram(cubeProgram);

    // 绑定顶点缓冲区
    gl.bindBuffer(gl.ARRAY_BUFFER, cubeVertexBuffer);

    // 设置位置属性
    gl.enableVertexAttribArray(cubePositionLocation);
    gl.vertexAttribPointer(cubePositionLocation, 3, gl.FLOAT, false, 32, 0);

    // 设置法线属性
    gl.enableVertexAttribArray(cubeNormalLocation);
    gl.vertexAttribPointer(cubeNormalLocation, 3, gl.FLOAT, false, 32, 12);

    // 设置UV属性
    gl.enableVertexAttribArray(cubeUvLocation);
    gl.vertexAttribPointer(cubeUvLocation, 2, gl.FLOAT, false, 32, 24);

    // 绑定索引缓冲区
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, cubeIndexBuffer);

    // 设置变换矩阵
    cubeRotation += 0.01;
    const modelViewMatrix = createModelViewMatrix(cubeRotation);
    const projectionMatrix = createPerspectiveMatrix();
    const normalMatrix = createNormalMatrix(modelViewMatrix);

    gl.uniformMatrix4fv(cubeModelViewMatrixLocation, false, modelViewMatrix);
    gl.uniformMatrix4fv(cubeProjectionMatrixLocation, false, projectionMatrix);
    gl.uniformMatrix3fv(cubeNormalMatrixLocation, false, normalMatrix);

    // 设置光照方向
    gl.uniform3fv(cubeLightDirectionLocation, [1.0, 1.0, 1.0]);

    // 绑定纹理
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.uniform1i(cubeTextureLocation, 0);

    // 绘制立方体
    gl.drawElements(gl.TRIANGLES, cubeIndices.length, gl.UNSIGNED_SHORT, 0);
}
```

---

## 🌐 场景三：球体渲染

### OGL 框架版本

```javascript
import { SphereGeometry } from '../src/extras/index.js';

// 1. 创建球体几何体
const sphereGeometry = new SphereGeometry(gl, {
    radius: 1,
    widthSegments: 32,
    heightSegments: 16,
});

// 2. 球体着色器（带环境映射）
const sphereVertexShader = `
    attribute vec3 position;
    attribute vec3 normal;
    
    uniform mat4 modelViewMatrix;
    uniform mat4 projectionMatrix;
    uniform mat3 normalMatrix;
    uniform mat4 modelMatrix;
    
    varying vec3 vNormal;
    varying vec3 vWorldPosition;
    
    void main() {
        vNormal = normalize(normalMatrix * normal);
        vWorldPosition = (modelMatrix * vec4(position, 1.0)).xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
`;

const sphereFragmentShader = `
    precision mediump float;
    
    uniform vec3 cameraPosition;
    uniform samplerCube tCube;
    
    varying vec3 vNormal;
    varying vec3 vWorldPosition;
    
    void main() {
        vec3 viewDirection = normalize(vWorldPosition - cameraPosition);
        vec3 reflectDirection = reflect(viewDirection, normalize(vNormal));
        
        vec4 envColor = textureCube(tCube, reflectDirection);
        gl_FragColor = envColor;
    }
`;

// 3. 创建球体程序
const sphereProgram = new Program(gl, {
    vertex: sphereVertexShader,
    fragment: sphereFragmentShader,
    uniforms: {
        tCube: { value: environmentTexture },
        cameraPosition: { value: camera.position },
    },
});

// 4. 创建球体网格
const sphere = new Mesh(gl, {
    geometry: sphereGeometry,
    program: sphereProgram,
});

sphere.position.set(2, 0, 0);

// 5. 渲染循环
function renderSphere() {
    sphere.rotation.y += 0.005;
    sphereProgram.uniforms.cameraPosition.value = camera.position;
    renderer.render({ scene: sphere, camera });
    requestAnimationFrame(renderSphere);
}
renderSphere();
```

### 原生 WebGL 版本

```javascript
// 1. 球体几何体生成函数
function createSphereGeometry(radius = 1, widthSegments = 32, heightSegments = 16) {
    const vertices = [];
    const normals = [];
    const uvs = [];
    const indices = [];

    // 生成顶点、法线和UV坐标
    for (let y = 0; y <= heightSegments; y++) {
        const v = y / heightSegments;
        const phi = v * Math.PI;

        for (let x = 0; x <= widthSegments; x++) {
            const u = x / widthSegments;
            const theta = u * Math.PI * 2;

            // 球面坐标转换为笛卡尔坐标
            const sinPhi = Math.sin(phi);
            const cosPhi = Math.cos(phi);
            const sinTheta = Math.sin(theta);
            const cosTheta = Math.cos(theta);

            const px = radius * sinPhi * cosTheta;
            const py = radius * cosPhi;
            const pz = radius * sinPhi * sinTheta;

            // 顶点位置
            vertices.push(px, py, pz);

            // 法线（球心指向顶点的单位向量）
            normals.push(px / radius, py / radius, pz / radius);

            // UV坐标
            uvs.push(u, 1 - v);
        }
    }

    // 生成索引
    for (let y = 0; y < heightSegments; y++) {
        for (let x = 0; x < widthSegments; x++) {
            const a = y * (widthSegments + 1) + x;
            const b = a + widthSegments + 1;
            const c = a + 1;
            const d = b + 1;

            // 两个三角形组成一个四边形
            indices.push(a, b, c);
            indices.push(c, b, d);
        }
    }

    return {
        vertices: new Float32Array(vertices),
        normals: new Float32Array(normals),
        uvs: new Float32Array(uvs),
        indices: new Uint16Array(indices),
    };
}

// 2. 创建球体数据
const sphereData = createSphereGeometry(1, 32, 16);

// 3. 球体着色器源码
const sphereVertexShaderSource = `
    attribute vec3 a_position;
    attribute vec3 a_normal;
    attribute vec2 a_uv;

    uniform mat4 u_modelViewMatrix;
    uniform mat4 u_projectionMatrix;
    uniform mat3 u_normalMatrix;

    varying vec3 v_normal;
    varying vec2 v_uv;

    void main() {
        v_normal = normalize(u_normalMatrix * a_normal);
        v_uv = a_uv;
        gl_Position = u_projectionMatrix * u_modelViewMatrix * vec4(a_position, 1.0);
    }
`;

const sphereFragmentShaderSource = `
    precision mediump float;

    uniform vec3 u_lightDirection;
    uniform vec3 u_baseColor;

    varying vec3 v_normal;
    varying vec2 v_uv;

    void main() {
        vec3 normal = normalize(v_normal);

        // 简单的漫反射光照
        float diffuse = max(0.0, dot(normal, normalize(u_lightDirection)));

        // 最终颜色
        vec3 color = u_baseColor * (0.2 + 0.8 * diffuse);
        gl_FragColor = vec4(color, 1.0);
    }
`;

// 4. 创建球体程序
const sphereVertexShader = createShader(gl, gl.VERTEX_SHADER, sphereVertexShaderSource);
const sphereFragmentShader = createShader(gl, gl.FRAGMENT_SHADER, sphereFragmentShaderSource);
const sphereProgram = createProgram(gl, sphereVertexShader, sphereFragmentShader);

// 5. 获取属性和统一变量位置
const spherePositionLocation = gl.getAttribLocation(sphereProgram, 'a_position');
const sphereNormalLocation = gl.getAttribLocation(sphereProgram, 'a_normal');
const sphereUvLocation = gl.getAttribLocation(sphereProgram, 'a_uv');
const sphereModelViewMatrixLocation = gl.getUniformLocation(sphereProgram, 'u_modelViewMatrix');
const sphereProjectionMatrixLocation = gl.getUniformLocation(sphereProgram, 'u_projectionMatrix');
const sphereNormalMatrixLocation = gl.getUniformLocation(sphereProgram, 'u_normalMatrix');
const sphereLightDirectionLocation = gl.getUniformLocation(sphereProgram, 'u_lightDirection');
const sphereBaseColorLocation = gl.getUniformLocation(sphereProgram, 'u_baseColor');

// 6. 创建球体缓冲区
const sphereVertexBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, sphereVertexBuffer);

// 交错存储顶点数据（位置、法线、UV）
const sphereVertexData = [];
for (let i = 0; i < sphereData.vertices.length / 3; i++) {
    // 位置
    sphereVertexData.push(sphereData.vertices[i * 3], sphereData.vertices[i * 3 + 1], sphereData.vertices[i * 3 + 2]);
    // 法线
    sphereVertexData.push(sphereData.normals[i * 3], sphereData.normals[i * 3 + 1], sphereData.normals[i * 3 + 2]);
    // UV
    sphereVertexData.push(sphereData.uvs[i * 2], sphereData.uvs[i * 2 + 1]);
}

gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(sphereVertexData), gl.STATIC_DRAW);

const sphereIndexBuffer = gl.createBuffer();
gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, sphereIndexBuffer);
gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, sphereData.indices, gl.STATIC_DRAW);

// 7. 球体渲染函数
let sphereRotation = 0;
function drawSphere() {
    gl.useProgram(sphereProgram);

    // 绑定顶点缓冲区
    gl.bindBuffer(gl.ARRAY_BUFFER, sphereVertexBuffer);

    // 设置位置属性（步长32字节，偏移0）
    gl.enableVertexAttribArray(spherePositionLocation);
    gl.vertexAttribPointer(spherePositionLocation, 3, gl.FLOAT, false, 32, 0);

    // 设置法线属性（步长32字节，偏移12）
    gl.enableVertexAttribArray(sphereNormalLocation);
    gl.vertexAttribPointer(sphereNormalLocation, 3, gl.FLOAT, false, 32, 12);

    // 设置UV属性（步长32字节，偏移24）
    gl.enableVertexAttribArray(sphereUvLocation);
    gl.vertexAttribPointer(sphereUvLocation, 2, gl.FLOAT, false, 32, 24);

    // 绑定索引缓冲区
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, sphereIndexBuffer);

    // 设置变换矩阵
    sphereRotation += 0.005;
    const modelViewMatrix = createSphereModelViewMatrix(sphereRotation);
    const projectionMatrix = createPerspectiveMatrix();
    const normalMatrix = createNormalMatrix(modelViewMatrix);

    gl.uniformMatrix4fv(sphereModelViewMatrixLocation, false, modelViewMatrix);
    gl.uniformMatrix4fv(sphereProjectionMatrixLocation, false, projectionMatrix);
    gl.uniformMatrix3fv(sphereNormalMatrixLocation, false, normalMatrix);

    // 设置光照和材质参数
    gl.uniform3fv(sphereLightDirectionLocation, [1.0, 1.0, 1.0]);
    gl.uniform3fv(sphereBaseColorLocation, [0.2, 0.6, 1.0]); // 蓝色

    // 绘制球体
    gl.drawElements(gl.TRIANGLES, sphereData.indices.length, gl.UNSIGNED_SHORT, 0);
}
```

---

## 🔄 完整场景组合

### 多对象渲染场景

```javascript
// 创建场景容器
const scene = new Transform();

// 添加所有对象到场景
triangle.setParent(scene);
cube.setParent(scene);
sphere.setParent(scene);

// 设置对象位置
triangle.position.set(-2, 0, 0);
cube.position.set(0, 0, 0);
sphere.position.set(2, 0, 0);

// 统一渲染循环
function renderScene() {
    // 更新动画
    triangle.rotation.z += 0.01;
    cube.rotation.x += 0.01;
    cube.rotation.y += 0.01;
    sphere.rotation.y += 0.005;

    // 渲染整个场景
    renderer.render({ scene, camera });
    requestAnimationFrame(renderScene);
}
renderScene();
```

---

## 🧮 数学辅助函数库

### 原生 WebGL 必需的数学函数

```javascript
// 1. 矩阵工具函数
function createPerspectiveMatrix(fov = 45, aspect = 1, near = 0.1, far = 100) {
    const f = Math.tan(Math.PI * 0.5 - (0.5 * fov * Math.PI) / 180);
    const rangeInv = 1.0 / (near - far);

    return new Float32Array([f / aspect, 0, 0, 0, 0, f, 0, 0, 0, 0, (near + far) * rangeInv, -1, 0, 0, near * far * rangeInv * 2, 0]);
}

function createModelViewMatrix(rotation = 0) {
    const cos = Math.cos(rotation);
    const sin = Math.sin(rotation);

    return new Float32Array([
        cos,
        0,
        sin,
        0,
        0,
        1,
        0,
        0,
        -sin,
        0,
        cos,
        0,
        0,
        0,
        -5,
        1, // 相机距离
    ]);
}

function createSphereModelViewMatrix(rotation = 0) {
    const cos = Math.cos(rotation);
    const sin = Math.sin(rotation);

    return new Float32Array([
        cos,
        0,
        sin,
        0,
        0,
        1,
        0,
        0,
        -sin,
        0,
        cos,
        0,
        2,
        0,
        -5,
        1, // 球体位置 (2, 0, -5)
    ]);
}

function createNormalMatrix(modelViewMatrix) {
    // 提取3x3旋转部分并求逆转置
    const m = modelViewMatrix;
    const a00 = m[0],
        a01 = m[1],
        a02 = m[2];
    const a10 = m[4],
        a11 = m[5],
        a12 = m[6];
    const a20 = m[8],
        a21 = m[9],
        a22 = m[10];

    const b01 = a22 * a11 - a12 * a21;
    const b11 = -a22 * a10 + a12 * a20;
    const b21 = a21 * a10 - a11 * a20;

    const det = a00 * b01 + a01 * b11 + a02 * b21;

    if (!det) return new Float32Array(9);

    const detInv = 1.0 / det;

    return new Float32Array([
        b01 * detInv,
        (-a22 * a01 + a02 * a21) * detInv,
        (a12 * a01 - a02 * a11) * detInv,
        b11 * detInv,
        (a22 * a00 - a02 * a20) * detInv,
        (-a12 * a00 + a02 * a10) * detInv,
        b21 * detInv,
        (-a21 * a00 + a01 * a20) * detInv,
        (a11 * a00 - a01 * a10) * detInv,
    ]);
}

// 2. 矩阵乘法
function multiplyMatrices(a, b) {
    const result = new Float32Array(16);

    for (let i = 0; i < 4; i++) {
        for (let j = 0; j < 4; j++) {
            result[i * 4 + j] = a[i * 4 + 0] * b[0 * 4 + j] + a[i * 4 + 1] * b[1 * 4 + j] + a[i * 4 + 2] * b[2 * 4 + j] + a[i * 4 + 3] * b[3 * 4 + j];
        }
    }

    return result;
}

// 3. 创建模型矩阵
function createModelMatrix(translation = [0, 0, 0], rotation = [0, 0, 0], scale = [1, 1, 1]) {
    const [tx, ty, tz] = translation;
    const [rx, ry, rz] = rotation;
    const [sx, sy, sz] = scale;

    // 简化版本：只处理Y轴旋转
    const cos = Math.cos(ry);
    const sin = Math.sin(ry);

    return new Float32Array([cos * sx, 0, sin * sx, 0, 0, sy, 0, 0, -sin * sz, 0, cos * sz, 0, tx, ty, tz, 1]);
}

// 4. 视图矩阵
const viewMatrix = new Float32Array([
    1,
    0,
    0,
    0,
    0,
    1,
    0,
    0,
    0,
    0,
    1,
    0,
    0,
    0,
    -5,
    1, // 相机位置
]);
```

---

## 📊 性能对比分析

### OGL 框架优势

-   **代码简洁性**：减少 90%的样板代码
-   **状态管理**：自动优化 WebGL 状态切换
-   **内存管理**：自动资源清理和缓存
-   **错误处理**：完善的错误检查和调试支持

### 原生 WebGL 优势

-   **完全控制**：对每个 WebGL 调用的精确控制
-   **性能极致**：无框架开销，最大化性能
-   **学习价值**：深入理解 WebGL 底层机制
-   **兼容性**：不依赖第三方库

---

## 🚀 完整实战示例

### 原生 WebGL 完整渲染循环

```javascript
// HTML结构
// <canvas id="webgl-canvas" width="800" height="600"></canvas>

// 主渲染函数
function initWebGLScene() {
    // 1. 初始化WebGL上下文
    const canvas = document.getElementById('webgl-canvas');
    const gl = canvas.getContext('webgl');

    if (!gl) {
        console.error('WebGL不支持');
        return;
    }

    // 2. 设置视口和渲染状态
    gl.viewport(0, 0, canvas.width, canvas.height);
    gl.clearColor(0.1, 0.1, 0.1, 1.0);
    gl.enable(gl.DEPTH_TEST);
    gl.enable(gl.CULL_FACE);

    // 3. 编译所有着色器程序
    const triangleProgram = createProgram(gl, createShader(gl, gl.VERTEX_SHADER, vertexShaderSource), createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource));

    const cubeProgram = createProgram(gl, createShader(gl, gl.VERTEX_SHADER, cubeVertexShaderSource), createShader(gl, gl.FRAGMENT_SHADER, cubeFragmentShaderSource));

    const sphereProgram = createProgram(gl, createShader(gl, gl.VERTEX_SHADER, sphereVertexShaderSource), createShader(gl, gl.FRAGMENT_SHADER, sphereFragmentShaderSource));

    // 4. 创建所有几何体数据
    setupTriangleGeometry(gl);
    setupCubeGeometry(gl);
    setupSphereGeometry(gl);

    // 5. 主渲染循环
    let time = 0;
    function render() {
        time += 0.016; // 约60FPS

        // 清除缓冲区
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

        // 渲染三角形
        drawTriangle();

        // 渲染立方体
        drawCube();

        // 渲染球体
        drawSphere();

        requestAnimationFrame(render);
    }

    render();
}

// 启动应用
window.addEventListener('load', initWebGLScene);
```

### OGL 框架完整示例

```javascript
import { Renderer, Camera, Transform, Program, Geometry, Mesh, BoxGeometry, SphereGeometry } from '../src/index.js';

class WebGLScene {
    constructor(canvas) {
        this.setupRenderer(canvas);
        this.setupCamera();
        this.setupScene();
        this.setupObjects();
        this.startRenderLoop();
    }

    setupRenderer(canvas) {
        this.renderer = new Renderer({
            canvas,
            width: 800,
            height: 600,
            alpha: false,
            depth: true,
            antialias: true,
        });
        this.gl = this.renderer.gl;
    }

    setupCamera() {
        this.camera = new Camera(this.gl, {
            fov: 45,
            aspect: 800 / 600,
            near: 0.1,
            far: 100,
        });
        this.camera.position.set(0, 0, 8);
    }

    setupScene() {
        this.scene = new Transform();
    }

    setupObjects() {
        // 创建三角形
        this.triangle = this.createTriangle();
        this.triangle.position.set(-3, 0, 0);
        this.triangle.setParent(this.scene);

        // 创建立方体
        this.cube = this.createCube();
        this.cube.position.set(0, 0, 0);
        this.cube.setParent(this.scene);

        // 创建球体
        this.sphere = this.createSphere();
        this.sphere.position.set(3, 0, 0);
        this.sphere.setParent(this.scene);
    }

    createTriangle() {
        const geometry = new Geometry(this.gl, {
            position: {
                size: 3,
                data: new Float32Array([0.0, 0.8, 0.0, -0.8, -0.8, 0.0, 0.8, -0.8, 0.0]),
            },
            color: {
                size: 3,
                data: new Float32Array([1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0]),
            },
        });

        const program = new Program(this.gl, {
            vertex: `
                attribute vec3 position;
                attribute vec3 color;
                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;
                varying vec3 vColor;

                void main() {
                    vColor = color;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragment: `
                precision mediump float;
                varying vec3 vColor;

                void main() {
                    gl_FragColor = vec4(vColor, 1.0);
                }
            `,
        });

        return new Mesh(this.gl, { geometry, program });
    }

    createCube() {
        const geometry = new BoxGeometry(this.gl, {
            width: 1.5,
            height: 1.5,
            depth: 1.5,
        });

        const program = new Program(this.gl, {
            vertex: `
                attribute vec3 position;
                attribute vec3 normal;
                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;
                uniform mat3 normalMatrix;
                varying vec3 vNormal;

                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragment: `
                precision mediump float;
                uniform vec3 uLightDirection;
                varying vec3 vNormal;

                void main() {
                    vec3 normal = normalize(vNormal);
                    float light = max(0.2, dot(normal, normalize(uLightDirection)));
                    vec3 color = vec3(0.8, 0.4, 0.2) * light;
                    gl_FragColor = vec4(color, 1.0);
                }
            `,
            uniforms: {
                uLightDirection: { value: [1, 1, 1] },
            },
        });

        return new Mesh(this.gl, { geometry, program });
    }

    createSphere() {
        const geometry = new SphereGeometry(this.gl, {
            radius: 0.8,
            widthSegments: 32,
            heightSegments: 16,
        });

        const program = new Program(this.gl, {
            vertex: `
                attribute vec3 position;
                attribute vec3 normal;
                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;
                uniform mat3 normalMatrix;
                varying vec3 vNormal;

                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragment: `
                precision mediump float;
                uniform vec3 uLightDirection;
                varying vec3 vNormal;

                void main() {
                    vec3 normal = normalize(vNormal);
                    float light = max(0.2, dot(normal, normalize(uLightDirection)));
                    vec3 color = vec3(0.2, 0.6, 1.0) * light;
                    gl_FragColor = vec4(color, 1.0);
                }
            `,
            uniforms: {
                uLightDirection: { value: [1, 1, 1] },
            },
        });

        return new Mesh(this.gl, { geometry, program });
    }

    startRenderLoop() {
        const render = () => {
            // 更新动画
            this.triangle.rotation.z += 0.02;
            this.cube.rotation.x += 0.01;
            this.cube.rotation.y += 0.01;
            this.sphere.rotation.y += 0.008;

            // 渲染场景
            this.renderer.render({
                scene: this.scene,
                camera: this.camera,
            });

            requestAnimationFrame(render);
        };

        render();
    }
}

// 使用示例
const canvas = document.getElementById('canvas');
const scene = new WebGLScene(canvas);
```

---

## 🎓 学习总结

通过本教程，您已经掌握了：

1. **WebGL 渲染管线**的完整流程
2. **几何体创建**的两种方式
3. **着色器编程**的基础知识
4. **框架与原生 API**的差异
5. **3D 场景构建**的实践经验

继续深入学习建议：

-   探索更复杂的着色器效果
-   学习光照和材质系统
-   掌握纹理和后处理技术
-   研究性能优化策略

---

_本教程为 WebGL 学习系列的基础篇，更多高级内容请参考其他专题教程。_
