# WebGL 数字类型字节大小详解

## 为什么数字有不同的字节大小？

### 基本原理

计算机使用二进制（0 和 1）存储数据。字节大小决定了能存储的二进制位数，从而决定数值范围。

```
1字节 = 8位 = 2^8 = 256种可能的值
2字节 = 16位 = 2^16 = 65,536种可能的值
4字节 = 32位 = 2^32 = 4,294,967,296种可能的值
```

## 详细对比分析

### 1 字节类型（8 位）

```javascript
// Uint8Array - 无符号8位整数
const colors = new Uint8Array([255, 128, 64, 255]); // RGBA颜色
// 范围：0-255，完美适配RGB颜色分量

// Int8Array - 有符号8位整数
const normals = new Int8Array([-127, 0, 127]); // 压缩法线
// 范围：-128到127，需要1位表示正负号
```

**优势：**

-   内存占用最小
-   GPU 传输速度最快
-   适合大量重复数据

**适用场景：**

-   颜色数据（0-255）
-   压缩法线向量
-   小范围索引

### 2 字节类型（16 位）

```javascript
// Uint16Array - 无符号16位整数
const indices = new Uint16Array([0, 1, 2, 3, 4, 5]); // 模型索引
// 范围：0-65,535，可索引65K个顶点

// Int16Array - 有符号16位整数
const positions = new Int16Array([-32000, 15000, -8000]); // 压缩位置
// 范围：-32,768到32,767
```

**优势：**

-   平衡内存和精度
-   适合中等规模数据
-   仍然相对高效

**适用场景：**

-   中型模型索引
-   压缩位置数据
-   音频采样

### 4 字节类型（32 位）

#### 整数类型

```javascript
// Uint32Array - 无符号32位整数
const largeIndices = new Uint32Array([0, 100000, 200000]); // 大模型索引
// 范围：0到4,294,967,295，可索引400万个顶点

// Int32Array - 有符号32位整数
const ids = new Int32Array([-1000000, 0, 1000000]); // 特殊ID
// 范围：-2,147,483,648到2,147,483,647
```

#### 浮点类型

```javascript
// Float32Array - 32位浮点数
const positions = new Float32Array([1.23456, -2.78901, 0.12345]);
// 可表示小数，精度约7位有效数字
```

**优势：**

-   最大数值范围
-   浮点数支持小数
-   标准精度

**适用场景：**

-   大型模型索引
-   精确位置坐标
-   法线向量
-   UV 纹理坐标

## 内存效率对比

### 存储 1000 个顶点的内存占用

```javascript
// 位置数据（x,y,z）
const positions_float32 = new Float32Array(3000); // 12KB (4字节×3000)
const positions_int16 = new Int16Array(3000); // 6KB  (2字节×3000)
const positions_int8 = new Int8Array(3000); // 3KB  (1字节×3000)

// 颜色数据（r,g,b,a）
const colors_float32 = new Float32Array(4000); // 16KB (4字节×4000)
const colors_uint8 = new Uint8Array(4000); // 4KB  (1字节×4000)
```

**内存节省：**

-   颜色用 Uint8Array 比 Float32Array 节省 75%内存
-   压缩位置用 Int16Array 比 Float32Array 节省 50%内存

## 选择建议

### 根据数据特性选择

1. **颜色数据** → `Uint8Array` (0-255 范围完美匹配)
2. **大模型索引** → `Uint32Array` (支持 400 万+顶点)
3. **精确位置** → `Float32Array` (需要小数精度)
4. **压缩法线** → `Int8Array` (归一化后范围-1 到 1)

### 性能考虑

```javascript
// ❌ 错误：过度使用高精度类型
const colors = new Float32Array([1.0, 0.5, 0.25, 1.0]); // 浪费内存

// ✅ 正确：根据需求选择合适类型
const colors = new Uint8Array([255, 128, 64, 255]); // 内存高效
```

## WebGL 中的实际应用

### 顶点缓冲区优化

```javascript
// 混合使用不同类型优化内存
const vertexData = {
    positions: new Float32Array([...]),    // 需要精度
    normals: new Int8Array([...]),         // 可以压缩
    colors: new Uint8Array([...]),         // 0-255范围
    indices: new Uint16Array([...])        // 中型模型
};

// 在着色器中正确处理
gl.vertexAttribPointer(positionLoc, 3, gl.FLOAT, false, 0, 0);
gl.vertexAttribPointer(normalLoc, 3, gl.BYTE, true, 0, 0);    // 归一化
gl.vertexAttribPointer(colorLoc, 4, gl.UNSIGNED_BYTE, true, 0, 0); // 归一化
```

## CPU vs GPU 内存存储

### 字节大小在 CPU 和 GPU 中是一致的

```javascript
// 1. 数据创建（CPU内存/RAM）
const positions = new Float32Array([1.0, 2.0, 3.0]); // CPU: 12字节
const colors = new Uint8Array([255, 128, 64]); // CPU: 3字节

// 2. 传输到GPU（显存/VRAM）
const buffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW); // CPU → GPU

// 3. GPU中的存储大小保持不变
// Float32Array 在GPU中仍然是4字节/元素
// Uint8Array 在GPU中仍然是1字节/元素
```

### 重要概念澄清

-   **数据类型的字节大小是固定的**，不会因存储位置（CPU/GPU）而改变
-   `Float32Array` 无论在 CPU 还是 GPU 都是 4 字节/元素
-   `Uint8Array` 无论在 CPU 还是 GPU 都是 1 字节/元素

### 为什么字节大小很重要？

#### 1. 内存带宽影响传输速度

```javascript
// 传输1000个顶点位置数据的对比
const positions_float = new Float32Array(3000); // 传输12KB
const positions_byte = new Uint8Array(3000); // 传输3KB（快4倍）
```

#### 2. GPU 显存占用

```javascript
// 大型场景中，类型选择直接影响显存使用
const bigModel_float = new Float32Array(1000000); // 占用4MB显存
const bigModel_short = new Uint16Array(1000000); // 占用2MB显存
```

#### 3. GPU 处理效率

-   较小的数据类型 = 更高的缓存命中率
-   更多数据可以同时在 GPU 核心中处理

## 总结

不同字节大小的数字类型是为了在**内存效率**和**数值精度**之间找到最佳平衡：

-   **1 字节**：最节省内存，适合简单数据
-   **2 字节**：平衡选择，适合中等精度需求
-   **4 字节**：最高精度，适合复杂计算

**关键理解：**

-   字节大小在 CPU 和 GPU 中保持一致
-   选择合适的类型影响传输速度、显存占用和 GPU 处理效率
-   数据从 CPU 传输到 GPU 时，类型信息必须正确匹配
