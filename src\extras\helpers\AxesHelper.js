import { Mesh } from '../../core/Mesh.js';
import { Program } from '../../core/Program.js';
import { Geometry } from '../../core/Geometry.js';
import { Vec3 } from '../../math/Vec3.js';

/**
 * 坐标轴辅助类
 * 用于可视化显示3D空间中的X、Y、Z轴
 */
export class AxesHelper extends Mesh {
    /**
     * 创建一个坐标轴辅助对象
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} [options] - 配置选项
     * @param {Number} [options.size=1] - 坐标轴长度
     * @param {Boolean} [options.symmetric=false] - 是否对称显示（轴的正负方向）
     * @param {Vec3} [options.xColor=new Vec3(0.96, 0.21, 0.32)] - X轴颜色（默认红色）
     * @param {Vec3} [options.yColor=new Vec3(0.44, 0.64, 0.11)] - Y轴颜色（默认绿色）
     * @param {Vec3} [options.zColor=new Vec3(0.18, 0.52, 0.89)] - Z轴颜色（默认蓝色）
     * @param {Object} [meshProps] - 其他Mesh属性
     */
    constructor(
        gl,
        {
            size = 1,
            symmetric = false,
            xColor = new Vec3(0.96, 0.21, 0.32), // 红色
            yColor = new Vec3(0.44, 0.64, 0.11), // 绿色
            zColor = new Vec3(0.18, 0.52, 0.89), // 蓝色
            ...meshProps
        } = {}
    ) {
        // 确定坐标轴的起点和终点
        const a = symmetric ? -size : 0; // 如果对称，则起点为-size，否则为0
        const b = size; // 终点始终为size

        // 定义顶点位置
        // 每个轴由两个点组成：起点和终点
        // prettier-ignore
        const vertices = new Float32Array([
			a, 0, 0,  b, 0, 0,  // X轴：从(a,0,0)到(b,0,0)
			0, a, 0,  0, b, 0,  // Y轴：从(0,a,0)到(0,b,0)
			0, 0, a,  0, 0, b   // Z轴：从(0,0,a)到(0,0,b)
		]);

        // 定义每个顶点的颜色
        // prettier-ignore
        const colors = new Float32Array([
			...xColor,  ...xColor,  // X轴颜色
			...yColor,  ...yColor,  // Y轴颜色
			...zColor,  ...zColor   // Z轴颜色
		]);

        // 创建几何体
        const geometry = new Geometry(gl, {
            position: { size: 3, data: vertices },
            color: { size: 3, data: colors },
        });

        // 创建着色器程序
        const program = new Program(gl, { vertex, fragment });

        // 调用父类构造函数，使用线段模式渲染
        super(gl, { ...meshProps, mode: gl.LINES, geometry, program });
    }
}

/**
 * 顶点着色器
 * 将顶点位置从模型空间转换到裁剪空间，并传递颜色到片段着色器
 */
const vertex = /* glsl */ `
attribute vec3 position;  // 顶点位置
attribute vec3 color;     // 顶点颜色
uniform mat4 modelViewMatrix;    // 模型视图矩阵
uniform mat4 projectionMatrix;   // 投影矩阵

varying vec3 vColor;      // 传递给片段着色器的颜色

void main() {
    vColor = color;  // 传递颜色
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);  // 计算裁剪空间位置
}
`;

/**
 * 片段着色器
 * 使用从顶点着色器传递的颜色为每个片段着色
 */
const fragment = /* glsl */ `
precision highp float;    // 高精度浮点数
varying vec3 vColor;      // 从顶点着色器接收的颜色

void main() {
    gl_FragColor = vec4(vColor, 1.0);  // 输出RGBA颜色，不透明
}
`;
