# WebGL面剔除与GPU渲染管线学习笔记

## 核心问题
- `this.worldMatrix.determinant() < 0` 的作用是什么？
- WebGL如何判断顶点顺序并进行面剔除？
- 叉积计算顶点顺序至少需要几个点？
- 着色器逐个处理顶点，如何实现需要3个顶点的面剔除？

## 1. 行列式与镜像变换检测

### 行列式的几何意义
- **determinant > 0**：保持坐标系"手性"（右手坐标系仍为右手坐标系）
- **determinant < 0**：改变坐标系"手性"（右手坐标系变为左手坐标系）
- **determinant = 0**：退化变换（3D空间压缩到更低维度）

### 实际应用场景
```javascript
// 正常情况：determinant = 1 (正值)
mesh.scale.set(1, 1, 1);
// 三角形保持逆时针绕序，正常渲染

// 镜像情况：determinant = -1 (负值)  
mesh.scale.set(-1, 1, 1); // X轴镜像
// 三角形变为顺时针绕序，需要翻转面剔除设置
```

### 三角形顶点绕序变化示例
```
原始状态 (scale: 1, 1, 1)
     Y
     ↑
  A  |  B     坐标：A(-1, 1)  B(1, 1)  C(0, -1)
  ●--+--●     顶点绕序：A → B → C (逆时针 ↺)
   \ | /      正面朝向观察者 👁️
    \|/       determinant = 1 > 0
     ●        
     C        
─────┼─────→ X

X轴镜像后 (scale: -1, 1, 1)
     Y
     ↑
  B' |  A'    坐标：A'(1, 1)  B'(-1, 1)  C'(0, -1)
  ●--+--●     顶点绕序：A' → B' → C' (顺时针 ↻)
   \ | /      背面朝向观察者 👁️
    \|/       determinant = -1 < 0
     ●        
     C'       
─────┼─────→ X
```

## 2. WebGL面剔除原理

### 顶点顺序判断 - 叉积计算
```javascript
// WebGL内部的绕序判断（伪代码）
function getWindingOrder(A, B, C) {
    // 计算两个边向量
    let AB = { x: B.x - A.x, y: B.y - A.y };
    let AC = { x: C.x - A.x, y: C.y - A.y };
    
    // 2D叉积 (只有Z分量)
    let crossProduct = AB.x * AC.y - AB.y * AC.x;
    
    if (crossProduct > 0) {
        return "COUNTER_CLOCKWISE";  // 逆时针
    } else if (crossProduct < 0) {
        return "CLOCKWISE";          // 顺时针
    } else {
        return "DEGENERATE";         // 退化三角形
    }
}
```

### WebGL面剔除设置
```javascript
// 完整的面剔除设置
gl.enable(gl.CULL_FACE);        // 启用面剔除
gl.cullFace(gl.BACK);           // 剔除背面 (默认)
gl.frontFace(gl.CCW);           // 逆时针为正面 (默认)

// 镜像对象的处理
if (determinant < 0) {
    gl.cullFace(gl.FRONT);      // 临时剔除正面
} else {
    gl.cullFace(gl.BACK);       // 正常剔除背面
}
```

## 3. 叉积计算的最少点数要求

**答案：至少需要3个点**

### 数学原理
```
要确定平面上的旋转方向，需要：
• 2个向量
• 每个向量需要2个点来定义
• 总共需要3个不同的点

点A ──→ 点B  (向量AB)
点A ──→ 点C  (向量AC)
```

### 可视化说明
```
只有2个点：无法确定方向
A ●────● B
   无法判断旋转方向

有3个点：可以确定方向
    C
    ●
   /|
  / |
 /  |
A●──● B

向量AB × 向量AC = 叉积
叉积 > 0 → 逆时针 ↺
叉积 < 0 → 顺时针 ↻
```

## 4. GPU渲染管线与面剔除

### 关键理解：面剔除不在着色器中进行
面剔除是在GPU硬件的**固定功能阶段**进行的，不是在可编程的着色器中。

### GPU渲染管线流程
```
顶点着色器 → 图元装配 → 面剔除 → 光栅化 → 片段着色器
(逐顶点)   (组装三角形) (硬件处理) (生成像素) (逐像素)
    ↓           ↓         ↓         ↓         ↓
处理单个顶点   收集3个顶点   计算叉积   确定像素   处理单个像素
```

### 详细阶段说明

#### 1. 顶点着色器阶段 (可编程)
```glsl
// 每次只处理一个顶点
attribute vec3 position;
uniform mat4 modelViewMatrix;
uniform mat4 projectionMatrix;

void main() {
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
```

#### 2. 图元装配阶段 (固定功能)
- 收集3个顶点组成一个三角形
- 执行裁剪操作
- 进行透视除法
- 执行视口变换

#### 3. 面剔除阶段 (固定功能)
- 已经有了3个顶点的屏幕坐标
- 计算2D叉积判断绕序
- 根据gl.cullFace()设置决定是否剔除
- 在硬件中自动完成

#### 4. 光栅化阶段 (固定功能)
- 确定三角形覆盖哪些像素
- 为每个像素生成片段
- 插值顶点属性

#### 5. 片段着色器阶段 (可编程)
```glsl
// 每次只处理一个像素
precision mediump float;
varying vec3 vColor;

void main() {
    gl_FragColor = vec4(vColor, 1.0);
}
```

## 5. 性能优化意义

### 面剔除的性能收益
```
没有面剔除：渲染所有三角形 → 浪费50%性能
启用面剔除：只渲染可见面   → 提升50%性能

封闭物体的背面永远不会被看到，剔除它们可以：
• 减少片段着色器调用
• 减少纹理采样  
• 提高渲染效率
```

## 6. 关键要点总结

1. **行列式检测**：`determinant() < 0` 检测镜像变换
2. **最少点数**：叉积计算至少需要3个点
3. **硬件处理**：面剔除在GPU固定功能阶段自动进行
4. **着色器分工**：顶点着色器逐个处理，面剔除在中间阶段批量处理
5. **性能优化**：对封闭物体可提升约50%的渲染性能

## 7. 实际代码应用

```javascript
// 在Mesh.js的draw方法中
draw({ camera } = {}) {
    // ... 矩阵设置 ...
    
    // 检测镜像变换
    let flipFaces = this.program.cullFace && this.worldMatrix.determinant() < 0;
    
    // 使用着色器程序（会设置面剔除状态）
    this.program.use({ flipFaces });
    
    // 绘制几何体
    this.geometry.draw({ mode: this.mode, program: this.program });
}
```

这样确保了无论对象是否被镜像，都能正确显示而不会出现"消失"或"内外翻转"的问题！
