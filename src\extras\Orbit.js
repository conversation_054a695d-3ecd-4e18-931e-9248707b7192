// 基于ThreeJS的OrbitControls类，使用ES6重写，并进行了一些增减修改
// TODO: 抽象事件处理器，使其可以从其他来源获取输入
// TODO: 使滚轮缩放比简单的 >/< 零判断更精确
// TODO: 能够传入新的相机位置

import { Vec3 } from '../math/Vec3.js';
import { Vec2 } from '../math/Vec2.js';

/**
 * 轨道控制器状态枚举
 * NONE: 无操作状态
 * ROTATE: 旋转状态
 * DOLLY: 缩放状态
 * PAN: 平移状态
 * DOLLY_PAN: 同时缩放和平移状态（触摸设备上的多指操作）
 */
const STATE = { NONE: -1, ROTATE: 0, DOLLY: 1, PAN: 2, DOLLY_PAN: 3 };

// 预先创建临时变量以避免重复创建对象
const tempVec3 = /* @__PURE__ */ new Vec3();
const tempVec2a = /* @__PURE__ */ new Vec2();
const tempVec2b = /* @__PURE__ */ new Vec2();

/**
 * 轨道控制器
 * 允许用户通过鼠标或触摸操作来旋转、缩放和平移相机
 *
 * @param {Object} object - 要控制的对象（通常是相机）
 * @param {Object} options - 配置选项
 */
export function Orbit(
    object,
    {
        element = document, // 监听事件的DOM元素
        enabled = true, // 是否启用控制器
        target = new Vec3(), // 相机围绕旋转的目标点
        ease = 0.25, // 缓动系数，控制相机移动的平滑度
        inertia = 0.85, // 惯性系数，控制相机停止后的滑动效果
        enableRotate = true, // 是否启用旋转
        rotateSpeed = 0.1, // 旋转速度
        autoRotate = false, // 是否自动旋转
        autoRotateSpeed = 1.0, // 自动旋转速度
        enableZoom = true, // 是否启用缩放
        zoomSpeed = 1, // 缩放速度
        zoomStyle = 'dolly', // 缩放方式：'dolly'（移动相机）或改变FOV
        enablePan = true, // 是否启用平移
        panSpeed = 0.1, // 平移速度
        minPolarAngle = 0, // 最小极角（垂直旋转限制）
        maxPolarAngle = Math.PI, // 最大极角
        minAzimuthAngle = -Infinity, // 最小方位角（水平旋转限制）
        maxAzimuthAngle = Infinity, // 最大方位角
        minDistance = 0, // 最小距离（缩放限制）
        maxDistance = Infinity, // 最大距离
    } = {}
) {
    this.enabled = enabled;
    this.target = target;
    this.zoomStyle = zoomStyle;

    // 处理禁用尝试 - 设为1使其无效果
    ease = ease || 1;
    inertia = inertia || 0;

    this.minDistance = minDistance;
    this.maxDistance = maxDistance;

    // 球坐标系中的当前位置和目标位置
    const sphericalDelta = { radius: 1, phi: 0, theta: 0 }; // 球坐标增量
    const sphericalTarget = { radius: 1, phi: 0, theta: 0 }; // 目标球坐标
    const spherical = { radius: 1, phi: 0, theta: 0 }; // 当前球坐标
    const panDelta = new Vec3(); // 平移增量

    // 获取初始位置值
    const offset = new Vec3();
    offset.copy(object.position).sub(this.target);
    // 计算初始球坐标
    spherical.radius = sphericalTarget.radius = offset.distance();
    spherical.theta = sphericalTarget.theta = Math.atan2(offset.x, offset.z);
    spherical.phi = sphericalTarget.phi = Math.acos(Math.min(Math.max(offset.y / sphericalTarget.radius, -1), 1));

    this.offset = offset;

    /**
     * 更新相机位置和旋转
     * 根据当前的增量值计算新的相机位置
     */
    this.update = () => {
        // 如果启用了自动旋转，则处理自动旋转
        if (autoRotate) {
            handleAutoRotate();
        }

        // 应用增量到目标球坐标
        sphericalTarget.radius *= sphericalDelta.radius;
        sphericalTarget.theta += sphericalDelta.theta;
        sphericalTarget.phi += sphericalDelta.phi;

        // 应用边界限制
        sphericalTarget.theta = Math.max(minAzimuthAngle, Math.min(maxAzimuthAngle, sphericalTarget.theta));
        sphericalTarget.phi = Math.max(minPolarAngle, Math.min(maxPolarAngle, sphericalTarget.phi));
        sphericalTarget.radius = Math.max(this.minDistance, Math.min(this.maxDistance, sphericalTarget.radius));

        // 应用缓动效果
        spherical.phi += (sphericalTarget.phi - spherical.phi) * ease;
        spherical.theta += (sphericalTarget.theta - spherical.theta) * ease;
        spherical.radius += (sphericalTarget.radius - spherical.radius) * ease;

        // 应用平移到目标点。由于偏移量是相对于目标的，它也会移动
        this.target.add(panDelta);

        // 应用旋转到偏移量（将球坐标转换为笛卡尔坐标）
        let sinPhiRadius = spherical.radius * Math.sin(Math.max(0.000001, spherical.phi));
        offset.x = sinPhiRadius * Math.sin(spherical.theta);
        offset.y = spherical.radius * Math.cos(spherical.phi);
        offset.z = sinPhiRadius * Math.cos(spherical.theta);

        // 将更新后的值应用到对象
        object.position.copy(this.target).add(offset);
        object.lookAt(this.target);

        // 应用惯性到增量值
        sphericalDelta.theta *= inertia;
        sphericalDelta.phi *= inertia;
        panDelta.multiply(inertia);

        // 每帧重置缩放，避免多次应用缩放
        sphericalDelta.radius = 1;
    };

    /**
     * 强制更新内部状态以匹配当前位置
     * 用于在外部修改相机位置后同步控制器状态
     */
    this.forcePosition = () => {
        offset.copy(object.position).sub(this.target);
        spherical.radius = sphericalTarget.radius = offset.distance();
        spherical.theta = sphericalTarget.theta = Math.atan2(offset.x, offset.z);
        spherical.phi = sphericalTarget.phi = Math.acos(Math.min(Math.max(offset.y / sphericalTarget.radius, -1), 1));
        object.lookAt(this.target);
    };

    // 以下代码用于更新panDelta和sphericalDelta
    // 使用这两个对象的值来计算轨道

    // 用于跟踪交互起始点的向量
    const rotateStart = new Vec2(); // 旋转起始点
    const panStart = new Vec2(); // 平移起始点
    const dollyStart = new Vec2(); // 缩放起始点

    // 当前控制器状态
    let state = STATE.NONE;
    // 鼠标按钮映射
    this.mouseButtons = { ORBIT: 0, ZOOM: 1, PAN: 2 };

    /**
     * 获取缩放比例
     * @returns {Number} 缩放比例因子
     */
    function getZoomScale() {
        return Math.pow(0.95, zoomSpeed);
    }

    /**
     * 向左平移相机
     * @param {Number} distance - 平移距离
     * @param {Array} m - 对象的变换矩阵
     */
    function panLeft(distance, m) {
        tempVec3.set(m[0], m[1], m[2]); // 获取矩阵的第一列（右向量）
        tempVec3.multiply(-distance); // 向左移动（负方向）
        panDelta.add(tempVec3); // 添加到平移增量
    }

    /**
     * 向上平移相机
     * @param {Number} distance - 平移距离
     * @param {Array} m - 对象的变换矩阵
     */
    function panUp(distance, m) {
        tempVec3.set(m[4], m[5], m[6]); // 获取矩阵的第二列（上向量）
        tempVec3.multiply(distance); // 向上移动（正方向）
        panDelta.add(tempVec3); // 添加到平移增量
    }

    /**
     * 平移相机
     * @param {Number} deltaX - X轴平移量
     * @param {Number} deltaY - Y轴平移量
     */
    const pan = (deltaX, deltaY) => {
        let el = element === document ? document.body : element;
        // 计算相机到目标的距离
        tempVec3.copy(object.position).sub(this.target);
        let targetDistance = tempVec3.distance();
        // 根据FOV调整平移距离
        targetDistance *= Math.tan((((object.fov || 45) / 2) * Math.PI) / 180.0);
        // 执行平移
        panLeft((2 * deltaX * targetDistance) / el.clientHeight, object.matrix);
        panUp((2 * deltaY * targetDistance) / el.clientHeight, object.matrix);
    };

    /**
     * 缩放相机
     * @param {Number} dollyScale - 缩放比例
     */
    const dolly = (dollyScale) => {
        if (this.zoomStyle === 'dolly') {
            // 通过改变相机距离来缩放
            sphericalDelta.radius /= dollyScale;
        } else {
            // 通过改变FOV来缩放
            object.fov /= dollyScale;
            // 更新投影矩阵
            if (object.type === 'orthographic') object.orthographic();
            else object.perspective();
        }
    };

    /**
     * 处理自动旋转
     */
    function handleAutoRotate() {
        // 计算每帧的旋转角度
        const angle = ((2 * Math.PI) / 60 / 60) * autoRotateSpeed;
        sphericalDelta.theta -= angle;
    }

    /**
     * 处理旋转移动
     * @param {Number} x - 鼠标X坐标
     * @param {Number} y - 鼠标Y坐标
     */
    function handleMoveRotate(x, y) {
        tempVec2a.set(x, y);
        // 计算鼠标移动距离并应用旋转速度
        tempVec2b.sub(tempVec2a, rotateStart).multiply(rotateSpeed);
        let el = element === document ? document.body : element;
        // 更新球坐标增量
        sphericalDelta.theta -= (2 * Math.PI * tempVec2b.x) / el.clientHeight;
        sphericalDelta.phi -= (2 * Math.PI * tempVec2b.y) / el.clientHeight;
        // 更新旋转起始点
        rotateStart.copy(tempVec2a);
    }

    /**
     * 处理鼠标缩放移动
     * @param {MouseEvent} e - 鼠标事件
     */
    function handleMouseMoveDolly(e) {
        tempVec2a.set(e.clientX, e.clientY);
        tempVec2b.sub(tempVec2a, dollyStart);
        // 根据鼠标Y轴移动方向决定缩放方向
        if (tempVec2b.y > 0) {
            dolly(getZoomScale()); // 放大
        } else if (tempVec2b.y < 0) {
            dolly(1 / getZoomScale()); // 缩小
        }
        dollyStart.copy(tempVec2a);
    }

    /**
     * 处理平移移动
     * @param {Number} x - 鼠标X坐标
     * @param {Number} y - 鼠标Y坐标
     */
    function handleMovePan(x, y) {
        tempVec2a.set(x, y);
        // 计算鼠标移动距离并应用平移速度
        tempVec2b.sub(tempVec2a, panStart).multiply(panSpeed);
        // 执行平移
        pan(tempVec2b.x, tempVec2b.y);
        // 更新平移起始点
        panStart.copy(tempVec2a);
    }

    /**
     * 处理触摸开始时的缩放和平移
     * @param {TouchEvent} e - 触摸事件
     */
    function handleTouchStartDollyPan(e) {
        if (enableZoom) {
            // 计算两个触摸点之间的距离
            let dx = e.touches[0].pageX - e.touches[1].pageX;
            let dy = e.touches[0].pageY - e.touches[1].pageY;
            let distance = Math.sqrt(dx * dx + dy * dy);
            dollyStart.set(0, distance);
        }

        if (enablePan) {
            // 计算两个触摸点的中心点
            let x = 0.5 * (e.touches[0].pageX + e.touches[1].pageX);
            let y = 0.5 * (e.touches[0].pageY + e.touches[1].pageY);
            panStart.set(x, y);
        }
    }

    /**
     * 处理触摸移动时的缩放和平移
     * @param {TouchEvent} e - 触摸事件
     */
    function handleTouchMoveDollyPan(e) {
        if (enableZoom) {
            // 计算两个触摸点之间的新距离
            let dx = e.touches[0].pageX - e.touches[1].pageX;
            let dy = e.touches[0].pageY - e.touches[1].pageY;
            let distance = Math.sqrt(dx * dx + dy * dy);
            tempVec2a.set(0, distance);
            // 根据距离变化计算缩放比例
            tempVec2b.set(0, Math.pow(tempVec2a.y / dollyStart.y, zoomSpeed));
            dolly(tempVec2b.y);
            dollyStart.copy(tempVec2a);
        }

        if (enablePan) {
            // 计算两个触摸点的新中心点并执行平移
            let x = 0.5 * (e.touches[0].pageX + e.touches[1].pageX);
            let y = 0.5 * (e.touches[0].pageY + e.touches[1].pageY);
            handleMovePan(x, y);
        }
    }

    /**
     * 鼠标按下事件处理
     * @param {MouseEvent} e - 鼠标事件
     */
    const onMouseDown = (e) => {
        if (!this.enabled) return;

        // 根据按下的鼠标按钮确定操作类型
        switch (e.button) {
            case this.mouseButtons.ORBIT:
                if (enableRotate === false) return;
                rotateStart.set(e.clientX, e.clientY);
                state = STATE.ROTATE;
                break;
            case this.mouseButtons.ZOOM:
                if (enableZoom === false) return;
                dollyStart.set(e.clientX, e.clientY);
                state = STATE.DOLLY;
                break;
            case this.mouseButtons.PAN:
                if (enablePan === false) return;
                panStart.set(e.clientX, e.clientY);
                state = STATE.PAN;
                break;
        }

        // 如果有操作，添加鼠标移动和抬起事件监听
        if (state !== STATE.NONE) {
            window.addEventListener('mousemove', onMouseMove, false);
            window.addEventListener('mouseup', onMouseUp, false);
        }
    };

    /**
     * 鼠标移动事件处理
     * @param {MouseEvent} e - 鼠标事件
     */
    const onMouseMove = (e) => {
        if (!this.enabled) return;

        // 根据当前状态执行相应的操作
        switch (state) {
            case STATE.ROTATE:
                if (enableRotate === false) return;
                handleMoveRotate(e.clientX, e.clientY);
                break;
            case STATE.DOLLY:
                if (enableZoom === false) return;
                handleMouseMoveDolly(e);
                break;
            case STATE.PAN:
                if (enablePan === false) return;
                handleMovePan(e.clientX, e.clientY);
                break;
        }
    };

    /**
     * 鼠标抬起事件处理
     */
    const onMouseUp = () => {
        // 移除临时事件监听
        window.removeEventListener('mousemove', onMouseMove, false);
        window.removeEventListener('mouseup', onMouseUp, false);
        // 重置状态
        state = STATE.NONE;
    };

    /**
     * 鼠标滚轮事件处理
     * @param {WheelEvent} e - 滚轮事件
     */
    const onMouseWheel = (e) => {
        // 只有在启用缩放且处于无操作或旋转状态时才处理滚轮事件
        if (!this.enabled || !enableZoom || (state !== STATE.NONE && state !== STATE.ROTATE)) return;
        e.stopPropagation();
        e.preventDefault();

        // 根据滚轮方向执行缩放
        if (e.deltaY < 0) {
            dolly(1 / getZoomScale()); // 放大
        } else if (e.deltaY > 0) {
            dolly(getZoomScale()); // 缩小
        }
    };

    /**
     * 触摸开始事件处理
     * @param {TouchEvent} e - 触摸事件
     */
    const onTouchStart = (e) => {
        if (!this.enabled) return;
        e.preventDefault();

        // 根据触摸点数量确定操作类型
        switch (e.touches.length) {
            case 1: // 单指触摸 - 旋转
                if (enableRotate === false) return;
                rotateStart.set(e.touches[0].pageX, e.touches[0].pageY);
                state = STATE.ROTATE;
                break;
            case 2: // 双指触摸 - 缩放和平移
                if (enableZoom === false && enablePan === false) return;
                handleTouchStartDollyPan(e);
                state = STATE.DOLLY_PAN;
                break;
            default:
                state = STATE.NONE;
        }
    };

    /**
     * 触摸移动事件处理
     * @param {TouchEvent} e - 触摸事件
     */
    const onTouchMove = (e) => {
        if (!this.enabled) return;
        e.preventDefault();
        e.stopPropagation();

        // 根据触摸点数量执行相应的操作
        switch (e.touches.length) {
            case 1: // 单指移动 - 旋转
                if (enableRotate === false) return;
                handleMoveRotate(e.touches[0].pageX, e.touches[0].pageY);
                break;
            case 2: // 双指移动 - 缩放和平移
                if (enableZoom === false && enablePan === false) return;
                handleTouchMoveDollyPan(e);
                break;
            default:
                state = STATE.NONE;
        }
    };

    /**
     * 触摸结束事件处理
     */
    const onTouchEnd = () => {
        if (!this.enabled) return;
        state = STATE.NONE;
    };

    /**
     * 上下文菜单事件处理（阻止默认行为）
     * @param {Event} e - 事件对象
     */
    const onContextMenu = (e) => {
        if (!this.enabled) return;
        e.preventDefault();
    };

    /**
     * 添加所有事件处理器
     */
    function addHandlers() {
        element.addEventListener('contextmenu', onContextMenu, false);
        element.addEventListener('mousedown', onMouseDown, false);
        element.addEventListener('wheel', onMouseWheel, { passive: false });
        element.addEventListener('touchstart', onTouchStart, { passive: false });
        element.addEventListener('touchend', onTouchEnd, false);
        element.addEventListener('touchmove', onTouchMove, { passive: false });
    }

    /**
     * 移除所有事件处理器
     * 在不再需要控制器时调用此方法以清理事件监听
     */
    this.remove = function () {
        element.removeEventListener('contextmenu', onContextMenu);
        element.removeEventListener('mousedown', onMouseDown);
        element.removeEventListener('wheel', onMouseWheel);
        element.removeEventListener('touchstart', onTouchStart);
        element.removeEventListener('touchend', onTouchEnd);
        element.removeEventListener('touchmove', onTouchMove);
        window.removeEventListener('mousemove', onMouseMove);
        window.removeEventListener('mouseup', onMouseUp);
    };

    // 初始化时添加事件处理器
    addHandlers();
}
