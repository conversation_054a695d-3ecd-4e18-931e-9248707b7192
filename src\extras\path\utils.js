// 来源: https://github.com/Pomax/bezierjs/blob/d19695f3cc3ce383cf38ce4643f467deca7edb92/src/utils.js#L26
// 勒让德-高斯积分点，n=24
// （x_i值，定义为n阶勒让德多项式Pn(x)的根）
// 用于高斯-勒让德积分法计算曲线长度
export const T_VALUES = [
    -0.0640568928626056260850430826247450385909, 0.0640568928626056260850430826247450385909, -0.1911188674736163091586398207570696318404, 0.1911188674736163091586398207570696318404,
    -0.3150426796961633743867932913198102407864, 0.3150426796961633743867932913198102407864, -0.4337935076260451384870842319133497124524, 0.4337935076260451384870842319133497124524,
    -0.5454214713888395356583756172183723700107, 0.5454214713888395356583756172183723700107, -0.6480936519369755692524957869107476266696, 0.6480936519369755692524957869107476266696,
    -0.7401241915785543642438281030999784255232, 0.7401241915785543642438281030999784255232, -0.8200019859739029219539498726697452080761, 0.8200019859739029219539498726697452080761,
    -0.8864155270044010342131543419821967550873, 0.8864155270044010342131543419821967550873, -0.9382745520027327585236490017087214496548, 0.9382745520027327585236490017087214496548,
    -0.9747285559713094981983919930081690617411, 0.9747285559713094981983919930081690617411, -0.9951872199970213601799974097007368118745, 0.9951872199970213601799974097007368118745,
];

// 勒让德-高斯权重，n=24
// （w_i值，由贝塞尔入门文章中链接的函数定义）
// 用于高斯-勒让德积分法计算曲线长度
export const C_VALUES = [
    0.1279381953467521569740561652246953718517, 0.1279381953467521569740561652246953718517, 0.1258374563468282961213753825111836887264, 0.1258374563468282961213753825111836887264,
    0.121670472927803391204463153476262425607, 0.121670472927803391204463153476262425607, 0.1155056680537256013533444839067835598622, 0.1155056680537256013533444839067835598622,
    0.1074442701159656347825773424466062227946, 0.1074442701159656347825773424466062227946, 0.0976186521041138882698806644642471544279, 0.0976186521041138882698806644642471544279,
    0.086190161531953275917185202983742667185, 0.086190161531953275917185202983742667185, 0.0733464814110803057340336152531165181193, 0.0733464814110803057340336152531165181193,
    0.0592985849154367807463677585001085845412, 0.0592985849154367807463677585001085845412, 0.0442774388174198061686027482113382288593, 0.0442774388174198061686027482113382288593,
    0.0285313886289336631813078159518782864491, 0.0285313886289336631813078159518782864491, 0.0123412297999871995468056670700372915759, 0.0123412297999871995468056670700372915759,
];

/**
 * 将角度转换为弧度
 * @param {number} a - 角度值（度）
 * @returns {number} 弧度值
 */
export const toRadian = (a) => (a * Math.PI) / 180;

/**
 * 将弧度转换为角度
 * @param {number} a - 弧度值
 * @returns {number} 角度值（度）
 */
export const toDegrees = (a) => (180 * a) / Math.PI;

/**
 * 将值限制在指定范围内
 * @param {number} val - 输入值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 限制后的值
 */
export const clamp = (val, min, max) => Math.max(min, Math.min(val, max));

/**
 * 线性插值
 * @param {number} t - 插值参数，范围[0,1]
 * @param {number} v0 - 起始值
 * @param {number} v1 - 结束值
 * @returns {number} 插值结果
 */
export const lerp = (t, v0, v1) => v0 * (1 - t) + v1 * t;

/**
 * 使用给定角度的正弦和余弦值围绕给定轴填充旋转矩阵
 * 此函数有助于避免反三角函数计算
 * @param {Mat4} out - 接收操作结果的4x4矩阵
 * @param {Vec3} axis - 旋转轴，应该是归一化的
 * @param {number} sin - 旋转角度的正弦值
 * @param {number} cos - 旋转角度的余弦值
 * @returns {Mat4} 结果矩阵
 */
export function mat4fromRotationSinCos(out, axis, sin, cos) {
    // 获取旋转轴的分量
    const x = axis[0];
    const y = axis[1];
    const z = axis[2];
    const t = 1 - cos; // 辅助变量

    // 使用罗德里格旋转公式填充旋转矩阵
    // 参考: https://en.wikipedia.org/wiki/Rodrigues%27_rotation_formula
    out[0] = x * x * t + cos; // 第一行第一列
    out[1] = y * x * t + z * sin; // 第一行第二列
    out[2] = z * x * t - y * sin; // 第一行第三列
    out[3] = 0; // 第一行第四列
    out[4] = x * y * t - z * sin; // 第二行第一列
    out[5] = y * y * t + cos; // 第二行第二列
    out[6] = z * y * t + x * sin; // 第二行第三列
    out[7] = 0; // 第二行第四列
    out[8] = x * z * t + y * sin; // 第三行第一列
    out[9] = y * z * t - x * sin; // 第三行第二列
    out[10] = z * z * t + cos; // 第三行第三列
    out[11] = 0; // 第三行第四列
    out[12] = 0; // 第四行第一列
    out[13] = 0; // 第四行第二列
    out[14] = 0; // 第四行第三列
    out[15] = 1; // 第四行第四列
    return out;
}

/**
 * 围绕切线方向旋转法线和副法线向量
 *
 * 参考: https://en.wikipedia.org/wiki/Rodrigues%27_rotation_formula
 * @param {number} angle - 旋转角度（弧度）
 * @param {Vec3} norm - 单位法线向量
 * @param {Vec3} binorm - 单位副法线向量
 * @param {Vec3} [outNorm=norm] - 可选的法线输出向量。如果不提供，则法线向量将在原地修改
 * @param {Vec3} [outBinorm=binorm] - 可选的副法线输出向量。如果不提供，则副法线向量将在原地修改
 */
export function rotateNormalBinormal(angle, norm, binorm, outNorm = norm, outBinorm = binorm) {
    // 计算旋转角度的正弦和余弦值
    const s = Math.sin(angle);
    const c = Math.cos(angle);

    // 计算旋转后的法线向量分量
    const nx = c * norm.x + s * binorm.x;
    const ny = c * norm.y + s * binorm.y;
    const nz = c * norm.z + s * binorm.z;

    // 计算旋转后的副法线向量分量
    const bx = c * binorm.x - s * norm.x;
    const by = c * binorm.y - s * norm.y;
    const bz = c * binorm.z - s * norm.z;

    // 设置输出向量
    outNorm.set(nx, ny, nz);
    outBinorm.set(bx, by, bz);
}
