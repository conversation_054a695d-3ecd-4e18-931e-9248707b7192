// 基于 Three.js 的环面几何体实现
// https://github.com/mrdoob/three.js/blob/master/src/geometries/TorusGeometry.js

import { Geometry } from '../core/Geometry.js';
import { Vec3 } from '../math/Vec3.js';

/**
 * 环面（甜甜圈）几何体类
 */
export class Torus extends Geometry {
    /**
     * 创建一个环面几何体
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} [options] - 配置选项
     * @param {Number} [options.radius=0.5] - 环面的主半径（从环面中心到管道中心的距离）
     * @param {Number} [options.tube=0.2] - 管道半径（管道的粗细）
     * @param {Number} [options.radialSegments=8] - 管道横截面的分段数（围绕管道的分段）
     * @param {Number} [options.tubularSegments=6] - 主环的分段数（沿着环面周长的分段）
     * @param {Number} [options.arc=Math.PI*2] - 环面的角度范围（默认为完整的环）
     * @param {Object} [options.attributes={}] - 自定义几何体属性
     */
    constructor(gl, { radius = 0.5, tube = 0.2, radialSegments = 8, tubularSegments = 6, arc = Math.PI * 2, attributes = {} } = {}) {
        // 计算顶点和索引数量
        const num = (radialSegments + 1) * (tubularSegments + 1); // 顶点总数
        const numIndices = radialSegments * tubularSegments * 6; // 索引总数（每个四边形由2个三角形组成）

        // 创建数据数组
        const vertices = new Float32Array(num * 3); // 顶点位置
        const normals = new Float32Array(num * 3); // 顶点法线
        const uvs = new Float32Array(num * 2); // 纹理坐标
        // 根据顶点数选择合适的索引类型
        const indices = num > 65536 ? new Uint32Array(numIndices) : new Uint16Array(numIndices);

        // 临时向量，用于计算
        const center = new Vec3(); // 环面中心到当前点的投影
        const vertex = new Vec3(); // 当前顶点
        const normal = new Vec3(); // 法线向量

        // 生成顶点、法线和纹理坐标
        let idx = 0;
        for (let j = 0; j <= radialSegments; j++) {
            for (let i = 0; i <= tubularSegments; i++, idx++) {
                // 计算参数坐标 u 和 v
                const u = (i / tubularSegments) * arc; // 沿着环面周长的参数 [0, arc]
                const v = (j / radialSegments) * Math.PI * 2; // 围绕管道的参数 [0, 2π]

                // 计算顶点位置
                // 环面参数方程：
                // x = (R + r·cos(v))·cos(u)
                // y = (R + r·cos(v))·sin(u)
                // z = r·sin(v)
                // 其中 R 是主半径，r 是管道半径，u 是沿环面的角度，v 是围绕管道的角度
                vertex.x = (radius + tube * Math.cos(v)) * Math.cos(u);
                vertex.y = (radius + tube * Math.cos(v)) * Math.sin(u);
                vertex.z = tube * Math.sin(v);

                // 设置顶点位置
                vertices.set([vertex.x, vertex.y, vertex.z], idx * 3);

                // 计算法线
                // 法线方向是从环面中心到顶点的方向
                center.x = radius * Math.cos(u); // 环面中心到当前点的投影x坐标
                center.y = radius * Math.sin(u); // 环面中心到当前点的投影y坐标
                normal.sub(vertex, center).normalize(); // 从中心指向顶点的归一化向量

                // 设置法线
                normals.set([normal.x, normal.y, normal.z], idx * 3);

                // 设置纹理坐标
                uvs.set([i / tubularSegments, j / radialSegments], idx * 2);
            }
        }

        // 生成索引（三角形）
        idx = 0;
        for (let j = 1; j <= radialSegments; j++) {
            for (let i = 1; i <= tubularSegments; i++, idx++) {
                // 计算四边形的四个顶点索引
                const a = (tubularSegments + 1) * j + i - 1; // 右上
                const b = (tubularSegments + 1) * (j - 1) + i - 1; // 右下
                const c = (tubularSegments + 1) * (j - 1) + i; // 左下
                const d = (tubularSegments + 1) * j + i; // 左上

                // 创建两个三角形（一个四边形）
                indices.set([a, b, d, b, c, d], idx * 6);
            }
        }

        // 设置几何体属性
        Object.assign(attributes, {
            position: { size: 3, data: vertices },
            normal: { size: 3, data: normals },
            uv: { size: 2, data: uvs },
            index: { data: indices },
        });

        // 调用父类构造函数
        super(gl, attributes);
    }
}
