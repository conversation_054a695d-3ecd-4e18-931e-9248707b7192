import { Geometry } from '../core/Geometry.js';
import { Vec3 } from '../math/Vec3.js';
import { Vec2 } from '../math/Vec2.js';

// 辅助变量，用于避免重复创建对象
const vertex = /* @__PURE__ */ new Vec3(); // 顶点位置
const normal = /* @__PURE__ */ new Vec3(); // 法线向量
const uv = /* @__PURE__ */ new Vec2(); // 纹理坐标
const point = /* @__PURE__ */ new Vec3(); // 路径上的点

/**
 * 管道几何体类
 * 沿着给定路径创建一个具有固定半径的管道
 */
export class Tube extends Geometry {
    /**
     * 创建一个管道几何体
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} [options] - 配置选项
     * @param {Path} options.path - 管道沿着的路径
     * @param {Number} [options.radius=1] - 管道半径
     * @param {Number} [options.tubularSegments=64] - 沿路径的分段数
     * @param {Number} [options.radialSegments=8] - 管道横截面的分段数
     * @param {Boolean} [options.closed=false] - 路径是否闭合
     * @param {Object} [options.attributes={}] - 自定义几何体属性
     */
    constructor(gl, { path, radius = 1, tubularSegments = 64, radialSegments = 8, closed = false, attributes = {} } = {}) {
        // 先调用父类构造函数，后面会添加属性
        super(gl, attributes);

        // 保存参数
        this.path = path; // 管道沿着的路径
        this.radius = radius; // 管道半径
        this.tubularSegments = tubularSegments; // 沿路径的分段数
        this.radialSegments = radialSegments; // 管道横截面的分段数
        this.closed = closed; // 路径是否闭合

        // 计算Frenet坐标系（切线、法线和副法线）
        // 用于确定管道在每个点的方向和旋转
        this.frenetFrames = path.computeFrenetFrames(tubularSegments, closed);

        // 计算顶点和索引数量
        const numVertices = (tubularSegments + 1) * (radialSegments + 1); // 顶点总数
        const numIndices = tubularSegments * radialSegments * 6; // 索引总数

        // 创建数据数组
        this.positions = new Float32Array(numVertices * 3); // 顶点位置
        this.normals = new Float32Array(numVertices * 3); // 顶点法线
        this.uvs = new Float32Array(numVertices * 2); // 纹理坐标
        // 根据顶点数选择合适的索引类型
        this.indices = numVertices > 65536 ? new Uint32Array(numIndices) : new Uint16Array(numIndices);

        // 生成几何体数据
        this._generateAttributes(); // 生成顶点、法线和纹理坐标
        this._generateIndices(); // 生成索引

        // 添加属性到几何体
        this.addAttribute('position', { size: 3, data: this.positions });
        this.addAttribute('normal', { size: 3, data: this.normals });
        this.addAttribute('uv', { size: 2, data: this.uvs });
        this.setIndex({ data: this.indices });
    }

    /**
     * 生成顶点、法线和纹理坐标
     * @private
     */
    _generateAttributes() {
        for (let i = 0; i <= this.tubularSegments; i++) {
            let ci = i;
            if (i === this.tubularSegments) {
                // 如果是最后一个分段：
                // 如果几何体不是闭合的，在给定路径上的常规位置生成最后一行顶点和法线
                // 如果几何体是闭合的，复制第一行顶点和法线（纹理坐标会不同）
                ci = this.closed ? 0 : this.tubularSegments;
            }

            // 获取路径上的点
            this.path.getPointAt(ci / this.tubularSegments, point);

            // 获取对应的法线和副法线
            const N = this.frenetFrames.normals[ci]; // 法线
            const B = this.frenetFrames.binormals[ci]; // 副法线

            // 为当前分段生成法线和顶点
            for (let j = 0; j <= this.radialSegments; j++) {
                // 计算围绕管道的角度 [0, 2π]
                const v = (j / this.radialSegments) * Math.PI * 2;
                const sin = Math.sin(v);
                const cos = -Math.cos(v);

                // 计算当前顶点的索引
                const idx = i * (this.radialSegments + 1) + j;

                // 计算法线向量
                // 通过法线和副法线的线性组合创建圆形横截面
                normal.x = cos * N.x + sin * B.x;
                normal.y = cos * N.y + sin * B.y;
                normal.z = cos * N.z + sin * B.z;
                // normal.normalize(); // 注释掉的归一化，可能不需要因为N和B已经是单位向量
                this.normals.set(normal, idx * 3);

                // 计算顶点位置
                // 顶点 = 路径点 + 半径 * 法线方向
                vertex.x = point.x + this.radius * normal.x;
                vertex.y = point.y + this.radius * normal.y;
                vertex.z = point.z + this.radius * normal.z;
                this.positions.set(vertex, idx * 3);

                // 计算纹理坐标
                uv.x = i / this.tubularSegments; // u坐标：沿管道长度 [0,1]
                uv.y = j / this.radialSegments; // v坐标：围绕管道周长 [0,1]
                this.uvs.set(uv, idx * 2);
            }
        }
    }

    /**
     * 生成索引（三角形）
     * @private
     */
    _generateIndices() {
        for (let j = 1; j <= this.tubularSegments; j++) {
            for (let i = 1; i <= this.radialSegments; i++) {
                // 计算四边形的四个顶点索引
                const a = (this.radialSegments + 1) * (j - 1) + (i - 1); // 左上
                const b = (this.radialSegments + 1) * j + (i - 1); // 左下
                const c = (this.radialSegments + 1) * j + i; // 右下
                const d = (this.radialSegments + 1) * (j - 1) + i; // 右上

                // 计算索引在数组中的位置
                const idx = (j - 1) * this.radialSegments + (i - 1);

                // 创建两个三角形（一个四边形）
                // 三角形1：a-b-d，三角形2：b-c-d
                this.indices.set([a, b, d, b, c, d], idx * 6);
            }
        }
    }
}
