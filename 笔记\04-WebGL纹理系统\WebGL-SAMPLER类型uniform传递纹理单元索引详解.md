# WebGL SAMPLER 类型 uniform 传递纹理单元索引详解

## 核心问题确认

您选中的代码片段：

```javascript
case 35678: // SAMPLER_2D - 2D纹理采样器
case 36306: // U_SAMPLER_2D - 无符号2D纹理采样器
case 35680: // SAMPLER_CUBE - 立方体纹理采样器
case 36289: // SAMPLER_2D_ARRAY - 2D纹理数组采样器
```

**您的理解完全正确！** 这些 SAMPLER 类型的 uniform 传递的都是**纹理单元索引**，而不是纹理数据本身。

## 纹理系统工作机制

### 1. 纹理绑定与采样器的关系

```
纹理系统三层架构：
┌─────────────────────────────────────┐
│ 着色器层 (GLSL)                      │
├─────────────────────────────────────┤
│ uniform sampler2D u_texture;        │ ← 采样器变量
│ gl_FragColor = texture2D(u_texture, │   (接收纹理单元索引)
│                         vUv);       │
└─────────────────────────────────────┘
                    ↑
                    │ 纹理单元索引 (0, 1, 2...)
                    │
┌─────────────────────────────────────┐
│ WebGL API 层                        │
├─────────────────────────────────────┤
│ gl.uniform1i(location, 0);          │ ← 传递索引值
│ gl.uniform1i(location, 1);          │
└─────────────────────────────────────┘
                    ↑
                    │ 纹理单元管理
                    │
┌─────────────────────────────────────┐
│ 纹理单元层                           │
├─────────────────────────────────────┤
│ GL_TEXTURE0 ← 纹理对象A              │
│ GL_TEXTURE1 ← 纹理对象B              │
│ GL_TEXTURE2 ← 纹理对象C              │
└─────────────────────────────────────┘
```

### 2. setUniform 中的处理逻辑

```javascript
// 在 setUniform 函数中，SAMPLER 类型的处理
switch (type) {
    case 35678: // SAMPLER_2D
    case 36306: // U_SAMPLER_2D
    case 35680: // SAMPLER_CUBE
    case 36289: // SAMPLER_2D_ARRAY
        // 🔍 关键：这里传递的 value 是纹理单元索引，不是纹理对象
        return gl.uniform1i(location, value);
    //     ↑ uniform1i 专门用于设置整数值
    //       ↑ value 是数字：0, 1, 2, 3... (纹理单元索引)
}
```

## 完整的纹理使用流程

### 1. 单个纹理的处理流程

```javascript
// JavaScript 端：定义纹理 uniform
const program = new Program(gl, {
    vertex: vertexShader,
    fragment: `
        uniform sampler2D u_diffuseTexture;  // 着色器中的采样器
        
        void main() {
            vec4 color = texture2D(u_diffuseTexture, vUv);
            gl_FragColor = color;
        }
    `,
    uniforms: {
        u_diffuseTexture: { value: textureObject }, // 传入纹理对象
    },
});

// Program.js 内部处理流程：
// 1. 检测到 uniform.value.texture 存在
if (uniform.value.texture) {
    textureUnit = textureUnit + 1; // 分配纹理单元：textureUnit = 1

    // 2. 绑定纹理到纹理单元
    uniform.value.update(textureUnit);
    // 内部执行：
    // gl.activeTexture(gl.TEXTURE0 + 1);  // 激活 GL_TEXTURE1
    // gl.bindTexture(gl.TEXTURE_2D, textureObject.texture);

    // 3. 设置采样器 uniform 指向纹理单元索引
    return setUniform(this.gl, activeUniform.type, location, textureUnit);
    // 内部执行：
    // gl.uniform1i(location, 1);  // 告诉着色器从 TEXTURE1 采样
}
```

### 2. 纹理数组的处理流程

```javascript
// JavaScript 端：定义纹理数组
const program = new Program(gl, {
    fragment: `
        uniform sampler2D u_textures[3];  // 纹理数组采样器
        uniform int u_textureIndex;
        
        void main() {
            vec4 color;
            // 根据索引选择纹理
            if(u_textureIndex == 0) color = texture2D(u_textures[0], vUv);
            else if(u_textureIndex == 1) color = texture2D(u_textures[1], vUv);
            else color = texture2D(u_textures[2], vUv);
            
            gl_FragColor = color;
        }
    `,
    uniforms: {
        u_textures: { value: [texture1, texture2, texture3] }, // 纹理对象数组
    },
});

// Program.js 内部处理流程：
// 1. 检测到纹理数组
if (uniform.value.length && uniform.value[0].texture) {
    const textureUnits = []; // 存储纹理单元索引数组

    // 2. 为每个纹理分配纹理单元
    uniform.value.forEach((textureObject) => {
        textureUnit = textureUnit + 1; // 递增：1, 2, 3
        textureObject.update(textureUnit); // 绑定到对应纹理单元
        textureUnits.push(textureUnit); // 记录索引：[1, 2, 3]
    });

    // 3. 设置采样器数组 uniform
    return setUniform(this.gl, activeUniform.type, location, textureUnits);
    // 内部执行：
    // gl.uniform1iv(location, [1, 2, 3]);  // 告诉着色器数组对应的纹理单元
}
```

## 实际应用示例

### 1. 多纹理材质系统

```javascript
// 复杂材质需要多个纹理
const materialProgram = new Program(gl, {
    fragment: `
        uniform sampler2D u_diffuseMap;    // 漫反射贴图
        uniform sampler2D u_normalMap;     // 法线贴图
        uniform sampler2D u_roughnessMap;  // 粗糙度贴图
        uniform sampler2D u_metallicMap;   // 金属度贴图
        
        void main() {
            vec4 diffuse = texture2D(u_diffuseMap, vUv);
            vec3 normal = texture2D(u_normalMap, vUv).rgb;
            float roughness = texture2D(u_roughnessMap, vUv).r;
            float metallic = texture2D(u_metallicMap, vUv).r;
            
            // PBR 计算...
        }
    `,
    uniforms: {
        u_diffuseMap: { value: diffuseTexture }, // 纹理对象
        u_normalMap: { value: normalTexture }, // 纹理对象
        u_roughnessMap: { value: roughnessTexture }, // 纹理对象
        u_metallicMap: { value: metallicTexture }, // 纹理对象
    },
});

// 内部执行结果：
// u_diffuseMap   -> gl.uniform1i(location, 1)  // 指向 GL_TEXTURE1
// u_normalMap    -> gl.uniform1i(location, 2)  // 指向 GL_TEXTURE2
// u_roughnessMap -> gl.uniform1i(location, 3)  // 指向 GL_TEXTURE3
// u_metallicMap  -> gl.uniform1i(location, 4)  // 指向 GL_TEXTURE4
```

### 2. 立方体纹理环境映射

```javascript
// 环境映射使用立方体纹理
const envProgram = new Program(gl, {
    fragment: `
        uniform samplerCube u_envMap;      // 立方体纹理采样器
        uniform sampler2D u_baseTexture;   // 基础纹理
        
        void main() {
            vec4 baseColor = texture2D(u_baseTexture, vUv);
            vec3 envColor = textureCube(u_envMap, vReflectDir).rgb;
            
            gl_FragColor = vec4(mix(baseColor.rgb, envColor, 0.3), 1.0);
        }
    `,
    uniforms: {
        u_envMap: { value: cubeTexture }, // 立方体纹理对象
        u_baseTexture: { value: baseTexture }, // 2D纹理对象
    },
});

// 内部执行结果：
// u_envMap      -> gl.uniform1i(location, 1)  // 指向 GL_TEXTURE1 (CUBE_MAP)
// u_baseTexture -> gl.uniform1i(location, 2)  // 指向 GL_TEXTURE2 (TEXTURE_2D)
```

### 3. 纹理数组动态选择

```javascript
// 地形系统使用纹理数组
const terrainProgram = new Program(gl, {
    fragment: `
        uniform sampler2D u_terrainTextures[4];  // 地形纹理数组
        uniform sampler2D u_blendMap;            // 混合权重贴图
        
        void main() {
            vec4 blendWeights = texture2D(u_blendMap, vUv);
            
            vec4 grass = texture2D(u_terrainTextures[0], vUv * 16.0);
            vec4 dirt = texture2D(u_terrainTextures[1], vUv * 16.0);
            vec4 rock = texture2D(u_terrainTextures[2], vUv * 16.0);
            vec4 snow = texture2D(u_terrainTextures[3], vUv * 16.0);
            
            gl_FragColor = grass * blendWeights.r + 
                          dirt * blendWeights.g + 
                          rock * blendWeights.b + 
                          snow * blendWeights.a;
        }
    `,
    uniforms: {
        u_terrainTextures: { value: [grassTex, dirtTex, rockTex, snowTex] },
        u_blendMap: { value: blendTexture },
    },
});

// 内部执行结果：
// u_terrainTextures -> gl.uniform1iv(location, [1, 2, 3, 4])  // 数组索引
// u_blendMap        -> gl.uniform1i(location, 5)              // 单个索引
```

## 技术细节深入分析

### 1. 为什么传递索引而不是纹理对象？

#### GPU 架构限制

```
GPU 纹理系统架构：
┌─────────────────────────────────────┐
│ 着色器执行单元 (Shader Cores)        │
├─────────────────────────────────────┤
│ • 只能通过纹理单元访问纹理            │
│ • 无法直接操作纹理对象               │
│ • 需要纹理单元索引进行寻址            │
└─────────────────────────────────────┘
                    ↑
                    │ 纹理单元索引
                    │
┌─────────────────────────────────────┐
│ 纹理单元 (Texture Units)            │
├─────────────────────────────────────┤
│ TEXTURE0: [TEXTURE_2D] [CUBE_MAP]   │
│ TEXTURE1: [TEXTURE_2D] [CUBE_MAP]   │
│ TEXTURE2: [TEXTURE_2D] [CUBE_MAP]   │
│ ...                                 │
└─────────────────────────────────────┘
                    ↑
                    │ 纹理绑定
                    │
┌─────────────────────────────────────┐
│ 纹理对象 (Texture Objects)          │
├─────────────────────────────────────┤
│ • 存储在 GPU 内存中                  │
│ • 包含像素数据和参数                 │
│ • 通过绑定操作连接到纹理单元          │
└─────────────────────────────────────┘
```

#### 性能考虑

```javascript
// ❌ 如果直接传递纹理对象（假设可行）
// 每次采样都需要：
// 1. 解析纹理对象指针
// 2. 查找纹理数据位置
// 3. 执行内存访问
// 开销：高延迟，复杂寻址

// ✅ 传递纹理单元索引（实际实现）
// 每次采样只需要：
// 1. 直接索引到预绑定的纹理单元
// 2. 快速访问纹理数据
// 开销：低延迟，简单寻址
```

### 2. 纹理单元索引的数据类型

```javascript
// setUniform 中的类型处理
switch (type) {
    case 35678: // SAMPLER_2D
        // 🔍 关键：使用 uniform1i 设置整数值
        return gl.uniform1i(location, value);
        //     ↑ 'i' 表示 integer (整数)
        //       ↑ value 必须是整数：0, 1, 2, 3...

    case 36289: // SAMPLER_2D_ARRAY
        // 🔍 数组情况：使用 uniform1iv 设置整数数组
        return gl.uniform1iv(location, value);
        //     ↑ 'iv' 表示 integer vector (整数向量)
        //        ↑ value 必须是整数数组：[0, 1, 2, 3]
}

// 对比其他类型：
case 5126: // FLOAT
    return gl.uniform1f(location, value);  // 'f' = float
case 35665: // FLOAT_VEC3
    return gl.uniform3fv(location, value); // 'fv' = float vector
```

### 3. 纹理单元分配策略

```javascript
// Program.js 中的纹理单元分配逻辑
let textureUnit = 0; // 从0开始计数

// 单个纹理处理
if (uniform.value.texture) {
    textureUnit = textureUnit + 1; // 递增分配：1, 2, 3...
    uniform.value.update(textureUnit);
    return setUniform(this.gl, activeUniform.type, location, textureUnit);
}

// 纹理数组处理
if (uniform.value.length && uniform.value[0].texture) {
    const textureUnits = [];
    uniform.value.forEach((textureObject) => {
        textureUnit = textureUnit + 1; // 继续递增：4, 5, 6...
        textureObject.update(textureUnit);
        textureUnits.push(textureUnit);
    });
    return setUniform(this.gl, activeUniform.type, location, textureUnits);
}

// 分配结果示例：
// 第1个纹理 -> TEXTURE1 (索引1)
// 第2个纹理 -> TEXTURE2 (索引2)
// 第3个纹理 -> TEXTURE3 (索引3)
// 纹理数组[0] -> TEXTURE4 (索引4)
// 纹理数组[1] -> TEXTURE5 (索引5)
// 纹理数组[2] -> TEXTURE6 (索引6)
```

### 4. 常见错误和调试技巧

#### 错误 1：纹理单元索引错误

```javascript
// ❌ 错误：手动设置错误的索引
program.uniforms.u_texture = { value: 5 }; // 直接传递数字

// ✅ 正确：传递纹理对象，让系统自动分配
program.uniforms.u_texture = { value: textureObject };
```

#### 错误 2：纹理单元数量超限

```javascript
// 检查设备支持的纹理单元数量
const maxTextureUnits = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);
console.log('最大纹理单元数:', maxTextureUnits); // 通常是16-32

// 避免超出限制
if (textureCount > maxTextureUnits) {
    console.warn(`纹理数量 ${textureCount} 超出设备限制 ${maxTextureUnits}`);
}
```

#### 调试技巧：纹理单元状态查看

```javascript
// 调试工具：查看当前纹理单元绑定状态
function debugTextureUnits(gl, maxUnits = 8) {
    console.log('🔍 纹理单元绑定状态:');

    for (let i = 0; i < maxUnits; i++) {
        gl.activeTexture(gl.TEXTURE0 + i);

        const texture2D = gl.getParameter(gl.TEXTURE_BINDING_2D);
        const textureCube = gl.getParameter(gl.TEXTURE_BINDING_CUBE_MAP);

        console.log(`TEXTURE${i}:`, {
            '2D': texture2D ? `绑定` : '空闲',
            CUBE: textureCube ? `绑定` : '空闲',
        });
    }
}

// 使用示例
debugTextureUnits(gl);
// 输出：
// TEXTURE0: { 2D: '空闲', CUBE: '空闲' }
// TEXTURE1: { 2D: '绑定', CUBE: '空闲' }
// TEXTURE2: { 2D: '绑定', CUBE: '空闲' }
// ...
```

## 总结

SAMPLER 类型 uniform 传递纹理单元索引的设计体现了 WebGL 的核心理念：

1. **性能优先**：通过索引访问预绑定的纹理，避免运行时查找开销
2. **硬件适配**：符合 GPU 纹理单元的硬件架构设计
3. **资源管理**：统一的纹理单元分配和管理机制
4. **类型安全**：使用整数类型确保索引的准确性

这种设计让 WebGL 能够高效地处理多纹理渲染，是现代 GPU 图形管线的基础。
